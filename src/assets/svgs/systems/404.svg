<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 96 96">
  <defs>
    <style>
      .cls-1 {
        fill: #f2f2f2;
      }

      .cls-2 {
        fill: #033884;
      }

      .cls-3 {
        fill: #0047bb;
      }

      .cls-4, .cls-5 {
        fill: #fff;
      }

      .cls-6 {
        fill: #1c82ba;
      }

      .cls-7 {
        fill: #9acdf7;
      }

      .cls-8 {
        fill: url(#_未命名的渐变_60);
      }

      .cls-9 {
        fill: #e0dede;
      }

      .cls-9, .cls-5, .cls-10, .cls-11, .cls-12 {
        stroke-linecap: round;
        stroke-linejoin: round;
      }

      .cls-9, .cls-5, .cls-11 {
        stroke: #000;
        stroke-width: .5px;
      }

      .cls-10 {
        stroke: #1c82ba;
      }

      .cls-10, .cls-11, .cls-12 {
        fill: none;
      }

      .cls-10, .cls-12 {
        stroke-width: .25px;
      }

      .cls-12 {
        stroke: #9acdf7;
      }

      .cls-13 {
        fill: url(#_未命名的渐变_60-2);
      }
    </style>
    <linearGradient id="_未命名的渐变_60" data-name="未命名的渐变 60" x1="1.76" y1="45.4" x2="31.19" y2="45.48" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#0047bb"/>
      <stop offset=".87" stop-color="#cce0f4"/>
      <stop offset=".93" stop-color="#d1deea"/>
      <stop offset=".98" stop-color="#dbdbdb"/>
    </linearGradient>
    <linearGradient id="_未命名的渐变_60-2" data-name="未命名的渐变 60" x1="65.76" y1="43.4" x2="95.19" y2="43.48" xlink:href="#_未命名的渐变_60"/>
  </defs>
  <polygon class="cls-8" points="23.91 47.54 23.91 42.05 17.67 42.05 17.67 47.37 12.97 47.37 20.23 32.42 13.83 32.42 5.86 48.22 6.14 52.92 17.67 52.92 17.67 58.48 24.08 58.48 24.03 52.91 27.23 52.91 27.23 47.54 23.91 47.54"/>
  <polygon class="cls-13" points="87.91 45.54 87.91 40.05 81.67 40.05 81.67 45.37 76.97 45.37 84.23 30.42 77.83 30.42 69.86 46.22 70.14 50.92 81.67 50.92 81.67 56.48 88.08 56.48 88.03 50.91 91.23 50.91 91.23 45.54 87.91 45.54"/>
  <path class="cls-5" d="M30.98,45.79c0,9.67,7.84,17.51,17.51,17.51s17.51-7.84,17.51-17.51c0-9.67-7.84-17.51-17.51-17.51-2.17,0-4.25,.39-6.17,1.12-2.73,1.03-5.14,2.72-7.02,4.88-2.69,3.08-4.32,7.11-4.32,11.52Z"/>
  <path class="cls-5" d="M33.01,30.92h0s2.29,3.36,2.29,3.36c1.88-2.16,4.29-3.85,7.02-4.88l-2.3-3.12h0s0-.01,0-.01c-1.25,.08-4.81,.65-6.97,4.57-.01,.02-.03,.05-.04,.07Z"/>
  <line class="cls-11" x1="32.97" y1="30.86" x2="33.01" y2="30.92"/>
  <path class="cls-5" d="M29.22,27.13l1.95,2.85c2.15-5.08,6.9-5.68,9.01-5.68l-1.9-3.1c-.31-.5-.85-.82-1.44-.85-5.14-.25-7.14,3.26-7.83,5.04-.22,.58-.15,1.23,.2,1.74Z"/>
  <path class="cls-9" d="M31.16,29.98l.07,.1c.4,.58,1.08,.86,1.74,.77,.03,0,.05,0,.08-.01,2.16-3.92,5.73-4.5,6.97-4.57h0s.02,0,.02,0c.57-.52,.6-1.16,.17-1.91l-.03-.06c-2.11,0-6.86,.59-9.01,5.68Z"/>
  <path class="cls-11" d="M40.03,26.28l.02-.02"/>
  <g>
    <g>
      <circle cx="53.55" cy="46.17" r="3.35"/>
      <circle cx="43.18" cy="46.17" r="3.35"/>
    </g>
    <g>
      <path class="cls-3" d="M33.26,48s.02,.09,.02,.14h0c2.1,12.88,15.53,12.5,15.53,12.5,14.41,0,14.92-13.02,14.92-13.02-.01,0-.03,.02-.04,.03-1.35,.86-1.63,.85-4.51-.15-1.94-.67-4.5-.28-5.98,.06-.73,.17-1.49,.17-2.23,.03-1.8-.34-3.82,.14-5.43,.72-1.64,.59-3.44,.45-5-.34-1.76-.88-3.1-.35-3.1-.35-2.65,1.77-4.17,.38-4.17,.38Z"/>
      <path class="cls-2" d="M33.28,48.15s-.02-.09-.02-.14c0,0,1.52,1.39,4.17-.38,0,0,1.34-.54,3.1,.35,1.56,.79,3.36,.93,5,.34,1.6-.58,3.63-1.06,5.43-.72,.74,.14,1.5,.13,2.23-.03,1.49-.34,4.05-.73,5.98-.06,2.88,1,3.16,1.01,4.51,.15,.2-.35,.37-.8-1.03-1.55-.89-.48-1.9-.67-2.9-.59-.64,.05-1.51,.1-2.44,.13h0c-1.05,.03-2.18,.03-3.2-.05-.5-.04-.96-.09-1.38-.17-.49-.09-.91-.22-1.21-.39,0,0-.16-.06-.45-.14h0c-.75-.2-2.39-.49-4.41,0h0c-.53,.13-1.08,.31-1.66,.57-.14,.06-.29,.13-.44,.21,0,0-.36,.04-.92,.08-.8,.05-2.02,.08-3.24-.06-.79-.09-1.58-.25-2.25-.52-.99-.4-2.09-.5-3.12-.19-1.3,.39-2.56,1.26-1.77,3.17Z"/>
    </g>
  </g>
  <ellipse class="cls-7" cx="53.17" cy="31.32" rx=".51" ry="1.71" transform="translate(5.91 70.99) rotate(-70.51)"/>
  <path class="cls-7" d="M62.98,39.28c-.38-1.09-1.66-4.1-4.74-5.99-.15-.09-.32-.14-.49-.16-.28-.02-.67,.02-.84,.36-.14,.27-.04,.6,.2,.78,.61,.47,2.2,1.92,4.23,5.46,.07,.13,.18,.24,.3,.32,.21,.14,.54,.29,.87,.2,.41-.12,.61-.58,.47-.98Z"/>
  <g>
    <circle class="cls-6" cx="37.53" cy="40.38" r="1.01"/>
    <circle class="cls-6" cx="35.19" cy="39.61" r=".57"/>
    <circle class="cls-6" cx="37.85" cy="38.35" r=".57"/>
    <circle class="cls-6" cx="36.58" cy="42.27" r=".57"/>
    <circle class="cls-6" cx="40.25" cy="41.13" r=".57"/>
    <line class="cls-10" x1="36.8" y1="41.89" x2="37.17" y2="41.24"/>
    <line class="cls-10" x1="36.61" y1="40.23" x2="35.63" y2="39.85"/>
    <line class="cls-10" x1="37.68" y1="40.38" x2="37.74" y2="38.83"/>
    <line class="cls-10" x1="39.84" y1="40.89" x2="38.45" y2="40.63"/>
  </g>
  <g>
    <circle class="cls-7" cx="56.71" cy="55.53" r=".88"/>
    <circle class="cls-7" cx="55.32" cy="53.76" r=".76"/>
    <circle class="cls-7" cx="57.34" cy="53.12" r=".63"/>
    <line class="cls-12" x1="55.65" y1="54.38" x2="56.15" y2="54.88"/>
    <line class="cls-12" x1="55.8" y1="53.5" x2="56.82" y2="53.25"/>
  </g>
  <path class="cls-11" d="M33.7,22.28s-2.28,.63-3.29,3.67"/>
  <line class="cls-11" x1="34.08" y1="30.87" x2="35.34" y2="32.52"/>
  <path class="cls-4" d="M38.25,40.23c-.35-.37-.64-.46-1.14-.51,.41-.36,1.16-.04,1.14,.51h0Z"/>
  <circle class="cls-4" cx="40.34" cy="41.05" r=".13"/>
  <circle class="cls-4" cx="57.4" cy="52.93" r=".13"/>
  <circle class="cls-4" cx="55.25" cy="53.44" r=".13"/>
  <circle class="cls-4" cx="56.14" cy="55.84" r=".13"/>
  <circle class="cls-4" cx="37.93" cy="38.14" r=".13"/>
  <circle class="cls-4" cx="34.9" cy="39.41" r=".13"/>
  <circle class="cls-4" cx="36.48" cy="42.38" r=".06"/>
  <ellipse class="cls-1" cx="49" cy="71" rx="8" ry="2"/>
</svg>