<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" viewBox="2 -1 26 26">
  <defs>
    <style>
      .cls-1 {
        stroke-linejoin: round;
      }

      .cls-1, .cls-2 {
        fill: none;
        stroke: #000;
        stroke-linecap: round;
      }

      .cls-2 {
        stroke-miterlimit: 10;
      }
    </style>
  </defs>
  <rect class="cls-2" x="3.5" y="3.5" width="17" height="17" rx="1.99" ry="1.99"/>
  <rect class="cls-1" x="6.5" y="6.5" width="4" height="9" rx=".51" ry=".51"/>
  <rect class="cls-1" x="13.5" y="6.5" width="4" height="5" rx=".51" ry=".51"/>
</svg>