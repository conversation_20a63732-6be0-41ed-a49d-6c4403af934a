<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 270 257" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组备份 11</title>
    <defs>
        <linearGradient x1="30.3301122%" y1="16.4799058%" x2="92.5866176%" y2="69.139566%" id="linearGradient-1">
            <stop stop-color="#6ED8FF" offset="0%"></stop>
            <stop stop-color="#0D4BBA" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="logo-原版" transform="translate(-1537.000000, -1118.000000)">
            <g id="编组备份-11" transform="translate(1537.898211, 1118.215598)">
                <path d="M132.101789,19.7844015 C187.330264,19.7844015 232.101789,64.5559265 232.101789,119.784402 C232.101789,175.012876 187.330264,219.784402 132.101789,219.784402 C76.8733141,219.784402 32.1017891,175.012876 32.1017891,119.784402 C32.1017891,64.5559265 76.8733141,19.7844015 132.101789,19.7844015 Z M132.101789,55.7844015 C96.7555651,55.7844015 68.1017891,84.4381775 68.1017891,119.784402 C68.1017891,155.130625 96.7555651,183.784402 132.101789,183.784402 C167.448013,183.784402 196.101789,155.130625 196.101789,119.784402 C196.101789,84.4381775 167.448013,55.7844015 132.101789,55.7844015 Z" id="形状备份-2" fill="#4F84E6"></path>
                <path d="M167.848348,181.69699 L167.202942,181.916468 C146.4408,188.88129 122.999866,187.542774 102.335843,176.438948 C111.226996,181.121226 121.355008,183.770711 132.101789,183.770711 C167.088709,183.770711 195.517456,155.689489 196.082532,120.834322 L196.091104,119.776053 C196.091104,74.4924846 159.384583,37.7828989 114.104794,37.7828989 C105.048836,37.7828989 96.3358093,39.2512823 88.190779,41.9629643 L87.345304,42.2497412 C55.9283561,53.0932943 33.1618777,82.49968 32.1533808,117.360499 L32.129282,118.415767 L32.1318788,118.122509 C33.0148104,63.66131 77.4347269,19.7844015 132.101789,19.7844015 C150.333933,19.7844015 167.426281,24.6648531 182.143888,33.1907081 L182.135638,33.2051233 L183.262101,33.8682091 C221.571992,56.8243587 234.51601,106.360921 212.102514,145.185475 C201.8223,162.992815 185.871575,175.484708 167.848348,181.69699 Z" id="路径" fill="url(#linearGradient-1)"></path>
                <path d="M189.123171,203.002624 L188.477765,203.222102 C167.715623,210.186924 144.274689,208.848408 123.610666,197.744581 C132.501819,202.42686 142.629831,205.076344 153.376612,205.076344 C188.363532,205.076344 216.792279,176.995123 217.357355,142.139956 L217.365927,141.081687 C217.365927,95.7981184 180.659406,59.0885327 135.379617,59.0885327 C126.323659,59.0885327 117.610632,60.5569161 109.465602,63.2685981 L108.620127,63.555375 C77.2031791,74.3989281 54.4367007,103.805314 53.4282038,138.666133 L53.4041051,139.721401 L53.4067018,139.428142 C54.2896334,84.9669438 98.7095499,41.0900353 153.376612,41.0900353 C171.608756,41.0900353 188.701104,45.9704869 203.418711,54.4963419 L203.410461,54.510757 L204.536924,55.1738429 C242.846815,78.1299925 255.790833,127.666555 233.377337,166.491109 C223.097123,184.298449 207.146398,196.790342 189.123171,203.002624 Z" id="路径" fill="url(#linearGradient-1)" transform="translate(148.889607, 124.295706) rotate(120.000000) translate(-148.889607, -124.295706) "></path>
                <path d="M160.034539,210.774344 L159.389134,210.993823 C138.626992,217.958644 115.186057,216.620128 94.522034,205.516302 C103.413187,210.19858 113.5412,212.848065 124.287981,212.848065 C159.2749,212.848065 187.703647,184.766843 188.268723,149.911676 L188.277296,148.853408 C188.277296,103.569839 151.570774,66.8602529 106.290986,66.8602529 C97.2350278,66.8602529 88.5220007,68.3286364 80.3769704,71.0403184 L79.5314954,71.3270952 C48.1145475,82.1706483 25.3480691,111.577034 24.3395721,146.437853 L24.3154734,147.493121 L24.3180701,147.199863 C25.2010018,92.7386641 69.6209183,48.8617556 124.287981,48.8617556 C142.520124,48.8617556 159.612472,53.7422072 174.33008,62.2680621 L174.32183,62.2824773 L175.448293,62.9455632 C213.758183,85.9017128 226.702201,135.438275 204.288705,174.262829 C194.008491,192.070169 178.057766,204.562062 160.034539,210.774344 Z" id="路径" fill="url(#linearGradient-1)" transform="translate(119.800976, 132.067426) rotate(240.000000) translate(-119.800976, -132.067426) "></path>
            </g>
        </g>
    </g>
</svg>