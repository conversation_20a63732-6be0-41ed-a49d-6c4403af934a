<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="-29.88559%" x2="50%" y2="44.0501254%" id="linearGradient-1">
            <stop stop-color="#CFDFFA" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="33.7484637%" x2="53.6226986%" y2="69.8229087%" id="linearGradient-2">
            <stop stop-color="#D6E8FF" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="36.47456%" x2="53.6226986%" y2="66.4977364%" id="linearGradient-3">
            <stop stop-color="#D6E8FF" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="57.28025%" y2="100%" id="linearGradient-4">
            <stop stop-color="#BFD4FA" offset="0%"></stop>
            <stop stop-color="#5792F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="51.9400923%" y2="131.881015%" id="linearGradient-5">
            <stop stop-color="#BFD4FA" offset="0%"></stop>
            <stop stop-color="#5792F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="13.0877007%" x2="74.282792%" y2="128.49897%" id="linearGradient-6">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="54.0733641%" y2="131.881015%" id="linearGradient-7">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="51.979585%" y2="131.881015%" id="linearGradient-8">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="55.3644123%" y2="131.881015%" id="linearGradient-9">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="48.2590898%" x2="74.2827921%" y2="53.7022797%" id="linearGradient-10">
            <stop stop-color="#C5E0FF" offset="0%"></stop>
            <stop stop-color="#59B4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="54.6593842%" y1="-91.6326561%" x2="51.9965305%" y2="93.8707843%" id="linearGradient-11">
            <stop stop-color="#5792F0" offset="0%"></stop>
            <stop stop-color="#BFD4FA" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="90.669778%" y1="58.9183998%" x2="8.73579937%" y2="-11.5290622%" id="linearGradient-12">
            <stop stop-color="#5FABFF" offset="0%"></stop>
            <stop stop-color="#BFD4FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="83.3610401%" y1="100%" x2="21.2495227%" y2="3.90977606%" id="linearGradient-13">
            <stop stop-color="#9CC9FF" offset="0%"></stop>
            <stop stop-color="#E2EFFF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="219.196164%" y1="50.7767422%" x2="-17.9385803%" y2="49.653899%" id="linearGradient-14">
            <stop stop-color="#70B6FF" offset="0%"></stop>
            <stop stop-color="#BFD9FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="219.196164%" y1="53.1069688%" x2="-17.9385803%" y2="48.615596%" id="linearGradient-15">
            <stop stop-color="#70B6FF" offset="0%"></stop>
            <stop stop-color="#BFD9FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="219.196164%" y1="188.087506%" x2="-17.9385803%" y2="-11.5290622%" id="linearGradient-16">
            <stop stop-color="#5792F0" offset="0%"></stop>
            <stop stop-color="#BFD4FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="219.196164%" y1="188.087506%" x2="-17.9385803%" y2="-11.5290622%" id="linearGradient-17">
            <stop stop-color="#5792F0" offset="0%"></stop>
            <stop stop-color="#BFD4FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="116.959906%" y1="58.9183998%" x2="-17.9385803%" y2="-11.5290622%" id="linearGradient-18">
            <stop stop-color="#5792F0" offset="0%"></stop>
            <stop stop-color="#BFD4FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="262.227292%" y1="247.538445%" x2="6.39235388%" y2="3.81708402%" id="linearGradient-19">
            <stop stop-color="#7DAFFF" offset="0%"></stop>
            <stop stop-color="#BFDBFA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="-19.0038784%" x2="50%" y2="100%" id="linearGradient-20">
            <stop stop-color="#FFCDA5" offset="0%"></stop>
            <stop stop-color="#FFE8D1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-21">
            <stop stop-color="#FFF2DF" offset="0%"></stop>
            <stop stop-color="#FEE0BC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="14.3474826%" y1="11.7098971%" x2="100%" y2="73.6326896%" id="linearGradient-22">
            <stop stop-color="#BFD4FA" offset="0%"></stop>
            <stop stop-color="#5792F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="43.5046289%" y1="-31.010887%" x2="50%" y2="93.1271749%" id="linearGradient-23">
            <stop stop-color="#D0E6FF" offset="0%"></stop>
            <stop stop-color="#5DA6FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-24">
            <stop stop-color="#FEE127" offset="0%"></stop>
            <stop stop-color="#F9AB21" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="插画/路线-ai-暂无内容" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="背景" transform="translate(6.250000, 24.101562)" fill-rule="nonzero">
            <path d="M96.75,164.898438 C148.526695,164.898438 187.5,178.576404 187.5,151.609375 C187.5,124.642346 145.526695,102.78125 93.75,102.78125 C41.9733047,102.78125 0,124.642346 0,151.609375 C0,178.576404 44.9733047,164.898438 96.75,164.898438 Z" id="椭圆形" fill="url(#linearGradient-1)"></path>
            <path d="M158.984375,24.6976799 C158.984375,27.5089963 156.285511,29.7580492 152.876421,29.7580492 L133.984375,29.7580492 C130.575284,29.5331439 127.734375,27.3965436 127.734375,24.4727746 C127.734375,22.5610795 129.012784,20.8742898 130.859375,19.9746686 C130.717329,19.6373106 130.575284,19.2999527 130.575284,18.9625947 C130.575284,17.0508996 132.421875,15.3641098 134.836648,15.3641098 C135.830966,15.3641098 136.683239,15.7014678 137.393466,16.1512784 C139.098011,13.5648674 142.223011,11.765625 145.916193,11.765625 C151.029829,11.765625 155.149148,15.2516572 155.149148,19.5248579 L155.149148,19.9746686 C157.421875,20.7618371 158.984375,22.5610795 158.984375,24.6976799 Z" id="云" fill="url(#linearGradient-2)"></path>
            <path d="M31.4652777,0 C36.2841568,0 40.3837305,3.07971215 41.9031457,7.37828316 L49.0976562,7.37847223 C50.9853482,7.37847223 52.515625,8.90874896 52.515625,10.796441 L52.515625,19.1514757 C52.515625,21.0391678 50.9853482,22.5694445 49.0976562,22.5694445 L14.2669271,22.5694445 C12.3792351,22.5694445 10.8489583,21.0391678 10.8489583,19.1514757 L10.8490061,16.7278747 C10.0235711,15.3994855 9.546875,13.8318008 9.546875,12.1527778 C9.546875,7.35863932 13.4332921,3.47222223 18.2274305,3.47222223 C19.776893,3.47222223 21.2315367,3.87818883 22.490799,4.58955922 C24.5010828,1.80910145 27.7719111,0 31.4652777,0 Z" id="云" fill="url(#linearGradient-3)"></path>
            <g id="草" transform="translate(33.203125, 113.328125)">
                <ellipse id="椭圆形" fill="#CCDCF6" cx="5.234375" cy="15.234375" rx="2.5" ry="1"></ellipse>
                <path d="M2.58001924,14.928412 C2.9204534,9.24340818 4.34664107,5.95295937 6.85858228,5.05706562 C7.84753584,4.92502992 8.94974166,4.72650473 8.18121148,6.00816311 C7.41268129,7.2898215 5.35469842,6.7910434 3.29678379,14.9250234 C3.11065035,15.2727916 2.87172883,15.2739211 2.58001924,14.928412 Z" id="路径-42" fill="url(#linearGradient-4)"></path>
                <path d="M2.5330573,14.8571882 C1.86473501,9.54728352 1.31773574,6.20798852 0.892059484,4.8393032 C0.253545097,2.78627521 -1.10597114,-1.1648377 1.72351485,0.329968683 C4.55300084,1.82477506 4.88753279,9.49240816 3.57962598,14.1587886 C3.17310109,15.3395931 2.82424486,15.572393 2.5330573,14.8571882 Z" id="路径-41" fill="url(#linearGradient-5)"></path>
            </g>
            <g id="草" transform="translate(134.007856, 102.669278)">
                <path d="M12.8628299,18.6412153 C12.3710052,16.4024717 10.9347,14.8375106 8.55391416,13.9463319 C5.83514369,13.0723787 4.34560324,12.033536 6.04687137,11.2802787 C7.4834992,11.1660799 10.5473239,10.0472503 12.8628299,16.7301368 C13.0239772,17.3777425 13.0239772,18.0147687 12.8628299,18.6412153 Z" id="路径-43" fill="url(#linearGradient-6)"></path>
                <path d="M12.9836904,18.99631 C11.5518512,13.6515662 9.92528201,9.74942906 8.10398297,7.28989842 C6.76665213,5.60914867 6.86036086,3.20412187 8.10398297,2.66052201 C9.37696057,2.51895934 11.723098,2.30000738 13.4896311,11.511198 C13.6012017,12.1447801 14.223665,15.8513663 13.4896311,18.2434922 C13.3809828,18.571792 13.2123359,18.8227313 12.9836904,18.99631 Z" id="路径-44" fill="url(#linearGradient-7)"></path>
                <path d="M13.3143813,17.7891423 C13.9619823,15.3975514 13.8797623,12.2770763 13.0677213,8.42771725 C11.8256231,4.69610754 9.70550281,0.105819642 13.5698148,0 C15.1375843,0.213193102 17.0307068,1.85653025 16.3153855,10.0467321 C15.8794772,13.3516422 15.1213519,15.304011 14.3257239,17.7891423 C14.0481803,17.9440994 13.7110661,17.9440994 13.3143813,17.7891423 Z" id="路径-45" fill="url(#linearGradient-8)"></path>
                <path d="M14.0708442,18.7179748 C13.8116175,11.9554894 15.0492626,7.10234297 17.7837794,4.15853547 C18.5222121,3.1748323 21.331535,2.25352527 21.3209594,5.3371867 C21.0276473,6.66314168 20.4000854,9.4938899 16.7813035,13.8127782 C15.5891199,15.3715901 14.8055878,17.6494477 14.7979506,18.1835208 C14.7928592,18.5395696 14.5504904,18.7177209 14.0708442,18.7179748 Z" id="路径-46" fill="url(#linearGradient-9)"></path>
                <path d="M0,19.6108811 C2.70788703,19.1061784 4.2012499,18.6777285 4.48008861,18.3255312 C4.89834666,17.7972353 8.58300975,15.5289115 10.8807505,17.4395526 C11.3809321,16.7967348 12.9850632,16.2967038 13.9030731,17.4395526 C14.3230681,15.6352362 16.943177,14.4796929 18.0826893,14.6668954 C19.3659587,14.5664683 23.1543633,14.608252 23.4375,19.6108811 C22.4038752,19.6349455 14.5913753,19.6349455 0,19.6108811 Z" id="路径-47" fill="url(#linearGradient-10)"></path>
            </g>
        </g>
        <g id="编组-2" transform="translate(53.578125, 50.710938)" fill-rule="nonzero">
            <polygon id="路径" fill="url(#linearGradient-11)" opacity="0.3" transform="translate(64.847591, 94.413316) rotate(-50.000000) translate(-64.847591, -94.413316) " points="42.7785725 49.3809496 82.5706692 93.6698683 92.2725331 139.445683 37.4226479 121.214222"></polygon>
            <g id="暂无记录">
                <path d="M72.6492881,89.453125 L21.8819619,89.453125 C17.9942687,89.453125 14.84375,86.3145424 14.84375,82.4415781 L14.84375,13.2615469 C14.84375,9.38858256 17.9942687,6.25 21.8819619,6.25 L72.6492881,6.25 C76.5369813,6.25 79.6875,9.38858256 79.6875,13.2615469 L79.6875,82.4415781 C79.6875,86.3145424 76.5369813,89.453125 72.6492881,89.453125 Z" id="路径" fill="url(#linearGradient-12)"></path>
                <path d="M69.5496131,91.40625 L21.0753869,91.40625 C17.2016824,91.40625 14.0625,88.2549754 14.0625,84.3663492 L14.0625,16.0242758 C14.0625,12.1356496 17.2016824,8.984375 21.0753869,8.984375 L69.5496131,8.984375 C73.4233176,8.984375 76.5625,12.1356496 76.5625,16.0242758 L76.5625,84.3684742 C76.5625,88.2549754 73.4233176,91.40625 69.5496131,91.40625 Z" id="路径" fill="url(#linearGradient-13)"></path>
                <path d="M68.9302344,82.8125 L22.4760156,82.8125 C21.9281713,82.8125 21.484375,82.3697086 21.484375,81.8231047 L21.484375,15.4425203 C21.484375,14.8959165 21.9281713,14.453125 22.4760156,14.453125 L68.9302344,14.453125 C69.4780787,14.453125 69.921875,14.8959165 69.921875,15.4425203 L69.921875,81.8231047 C69.9197516,82.3697086 69.4759553,82.8125 68.9302344,82.8125 Z" id="路径" fill="#FAFAFA"></path>
                <path d="M43.2549094,82.4216554 C38.9281129,82.1277914 15.1296733,80.3224816 0.39357453,68.391276 C-0.334970742,67.8001379 0.00600538848,66.585966 0.939983483,66.5009102 C23.8107986,64.429801 21.0258072,23.4966855 21.1867648,15.6481593 C21.1994721,14.9825975 21.6230449,14.453125 22.2859363,14.453125 L68.4659643,14.453125 C69.0547305,14.453125 69.53125,14.931564 69.53125,15.522702 L67.1126492,59.479552 C67.0236988,59.7219609 66.8542697,60.1918945 66.6869584,60.3875229 L48.9041072,80.2754686 C48.6859672,80.5306361 43.5895318,82.4446244 43.2549094,82.4216554 Z" id="路径" fill="#FFFFFF"></path>
                <path d="M49.8303887,67.1706082 C52.4843361,67.3374156 60.3913709,67.3986486 67.1875,59.765625 C65.5770029,67.7956082 55.2352506,82.3226352 41.796875,82.421875 C49.5184076,78.4396115 49.2232902,71.0663006 48.9787645,67.7597129 C48.9513607,67.3711994 49.4446281,67.1452703 49.8303887,67.1706082 Z" id="路径" fill="#D4E6FF"></path>
                <g id="编组-2" transform="translate(23.761013, 24.609375)">
                    <rect id="矩形" fill="url(#linearGradient-14)" x="7.87961248" y="0" width="31.25" height="2.34375" rx="1.171875"></rect>
                    <rect id="矩形备份" fill="url(#linearGradient-14)" x="7.09836248" y="7.8125" width="31.25" height="2.34375" rx="1.171875"></rect>
                    <rect id="矩形备份-2" fill="url(#linearGradient-15)" x="6.31711248" y="15.625" width="15.625" height="2.34375" rx="1.171875"></rect>
                    <rect id="矩形备份-3" fill="url(#linearGradient-16)" x="24.2858625" y="15.625" width="2.34375" height="2.34375" rx="1.171875"></rect>
                    <path d="M5.73117498,23.4375 L35.8093,23.4375 C36.1329043,23.4375 36.3952375,23.6998332 36.3952375,24.0234375 L36.3952375,25.1953125 C36.3952375,25.5189168 36.1329043,25.78125 35.8093,25.78125 L5.73117498,25.78125 C5.40757064,25.78125 5.14523748,25.5189168 5.14523748,25.1953125 L5.14523748,24.0234375 C5.14523748,23.6998332 5.40757064,23.4375 5.73117498,23.4375 Z" id="矩形备份-7" fill="url(#linearGradient-14)" transform="translate(20.770237, 24.609375) rotate(2.000000) translate(-20.770237, -24.609375) "></path>
                    <path d="M3.38742498,31.25 L33.46555,31.25 C33.7891543,31.25 34.0514875,31.5123332 34.0514875,31.8359375 L34.0514875,33.0078125 C34.0514875,33.3314168 33.7891543,33.59375 33.46555,33.59375 L3.38742498,33.59375 C3.06382064,33.59375 2.80148748,33.3314168 2.80148748,33.0078125 L2.80148748,31.8359375 C2.80148748,31.5123332 3.06382064,31.25 3.38742498,31.25 Z" id="矩形备份-6" fill="url(#linearGradient-14)" transform="translate(18.426487, 32.421875) rotate(4.000000) translate(-18.426487, -32.421875) "></path>
                    <path d="M0.670519947,38.7768688 L15.1236449,38.7768688 C15.4472493,38.7768688 15.7095824,39.039202 15.7095824,39.3628063 L15.7095824,40.5346813 C15.7095824,40.8582855 15.4472493,41.1206188 15.1236449,41.1206188 L0.670519947,41.1206188 C0.346915602,41.1206188 0.0845824465,40.8582855 0.0845824465,40.5346813 L0.0845824465,39.3628063 C0.0845824465,39.039202 0.346915602,38.7768688 0.670519947,38.7768688 Z" id="矩形备份-5" fill="url(#linearGradient-15)" transform="translate(7.897082, 39.948744) rotate(7.000000) translate(-7.897082, -39.948744) "></path>
                    <path d="M18.5548318,40.1574199 L19.7267068,40.1574199 C20.0503111,40.1574199 20.3126443,40.4197531 20.3126443,40.7433574 L20.3126443,41.9152324 C20.3126443,42.2388367 20.0503111,42.5011699 19.7267068,42.5011699 L18.5548318,42.5011699 C18.2312275,42.5011699 17.9688943,42.2388367 17.9688943,41.9152324 L17.9688943,40.7433574 C17.9688943,40.4197531 18.2312275,40.1574199 18.5548318,40.1574199 Z" id="矩形备份-4" fill="url(#linearGradient-17)" transform="translate(19.140769, 41.329295) rotate(7.000000) translate(-19.140769, -41.329295) "></path>
                </g>
                <g id="编组" transform="translate(42.859375, 0.000000)">
                    <path d="M4.015625,0 C5.95725107,0 7.53125,1.57399893 7.53125,3.515625 C7.53125,5.45725107 5.95725107,7.03125 4.015625,7.03125 C2.07399893,7.03125 0.5,5.45725107 0.5,3.515625 C0.5,1.57399893 2.07399893,0 4.015625,0 Z M4.015625,1.5625 C2.93694385,1.5625 2.0625,2.43694385 2.0625,3.515625 C2.0625,4.59430615 2.93694385,5.46875 4.015625,5.46875 C5.09430615,5.46875 5.96875,4.59430615 5.96875,3.515625 C5.96875,2.43694385 5.09430615,1.5625 4.015625,1.5625 Z" id="形状结合" fill="url(#linearGradient-18)"></path>
                    <path d="M3.515625,0.5000005 C5.45725107,0.5000005 7.03125,2.07399943 7.03125,4.0156255 C7.03125,5.95725157 5.45725107,7.5312505 3.515625,7.5312505 C1.57399893,7.5312505 0,5.95725157 0,4.0156255 C0,2.07399943 1.57399893,0.5000005 3.515625,0.5000005 Z M3.515625,2.0625005 C2.43694385,2.0625005 1.5625,2.93694435 1.5625,4.0156255 C1.5625,5.09430665 2.43694385,5.9687505 3.515625,5.9687505 C4.59430615,5.9687505 5.46875,5.09430665 5.46875,4.0156255 C5.46875,2.93694435 4.59430615,2.0625005 3.515625,2.0625005 Z" id="形状结合备份" fill="url(#linearGradient-19)"></path>
                </g>
            </g>
        </g>
        <g id="人物" transform="translate(121.000000, 106.000000)" fill-rule="nonzero">
            <ellipse id="椭圆形" fill="#CCDCF6" cx="12.5506276" cy="48.046875" rx="9.765625" ry="3.125"></ellipse>
            <rect id="矩形" fill="url(#linearGradient-20)" x="9.93845119" y="11.328125" width="3.125" height="3.125"></rect>
            <ellipse id="椭圆形" fill="#FEE0BC" transform="translate(15.480315, 8.789063) rotate(22.000000) translate(-15.480315, -8.789063) " cx="15.4803151" cy="8.78906251" rx="1" ry="1"></ellipse>
            <ellipse id="椭圆形备份-2" fill="#FEE0BC" transform="translate(7.277190, 8.789063) scale(-1, 1) rotate(23.000000) translate(-7.277190, -8.789063) " cx="7.27719009" cy="8.78906251" rx="1" ry="1"></ellipse>
            <circle id="椭圆形" fill="url(#linearGradient-21)" cx="11.5009512" cy="8.203125" r="3.90625"></circle>
            <path d="M9.54868382,45.7881566 L9.38671199,46.8466766 C8.72707425,46.8673059 8.00972763,47.0612037 7.23467212,47.4283705 C6.73592664,47.7483586 6.71372884,47.8933457 6.69767285,48.1688697 C6.68696886,48.3525523 8.09612285,48.3525523 10.9251348,48.1688697 L10.9251348,45.4002176 L9.54868382,45.7881566 Z" id="路径-52" fill="url(#linearGradient-22)"></path>
            <path d="M16.9705587,45.7881566 L16.8085869,46.8466766 C16.1489492,46.8673059 15.4316025,47.0612037 14.656547,47.4283705 C14.1578016,47.7483586 14.1356038,47.8933457 14.1195478,48.1688697 C14.1088438,48.3525523 15.5179978,48.3525523 18.3470097,48.1688697 L18.3470097,45.4002176 L16.9705587,45.7881566 Z" id="路径-52备份" fill="url(#linearGradient-22)" transform="translate(16.233248, 46.853425) scale(-1, 1) translate(-16.233248, -46.853425) "></path>
            <polygon id="路径-49" fill="url(#linearGradient-23)" points="8.04113324 26.4485861 9.1686096 46.1597355 11.3787526 46.1285881 12.3336029 27.9347545 13.8208216 46.120775 15.8987721 46.0870068 16.4544809 26.4912658"></polygon>
            <path d="M20.4853262,18.2374324 C20.7762395,18.0061511 21.0781024,17.9167634 21.3909149,17.9692693 C21.6021694,17.9740649 21.7234702,17.9216493 21.7548173,17.8120223 C22.1193032,17.756302 22.2651368,17.8272037 22.192318,18.0247273 C22.1194993,18.222251 22.0280132,18.2931526 21.9178596,18.2374324 L22.4197272,18.4674569 L22.8968167,18.1305447 C23.4874079,18.1374369 23.7113628,18.335756 23.5686815,18.7255018 C23.3213116,19.1327365 22.6458526,19.6432074 21.7548173,19.2180334 C21.5594218,19.069378 21.3817403,19.0020786 21.2217729,19.0161351 L20.4853262,18.2374324 Z" id="路径-50" fill="#FEE0BC"></path>
            <path d="M0.172826316,19.0186824 C0.463739621,18.7874012 0.765602518,18.6980135 1.078415,18.7505193 C1.28966946,18.7553149 1.41097025,18.7028993 1.44231737,18.5932723 C1.80680336,18.5375521 1.95263697,18.6084537 1.87981819,18.8059773 C1.8069994,19.003501 1.71551327,19.0744027 1.60535979,19.0186824 L2.10722734,19.2487069 L2.58431674,18.9117947 C3.17490795,18.918687 3.39886289,19.117006 3.25618158,19.5067518 C3.0088118,19.9139865 2.33335272,20.4244574 1.44231737,19.9992834 C1.24692186,19.850628 1.06924041,19.7833285 0.909272993,19.7973852 L0.172826316,19.0186824 Z" id="路径-50备份" fill="#FEE0BC" transform="translate(1.735326, 19.367201) scale(-1, 1) rotate(-19.000000) translate(-1.735326, -19.367201) "></path>
            <path d="M9.82511578,12.7659314 L11.7693776,14.046585 L13.1776116,12.6897685 L17.416391,14.27311 L18.4329939,19.6630785 L20.4607827,17.96875 L21.8147509,19.2086955 L18.6750292,23.924184 C17.7738549,24.8845953 17.1026775,24.8845953 16.6614972,23.924184 C16.5630782,23.3642521 16.2803181,21.4553594 15.813217,18.197506 L16.7152932,26.6714883 L7.79563337,26.7635365 L8.41788859,18.2007694 C8.29652632,22.0804719 7.72098101,23.9882768 6.69125263,23.924184 C5.95391872,23.728232 4.42347431,22.6061033 2.09991935,20.5577975 L2.68929646,18.9765303 L5.47731831,20.4420234 L6.05803035,15.1802966 L9.82511578,12.7659314 Z" id="路径-48" fill="url(#linearGradient-24)"></path>
            <ellipse id="椭圆形" fill="#3C3F58" cx="11.1834401" cy="1.3671875" rx="1.7578125" ry="1.3671875"></ellipse>
            <path d="M7.04052843,8.59375 C9.74913564,7.12395717 11.2089391,5.90476631 11.4199387,4.93617744 C11.877336,6.25044908 14.6844644,8.28590021 15.7993489,8.26694816 C16.4161439,6.48830789 16.586863,4.81392945 14.6390085,3.18115145 C12.6911541,1.54837344 10.4451344,1.53919869 7.98491734,3.18115145 C6.33083078,4.57151705 6.01603447,6.37571656 7.04052843,8.59375 Z" id="路径-51" fill="#3C3F58"></path>
        </g>
    </g>
</svg>