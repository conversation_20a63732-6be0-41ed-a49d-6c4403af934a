<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="-29.88559%" x2="50%" y2="44.0501254%" id="linearGradient-1">
            <stop stop-color="#CFDFFA" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="33.7484637%" x2="53.6226986%" y2="69.8229087%" id="linearGradient-2">
            <stop stop-color="#D6E8FF" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="36.47456%" x2="53.6226986%" y2="66.4977364%" id="linearGradient-3">
            <stop stop-color="#D6E8FF" offset="0%"></stop>
            <stop stop-color="#EAF0FC" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="57.28025%" y2="100%" id="linearGradient-4">
            <stop stop-color="#BFD4FA" offset="0%"></stop>
            <stop stop-color="#5792F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="51.9400923%" y2="131.881015%" id="linearGradient-5">
            <stop stop-color="#BFD4FA" offset="0%"></stop>
            <stop stop-color="#5792F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="13.0877007%" x2="74.282792%" y2="128.49897%" id="linearGradient-6">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="54.0733641%" y2="131.881015%" id="linearGradient-7">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="51.979585%" y2="131.881015%" id="linearGradient-8">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="11.4973736%" x2="55.3644123%" y2="131.881015%" id="linearGradient-9">
            <stop stop-color="#C9E3FF" offset="0%"></stop>
            <stop stop-color="#6EB9FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="48.2590898%" x2="74.2827921%" y2="53.7022797%" id="linearGradient-10">
            <stop stop-color="#C5E0FF" offset="0%"></stop>
            <stop stop-color="#59B4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="116.959906%" y1="58.3022102%" x2="-17.9385803%" y2="-7.27789959%" id="linearGradient-11">
            <stop stop-color="#5FABFF" offset="0%"></stop>
            <stop stop-color="#BFD4FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="108.018237%" y1="96.5454027%" x2="0%" y2="7.0942393%" id="linearGradient-12">
            <stop stop-color="#BAD9FF" offset="0%"></stop>
            <stop stop-color="#EAF4FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="87.6224684%" y1="58.9183998%" x2="11.8276479%" y2="-11.5290622%" id="linearGradient-13">
            <stop stop-color="#5FABFF" offset="0%"></stop>
            <stop stop-color="#BFD4FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="82.5984524%" y1="100%" x2="21.9067193%" y2="3.90977606%" id="linearGradient-14">
            <stop stop-color="#DBEBFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="插画/路线-ai-搜索超时" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="背景" transform="translate(6.250000, 23.101563)" fill-rule="nonzero">
            <path d="M86.75,151.398438 C138.526695,151.398438 187.5,178.576404 187.5,151.609375 C187.5,124.642346 145.526695,102.78125 93.75,102.78125 C41.9733047,102.78125 0,124.642346 0,151.609375 C0,178.576404 34.9733047,151.398438 86.75,151.398438 Z" id="椭圆形" fill="url(#linearGradient-1)"></path>
            <path d="M158.984375,24.6976799 C158.984375,27.5089963 156.285511,29.7580492 152.876421,29.7580492 L133.984375,29.7580492 C130.575284,29.5331439 127.734375,27.3965436 127.734375,24.4727746 C127.734375,22.5610795 129.012784,20.8742898 130.859375,19.9746686 C130.717329,19.6373106 130.575284,19.2999527 130.575284,18.9625947 C130.575284,17.0508996 132.421875,15.3641098 134.836648,15.3641098 C135.830966,15.3641098 136.683239,15.7014678 137.393466,16.1512784 C139.098011,13.5648674 142.223011,11.765625 145.916193,11.765625 C151.029829,11.765625 155.149148,15.2516572 155.149148,19.5248579 L155.149148,19.9746686 C157.421875,20.7618371 158.984375,22.5610795 158.984375,24.6976799 Z" id="云" fill="url(#linearGradient-2)"></path>
            <path d="M31.4652777,0 C36.2841568,0 40.3837305,3.07971215 41.9031457,7.37828316 L49.0976562,7.37847223 C50.9853482,7.37847223 52.515625,8.90874896 52.515625,10.796441 L52.515625,19.1514757 C52.515625,21.0391678 50.9853482,22.5694445 49.0976562,22.5694445 L14.2669271,22.5694445 C12.3792351,22.5694445 10.8489583,21.0391678 10.8489583,19.1514757 L10.8490061,16.7278747 C10.0235711,15.3994855 9.546875,13.8318008 9.546875,12.1527778 C9.546875,7.35863932 13.4332921,3.47222223 18.2274305,3.47222223 C19.776893,3.47222223 21.2315367,3.87818883 22.490799,4.58955922 C24.5010828,1.80910145 27.7719111,0 31.4652777,0 Z" id="云" fill="url(#linearGradient-3)"></path>
            <g id="草" transform="translate(33.203125, 113.328125)">
                <ellipse id="椭圆形" fill="#CCDCF6" cx="5.234375" cy="15.234375" rx="2.5" ry="1"></ellipse>
                <path d="M2.58001924,14.928412 C2.9204534,9.24340818 4.34664107,5.95295937 6.85858228,5.05706562 C7.84753584,4.92502992 8.94974166,4.72650473 8.18121148,6.00816311 C7.41268129,7.2898215 5.35469842,6.7910434 3.29678379,14.9250234 C3.11065035,15.2727916 2.87172883,15.2739211 2.58001924,14.928412 Z" id="路径-42" fill="url(#linearGradient-4)"></path>
                <path d="M2.5330573,14.8571882 C1.86473501,9.54728352 1.31773574,6.20798852 0.892059484,4.8393032 C0.253545097,2.78627521 -1.10597114,-1.1648377 1.72351485,0.329968683 C4.55300084,1.82477506 4.88753279,9.49240816 3.57962598,14.1587886 C3.17310109,15.3395931 2.82424486,15.572393 2.5330573,14.8571882 Z" id="路径-41" fill="url(#linearGradient-5)"></path>
            </g>
            <g id="草" transform="translate(134.007856, 102.669278)">
                <path d="M12.8628299,18.6412153 C12.3710052,16.4024717 10.9347,14.8375106 8.55391416,13.9463319 C5.83514369,13.0723787 4.34560324,12.033536 6.04687137,11.2802787 C7.4834992,11.1660799 10.5473239,10.0472503 12.8628299,16.7301368 C13.0239772,17.3777425 13.0239772,18.0147687 12.8628299,18.6412153 Z" id="路径-43" fill="url(#linearGradient-6)"></path>
                <path d="M12.9836904,18.99631 C11.5518512,13.6515662 9.92528201,9.74942906 8.10398297,7.28989842 C6.76665213,5.60914867 6.86036086,3.20412187 8.10398297,2.66052201 C9.37696057,2.51895934 11.723098,2.30000738 13.4896311,11.511198 C13.6012017,12.1447801 14.223665,15.8513663 13.4896311,18.2434922 C13.3809828,18.571792 13.2123359,18.8227313 12.9836904,18.99631 Z" id="路径-44" fill="url(#linearGradient-7)"></path>
                <path d="M13.3143813,17.7891423 C13.9619823,15.3975514 13.8797623,12.2770763 13.0677213,8.42771725 C11.8256231,4.69610754 9.70550281,0.105819642 13.5698148,0 C15.1375843,0.213193102 17.0307068,1.85653025 16.3153855,10.0467321 C15.8794772,13.3516422 15.1213519,15.304011 14.3257239,17.7891423 C14.0481803,17.9440994 13.7110661,17.9440994 13.3143813,17.7891423 Z" id="路径-45" fill="url(#linearGradient-8)"></path>
                <path d="M14.0708442,18.7179748 C13.8116175,11.9554894 15.0492626,7.10234297 17.7837794,4.15853547 C18.5222121,3.1748323 21.331535,2.25352527 21.3209594,5.3371867 C21.0276473,6.66314168 20.4000854,9.4938899 16.7813035,13.8127782 C15.5891199,15.3715901 14.8055878,17.6494477 14.7979506,18.1835208 C14.7928592,18.5395696 14.5504904,18.7177209 14.0708442,18.7179748 Z" id="路径-46" fill="url(#linearGradient-9)"></path>
                <path d="M0,19.6108811 C2.70788703,19.1061784 4.2012499,18.6777285 4.48008861,18.3255312 C4.89834666,17.7972353 8.58300975,15.5289115 10.8807505,17.4395526 C11.3809321,16.7967348 12.9850632,16.2967038 13.9030731,17.4395526 C14.3230681,15.6352362 16.943177,14.4796929 18.0826893,14.6668954 C19.3659587,14.5664683 23.1543633,14.608252 23.4375,19.6108811 C22.4038752,19.6349455 14.5913753,19.6349455 0,19.6108811 Z" id="路径-47" fill="url(#linearGradient-10)"></path>
            </g>
        </g>
        <path d="M99.2091947,60.4297718 C120.111889,60.4297718 137.020439,77.0720446 137.020439,97.5753247 C137.020439,107.560688 133.159431,116.614085 126.63566,123.270994 L132.626879,129.262212 C133.95826,130.593594 133.95826,132.856943 132.626879,134.188325 C131.162358,135.519707 128.899009,135.519707 127.567628,134.188325 L121.310133,127.93083 C115.052638,132.32439 107.463762,134.854016 99.3423329,134.854016 C91.2209038,134.854016 83.6320274,132.191252 77.507671,127.93083 L71.1170383,134.188325 C69.6525182,135.519707 67.3891691,135.519707 66.0577873,134.188325 C64.7264054,132.856943 64.7264054,130.593594 66.0577873,129.262212 L72.0490055,123.404132 C65.5252346,116.747223 61.5310891,107.693827 61.5310891,97.7084629 C61.5310891,77.0720446 78.4396383,60.4297718 99.2091947,60.4297718 Z M129.032148,59.7640809 L139.017511,69.6163064 C140.348893,70.9476883 140.348893,73.0778991 138.884373,74.5424191 C137.552991,75.873801 135.289642,75.873801 133.825122,74.5424191 L123.972897,64.6901936 C122.641515,63.3588118 122.641515,61.0954627 123.972897,59.7640809 C125.304278,58.432699 127.567628,58.432699 129.032148,59.7640809 Z M76.043151,59.09839 C77.3745328,60.4297718 77.3745328,62.6931209 76.043151,64.0245028 L66.0577873,73.8767282 C64.7264054,75.2081101 62.4630564,75.2081101 60.9985364,73.8767282 C59.6671545,72.5453464 59.6671545,70.2819973 60.9985364,68.9506155 L70.9839001,59.09839 C72.44842,57.63387 74.7117692,57.63387 76.043151,59.09839 Z" id="形状" fill="url(#linearGradient-11)" fill-rule="nonzero"></path>
        <path d="M96.2091947,61.4297718 C117.111889,61.4297718 134.020439,78.0720446 134.020439,98.5753247 C134.020439,108.560688 130.159431,117.614085 123.63566,124.270994 L129.626879,130.262212 C130.95826,131.593594 130.95826,133.856943 129.626879,135.188325 C128.162358,136.519707 125.899009,136.519707 124.567628,135.188325 L118.310133,128.93083 C112.052638,133.32439 104.463762,135.854016 96.3423329,135.854016 C88.2209038,135.854016 80.6320274,133.191252 74.507671,128.93083 L68.1170383,135.188325 C66.6525182,136.519707 64.3891691,136.519707 63.0577873,135.188325 C61.7264054,133.856943 61.7264054,131.593594 63.0577873,130.262212 L69.0490055,124.404132 C62.5252346,117.747223 58.5310891,108.693827 58.5310891,98.7084629 C58.5310891,78.0720446 75.4396383,61.4297718 96.2091947,61.4297718 Z M126.032148,60.7640809 L136.017511,70.6163064 C137.348893,71.9476883 137.348893,74.0778991 135.884373,75.5424191 C134.552991,76.873801 132.289642,76.873801 130.825122,75.5424191 L120.972897,65.6901936 C119.641515,64.3588118 119.641515,62.0954627 120.972897,60.7640809 C122.304278,59.432699 124.567628,59.432699 126.032148,60.7640809 Z M73.043151,60.09839 C74.3745328,61.4297718 74.3745328,63.6931209 73.043151,65.0245028 L63.0577873,74.8767282 C61.7264054,76.2081101 59.4630564,76.2081101 57.9985364,74.8767282 C56.6671545,73.5453464 56.6671545,71.2819973 57.9985364,69.9506155 L67.9839001,60.09839 C69.44842,58.63387 71.7117692,58.63387 73.043151,60.09839 Z" id="形状" fill="url(#linearGradient-12)" fill-rule="nonzero"></path>
        <path d="M95.676642,83.9301246 C93.6795693,83.9301246 92.0819111,85.3946447 92.0819111,87.3917174 L92.0819111,102.170056 C92.0819111,102.56947 92.0819111,102.968885 92.2150492,103.368299 C92.4813256,104.300267 93.0138783,104.965957 93.8127074,105.49851 L110.715402,115.347808 C112.31306,116.279776 114.576409,115.747223 115.508377,114.016427 C116.440344,112.28563 115.907791,110.155419 114.443271,109.223452 L100.25795,100.885985 C99.6467238,100.526735 99.271373,99.8707377 99.271373,99.1617534 L99.271373,87.3917174 C99.271373,85.5277828 97.6737148,83.9301246 95.676642,83.9301246 Z" id="路径" fill="url(#linearGradient-13)" fill-rule="nonzero"></path>
        <path d="M93.676642,84.9301246 C91.6795693,84.9301246 90.0819111,86.3946447 90.0819111,88.3917174 L90.0819111,103.170056 C90.0819111,103.56947 90.0819111,103.968885 90.2150492,104.368299 C90.4813256,105.300267 91.0138783,105.965957 91.8127074,106.49851 L108.715402,116.347808 C110.31306,117.279776 112.576409,116.747223 113.508377,115.016427 C114.440344,113.28563 113.907791,111.155419 112.443271,110.223452 L98.2579504,101.885985 C97.6467238,101.526735 97.271373,100.870738 97.271373,100.161753 L97.271373,88.3917174 C97.271373,86.5277828 95.6737148,84.9301246 93.676642,84.9301246 Z" id="路径备份" fill="url(#linearGradient-14)" fill-rule="nonzero"></path>
    </g>
</svg>