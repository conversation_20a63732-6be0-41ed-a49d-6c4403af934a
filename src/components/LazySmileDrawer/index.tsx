import { Skeleton } from 'antd'
import { Suspense, lazy } from 'react'
import { MoleculeStructureProps } from '../MoleculeStructure'
const MoleculeStructure = lazy(() =>
  import('@/components/MoleculeStructure').then((module) => ({
    default: module.default
  }))
)

export default function LazySmileDrawer(props: MoleculeStructureProps) {
  return (
    <Suspense
      fallback={
        <div>
          <Skeleton active />
        </div>
      }
    >
      <MoleculeStructure {...props} />
    </Suspense>
  )
}
