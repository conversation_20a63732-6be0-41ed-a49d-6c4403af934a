import { useEffect, useRef } from 'react'
import type { PopupWindowProps } from './index.d'
export default function PopupWindow(props: PopupWindowProps) {
  useEffect(() => {
    let scLauncher: any = document.querySelector('#sc-launcher')
    scLauncher.addEventListener('click', interceptLauncherClick)
    return () => scLauncher.removeEventListener('click', interceptLauncherClick)
  }, [])

  const emojiPopupRef = useRef(null)
  const interceptLauncherClick = (e: any) => {
    // const { isOpen } = props
    // const clickedOutside = !emojiPopupRef.contains(e.target) && props?.isOpen
    // clickedOutside && props.onClickedOutside(e)
    props?.onClickedOutside(e)
  }

  const { isOpen, children } = props
  return (
    <div className="sc-popup-window" ref={emojiPopupRef}>
      <div className={`sc-popup-window--cointainer ${isOpen ? '' : 'closed'}`}>
        <input
          onChange={props.onInputChange}
          className="sc-popup-window--search"
          placeholder="Search emoji..."
        />
        {children}
      </div>
    </div>
  )
}
