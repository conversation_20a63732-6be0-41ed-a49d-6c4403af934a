import { getWord } from '@/utils'
import cs from 'classnames'
import { useModel } from 'umi'
import AIIcon from '../assets/AI.png'
import closeIcon from '../assets/close-icon.png'
interface HeaderProps {
  showRobotIcon?: boolean
  onClose: () => void
  commendType?: 'reaction' | 'route'
  headerTitle?: string
}
export default function Header(props: HeaderProps) {
  const { commendSuject } = useModel('commend')
  return (
    <div className="sc-header">
      <img className="sc-header--img" src={AIIcon} alt="" />
      <div
        className={cs('sc-header--team-name', { hidden: props?.showRobotIcon })}
      >
        {props?.headerTitle ? (
          props?.headerTitle
        ) : (
          <>
            {props?.commendType === 'reaction'
              ? getWord('reaction')
              : getWord('Routes')}
            &nbsp;
            {getWord('ID')}: {commendSuject?.id}
          </>
        )}
      </div>
      <div className="sc-header--close-button" onClick={props.onClose}>
        <img src={closeIcon} alt="" />
      </div>
    </div>
  )
}
