import UserInput from '../UserInput'
import type { ChatWindowProps, SubmitValue } from '../types'
import Header from './Header'
import MessageList from './MessageList'

export default function ChatWindow(props: ChatWindowProps) {
  const { commendType, headerTitle } = props
  const onUserInputSubmit = (message: SubmitValue) =>
    props.onUserInputSubmit(message)
  let classList = ['sc-chat-window', props.isOpen ? 'opened' : 'closed']
  return (
    <div className={classList.join(' ')}>
      <Header
        onClose={props.onClose}
        showRobotIcon={props?.showRobotIcon}
        commendType={commendType}
        headerTitle={headerTitle}
      />
      <MessageList onSubmit={onUserInputSubmit} isRobot={props?.isRobot} />
      <UserInput onSubmit={onUserInputSubmit} />
    </div>
  )
}
