import type { CommendPoint, CommentConf } from '@/services/brain'
import type { DialogQuestion } from '@/types/models'
import { isValidArray } from '@/utils'
import { Space, Tag } from 'antd'
import { isArray, isEmpty } from 'lodash'
import { useEffect, useRef, useState } from 'react'
import { useModel } from 'umi'
import Message from '../Messages'
import likeCommentImg from '../assets/like-comment.png'
import robotIcon from '../assets/robot.png'
import unlikeImg from '../assets/unlike-comment.png'
import type { UserInputProps } from '../types'
const pointColor = {
  '-1': 'default',
  '0': 'green',
  '1': '#55acee'
}
export default function MessageList(props: UserInputProps) {
  const scrollRef = useRef(null)
  const { commonExpression, messageList, robotCommonExpression } =
    useModel('commend')
  const { initialState } = useModel('@@initialState')
  /* FIXME auto sroll not work */
  useEffect(() => {
    if (!scrollRef?.current) return
    scrollRef!.current!.scrollTop = scrollRef?.current?.scrollHeight
  }, [scrollRef])

  const getIcon = (point: CommendPoint) => {
    if (point === -1) return <img src={unlikeImg} alt="" />
    else if (point === 1) return <img src={likeCommentImg} alt="" />
    else return ''
  }

  const chooseCommend = (e: CommentConf & DialogQuestion) => {
    if (props?.isRobot) {
      props.onSubmit({
        userId: initialState?.userInfo?.id,
        commentor: initialState?.userInfo?.username,
        content: e?.question,
        type: 'text',
        value: e?.question
      })
    } else {
      props.onSubmit({
        userId: initialState?.userInfo?.id,
        commentor: initialState?.userInfo?.username,
        type: 'text',
        value: e.content_template,
        point: e.content_point
      })
    }
  }

  const ChatShorthand = () => (
    <>
      <div className="chat-shorthand">
        <div className="chat-robot">
          <img src={robotIcon} alt="" />
        </div>
        <div>你可能想输入：</div>
      </div>
      <div className="option">
        <Space size={[0, 8]} wrap>
          {props?.isRobot
            ? robotCommonExpression.map((item: DialogQuestion, i: number) => {
                return (
                  <Tag
                    key={`tag-${i}`}
                    className="tag"
                    onClick={() => chooseCommend(item)}
                  >
                    {item?.question}
                  </Tag>
                )
              })
            : commonExpression.map((item: CommentConf, i: number) => {
                return (
                  <Tag
                    key={`tag-${i}`}
                    className="tag"
                    icon={getIcon(item?.content_point as CommendPoint)}
                    color={pointColor[item?.content_point as CommendPoint]}
                    onClick={() => chooseCommend(item)}
                  >
                    &nbsp;&nbsp;{item?.content_template}
                  </Tag>
                )
              })}
        </Space>
      </div>
    </>
  )

  const [currentMessageList, setCurrentMessageList] = useState([])
  // const [loading, setLoading] = useState(false)
  function renderMessage() {
    return isArray(currentMessageList) && !isEmpty(currentMessageList) ? (
      currentMessageList.map((message) => (
        <Message message={message} key={`${message?.value}`} />
      ))
    ) : isValidArray(commonExpression) ||
      isValidArray(robotCommonExpression) ? (
      <ChatShorthand />
    ) : (
      ''
    )
  }

  useEffect(() => {
    // setLoading(true)
    setCurrentMessageList(messageList)
    // setLoading(false)
  }, [messageList])

  return (
    <div className="sc-message-list" ref={scrollRef}>
      {renderMessage()}
    </div>
  )
}
