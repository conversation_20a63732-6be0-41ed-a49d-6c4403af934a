import { KeyboardEvent, useRef, useState } from 'react'
import { useModel } from 'umi'
import xss from 'xss'
import { ReactComponent as LickSvg } from '../assets/like.svg'
import { ReactComponent as SendSvg } from '../assets/send.svg'
import { ReactComponent as UnLickSvg } from '../assets/unlike.svg'
import type { Attitude, UserInputProps } from '../types'
export default function UserInput(props: UserInputProps) {
  type KeyDownEvent = KeyboardEvent<HTMLInputElement>
  const [inputActive, setInputActive] = useState<boolean>(false)
  const [inputHasText, setInputHasText] = useState<boolean>(false)

  const userInputRef = useRef<HTMLInputElement>(null)
  const { initialState } = useModel('@@initialState')
  const submitText = (event: KeyDownEvent, point: Attitude) => {
    event.preventDefault()
    const inputText = userInputRef?.current?.textContent
      ? xss(userInputRef?.current?.textContent)
      : userInputRef?.current?.textContent
    if (inputText && inputText.length > 0) {
      props.onSubmit({
        userId: initialState?.userInfo?.id,
        commentor: initialState?.userInfo?.username,
        type: 'text',
        value: inputText,
        point
      })
      userInputRef.current.innerHTML = ''
    }
  }

  const handleKeyDown = (event: KeyDownEvent) => {
    if (event.keyCode === 13 && !event.shiftKey) return submitText(event, 0)
  }

  const handleKeyUp = (event) => {
    const inputHasText =
      event.target.innerHTML.length !== 0 && event.target.innerText !== '\n'
    setInputHasText(inputHasText)
  }

  return (
    <form className={`sc-user-input ${inputActive ? 'active' : ''}`}>
      <div
        role="button"
        tabIndex="0"
        onFocus={() => setInputActive(true)}
        onBlur={() => setInputActive(false)}
        ref={userInputRef}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        contentEditable="true"
        placeholder="Write a reply..."
        className="sc-user-input--text"
      />
      <div className="sc-user-input--buttons">
        {inputHasText ? (
          <>
            <div className="sc-user-input--button"></div>
            <div
              className="sc-user-input--button"
              onClick={(e) => submitText(e, -1)}
            >
              <UnLickSvg className="actionButton" />
            </div>
            <div
              className="sc-user-input--button"
              onClick={(e) => submitText(e, 1)}
            >
              <LickSvg className="actionButton" />
            </div>
            <div
              className="sc-user-input--button"
              onClick={(e) => submitText(e, 0)}
            >
              <SendSvg className="actionButton" />
            </div>
          </>
        ) : (
          ''
        )}
      </div>
    </form>
  )
}
