import cs from 'classnames'
import robotIconUrl from '../assets/C12-robot.svg'
import chatIconUrl from '../assets/chat-icon.svg'
import TextMessage from './TextMessage'
import type { MessageProps } from './index.d'
export default function Message(props: MessageProps) {
  return (
    <div className="sc-message">
      <div
        className={cs('sc-message--content', {
          sent: props.message.commentor === 'me',
          received: props.message.commentor !== 'me'
        })}
      >
        <div
          className="sc-message--avatar"
          style={{
            backgroundImage: `url(${
              props.message.commentor === 'C12助手' ? robotIconUrl : chatIconUrl
            })`
          }}
        />
        <TextMessage {...props.message} />
      </div>
    </div>
  )
}
