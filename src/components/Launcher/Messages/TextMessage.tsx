import { formatYTSTime, getWord } from '@/utils'
import Linkify from 'react-linkify'
import { useLocation } from 'umi'
import xss from 'xss'
import likeCommentImg from '../assets/like-comment.png'
import unlikeImg from '../assets/unlike-comment.png'

const TextMessage = (props) => {
  const { pathname } = useLocation()
  const isPlayground: boolean = pathname.includes('/playground')
  let curTime = new Date()
  return (
    <div className="sc-message--text">
      {
        <Linkify properties={{ target: '_blank' }}>
          <div
            dangerouslySetInnerHTML={{
              __html: props?.value ? xss(props?.value) : props?.value
            }}
          />
        </Linkify>
      }
      <div className="mood">
        {props.point === -1 && <img src={unlikeImg} alt="" />}
        {props.point === 1 && <img src={likeCommentImg} alt="" />}
      </div>
      <span className="time">
        {isPlayground && props?.comment_uri ? (
          <>
            <a href={props?.comment_uri}>{getWord('comment-object')}</a>{' '}
            &nbsp;&nbsp;
          </>
        ) : (
          ''
        )}
        <span className="commentor">{props?.commentor}</span>
        {props?.createdAt
          ? formatYTSTime(props?.createdAt)
          : formatYTSTime(curTime)}
      </span>
    </div>
  )
}
export default TextMessage
