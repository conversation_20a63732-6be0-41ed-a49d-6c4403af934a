.sc-launcher {
  position: fixed;
  bottom: 90px;
  left: 10px;
  z-index: 2147483647;
  width: 60px;
  height: 60px;
  background-color: #4e8cff;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 50%;
  box-shadow: none;
  transition: box-shadow 0.2s ease-in-out;
}

.sc-launcher:before {
  position: relative;
  display: block;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  transition: box-shadow 0.2s ease-in-out;
  content: '';
}

.sc-launcher .sc-open-icon,
.sc-launcher .sc-closed-icon {
  position: fixed;
  bottom: 90px;
  left: 10px;
  width: 60px;
  height: 60px;
  transition: opacity 100ms ease-in-out, transform 100ms ease-in-out;
}

.sc-launcher .sc-closed-icon {
  width: 60px;
  height: 60px;
  padding: 8px;
  transition: opacity 100ms ease-in-out, transform 100ms ease-in-out;
}

.sc-launcher .sc-open-icon {
  box-sizing: border-box;
  padding: 20px;
  opacity: 0;
}

.opened {
  cursor: pointer;
}

.sc-launcher.opened .sc-open-icon {
  transform: rotate(-90deg);
  opacity: 1;
}

.sc-launcher.opened .sc-closed-icon {
  transform: rotate(-90deg);
  opacity: 0;
}

.sc-launcher.opened:before {
  box-shadow: 0px 0px 400px 250px rgba(148, 149, 150, 0.2);
}

.sc-launcher:hover {
  box-shadow: 0 0px 27px 1.5px rgba(0, 0, 0, 0.2);
}

.sc-new-messages-count {
  position: absolute;
  top: -3px;
  left: 41px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 22px;
  height: 22px;
  margin: auto;
  color: white;
  font-weight: 500;
  font-size: 12px;
  text-align: center;
  background: #ff4646;
  border-radius: 50%;
  box-shadow: -1px 1px 2px rgba(0, 0, 0, 0.3);
}
