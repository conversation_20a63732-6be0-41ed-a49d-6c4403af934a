.sc-message {
  display: flex;
  width: 300px;
  margin: auto;
  padding-bottom: 10px;
}

.sc-message--content {
  display: flex;
  width: 100%;
  margin-bottom: 12px;
}

.sc-message--content.sent {
  justify-content: flex-end;
}

.sc-message--content.sent .sc-message--avatar {
  display: none;
}

.sc-message--avatar {
  align-self: center;
  min-width: 30px;
  min-height: 30px;
  margin-right: 15px;
  background-image: url(https://d13yacurqjgara.cloudfront.net/assets/avatar-default-aa2eab7684294781f93bc99ad394a0eb3249c5768c21390163c9f55ea8ef83a4.gif);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
  border-radius: 50%;
}

.sc-message--text {
  position: relative;
  width: calc(100% - 90px);
  padding: 17px 20px;
  font-weight: 300;
  font-size: 14px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  border-radius: 6px;
  -webkit-font-smoothing: subpixel-antialiased;
  .mood > img {
    position: absolute;
    right: 5px;
    bottom: 8px;
    width: 15px;
    height: 15px;
  }
  .time {
    position: absolute;
    right: 5px;
    bottom: -16px;
    width: max-content;
    /* color: #4e8cff; */
    color: #595859;
    /* color: #7d7d7f; */
    font-size: 10px;
  }
  .time > .commentor {
    margin-right: 6px;
    color: black;
    font-weight: bold;
    font-size: 12px;
  }
}

.sc-message--content.sent .sc-message--text {
  max-width: calc(100% - 120px);
  color: white;
  word-wrap: break-word;
  background-color: #4e8cff;
}

.sc-message--content.received .sc-message--text {
  margin-right: 40px;
  color: #263238;
  background-color: #f4f7f9;
}

.sc-message--emoji {
  font-size: 40px;
}

.sc-message--file {
  display: flex;
  padding: 15px 20px;
  font-weight: 300;
  font-size: 14px;
  line-height: 1.4;
  text-decoration: none;
  background: white;
  border: solid 1px #cccdd1;
  border-radius: 5px;
  cursor: pointer;
}

.sc-message--file p {
  margin: 0px 0px 0px 10px;
  color: rgba(86, 88, 103, 0.6);
}

.sc-message--file .sc-user-input--file-icon:hover path {
  fill: rgba(86, 88, 103, 0.3);
}

@media (max-width: 450px) {
  .sc-message {
    width: 80%;
  }
}
