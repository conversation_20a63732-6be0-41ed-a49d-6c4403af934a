.sc-header {
  position: relative;
  display: flex;
  box-sizing: border-box;
  min-height: 75px;
  padding: 10px;
  color: white;
  background: #4e8cff;
  border-top-left-radius: 9px;
  border-top-right-radius: 9px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.sc-header--img {
  align-self: center;
  width: 54px;
  height: 54px;
  padding: 10px;
  border-radius: 50%;
}

.sc-header--team-name {
  flex: 1;
  align-self: center;
  padding: 10px;
  border-radius: 5px;
  user-select: none;
}

.sc-header--close-button {
  align-self: center;
  box-sizing: border-box;
  width: 40px;
  height: 40px;
  margin-right: 10px;
  border-radius: 5px;
  cursor: pointer;
}

.sc-header--close-button:hover {
  background: #4882ed;
}

.sc-header--close-button img {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 13px;
}

@media (max-width: 450px) {
  .sc-header {
    border-radius: 0px;
  }
}
