.sc-user-input {
  position: relative;
  bottom: 0;
  display: flex;
  max-height: 150px;
  margin: 0px;
  background-color: #f4f7f9;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.sc-user-input--text {
  bottom: 0;
  box-sizing: border-box;
  width: 300px;
  max-height: 200px;
  padding: 18px;
  overflow: scroll;
  overflow-x: hidden;
  overflow-y: auto;
  color: #565867;
  font-weight: 400;
  font-size: 15px;
  line-height: 1.33;
  white-space: pre-wrap;
  word-wrap: break-word;
  border: none;
  border-bottom-left-radius: 10px;
  outline: none;
  resize: none;
  -webkit-font-smoothing: antialiased;
}

.sc-user-input--text:empty:before {
  display: block; /* For Firefox */
  color: rgba(86, 88, 103, 0.3);
  outline: none;
  content: attr(placeholder);
}

.sc-user-input--buttons {
  position: absolute;
  right: 10px;
  display: flex;
  justify-content: flex-end;
  width: 100px;
  height: 100%;
}

.sc-user-input--button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 30px;
  height: 55px;
}

.actionButton {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 20px !important;
  svg {
    width: 20px !important;
    height: auto;
  }
}

.sc-user-input--button button,
.actionButton {
  cursor: pointer;
}

.sc-user-input--buttons input[type='file'] {
  display: none;
}

.sc-user-input--picker-wrapper {
  display: flex;
  flex-direction: column;
}

.sc-user-input.active {
  background-color: white;
  box-shadow: none;
  box-shadow: 0px -5px 20px 0px rgba(150, 165, 190, 0.2);
}

.sc-user-input--file-icon,
.sc-user-input--send-icon {
  align-self: center;
  width: 20px;
  height: 20px;
  outline: none;
  cursor: pointer;
}

.actionButton path,
.sc-user-input--file-icon path,
.sc-user-input--send-icon path {
  fill: rgba(86, 88, 103, 0.3);
}

.actionButton:hover path,
.sc-user-input--file-icon:hover path,
.sc-user-input--send-icon:hover path {
  fill: rgba(86, 88, 103, 1);
}

.sc-user-input--emoji-icon-wrapper,
.sc-user-input--send-icon-wrapper,
.sc-user-input--file-icon-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0px;
  padding: 2px;
  background: none;
  border: none;
}

.sc-user-input--send-icon-wrapper,
.sc-user-input--file-icon-wrapper {
  flex-direction: row;
}

.sc-user-input--emoji-icon-wrapper:focus {
  outline: none;
}

.sc-user-input--emoji-icon {
  align-self: center;
  height: 18px;
  cursor: pointer;
}

.sc-user-input--emoji-icon path,
.sc-user-input--emoji-icon circle {
  fill: rgba(86, 88, 103, 0.3);
}

.sc-user-input--emoji-icon-wrapper:focus .sc-user-input--emoji-icon path,
.sc-user-input--emoji-icon-wrapper:focus .sc-user-input--emoji-icon circle,
.sc-user-input--emoji-icon.active path,
.sc-user-input--emoji-icon.active circle,
.sc-user-input--emoji-icon:hover path,
.sc-user-input--emoji-icon:hover circle {
  fill: rgba(86, 88, 103, 1);
}
