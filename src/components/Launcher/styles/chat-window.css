.sc-chat-window {
  position: fixed;
  right: 25px;
  bottom: 38px; /* 100px */
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  width: 370px;
  height: calc(100% - 120px);
  max-height: 590px;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: white;
  border-radius: 10px;
  box-shadow: 0px 7px 40px 2px rgba(148, 149, 150, 0.3);
  transition: 0.3s ease-in-out;
}

.sc-chat-window.closed {
  bottom: 90px;
  visibility: hidden;
  opacity: 0;
}

.sc-message-list {
  height: 80%;
  padding: 20px 0px;
  overflow-y: auto;
  background-color: white;
  background-size: 100%;
}

.sc-message--me {
  text-align: right;
}
.sc-message--them {
  text-align: left;
}

@media (max-width: 450px) {
  .sc-chat-window {
    right: 0px;
    bottom: 0px;
    width: 100%;
    height: 100%;
    max-height: 100%;
    border-radius: 0px;
  }
  .sc-chat-window {
    transition: 0.1s ease-in-out;
  }
  .sc-chat-window.closed {
    bottom: 0px;
  }
}

.chat-shorthand {
  display: flex;
  display: flex;
  align-items: center;
  min-height: 65px;
  margin: 0 35px;
}

.chat-robot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin-right: 15px;
  background: #4e8cff;
  border-radius: 15px;
}
.chat-robot > img {
  width: 20px;
  height: auto;
}

.option {
  margin: 0 0 10px 35px;
  .ant-tag {
    font-size: 16px;
    line-height: unset;
    cursor: pointer;
  }
  .ant-tag > img {
    position: relative;
    top: -1px;
    width: 14px;
    height: auto;
  }
}

.tag {
  margin-bottom: 5px;
}
