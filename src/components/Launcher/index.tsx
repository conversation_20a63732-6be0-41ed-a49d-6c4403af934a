import cs from 'classnames'
import { useEffect } from 'react'
import Cha<PERSON><PERSON><PERSON><PERSON> from './ChatWindow'
import launcherIconActive from './assets/close-icon.png'
import { ReactComponent as RobotIcon } from './assets/robot.svg'
import type { LauncherProps } from './index.d'
import './styles'
export default function Launcher(props: LauncherProps) {
  const { isOpen, showRobotIcon, commendType, headerTitle } = props
  useEffect(() => {
    if (isOpen) props?.hiddenLauncher()
    return () => {
      if (isOpen) props?.hiddenLauncher()
    }
  }, [])
  // TODO Note 使用context优化Launcher代码结构
  return (
    <div id="sc-launcher">
      {showRobotIcon ? (
        <div
          className={cs('sc-launcher', {
            opened: isOpen
          })}
          onClick={() => {
            props?.hiddenLauncher()
          }}
        >
          <img className={'sc-open-icon'} src={launcherIconActive} />
          <RobotIcon className={'sc-closed-icon'} fill="#fff" />
        </div>
      ) : (
        ''
      )}
      <ChatWindow
        headerTitle={headerTitle}
        commendType={commendType}
        showRobotIcon={showRobotIcon}
        onUserInputSubmit={props?.onMessageWasSent}
        isOpen={isOpen}
        onClose={props?.hiddenLauncher}
        isRobot={props?.isRobot}
      />
    </div>
  )
}
