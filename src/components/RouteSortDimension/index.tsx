import { RetroPreferenceConfig } from '@/services/brain'
import { isValidArray } from '@/utils'
import { fetchRetroPreferenceConfigs } from '@/utils/retroPreference'
import { ProFormRadio } from '@ant-design/pro-components'
import { useEffect, useState } from 'react'

export default function RouteSortDimension() {
  const [retroPreferenceConfigs, setRetroPreferenceConfigs] = useState<
    RetroPreferenceConfig[]
  >([])
  const getRetroPreferenceConfigs = async () => {
    const _retroPreferenceConfigs = await fetchRetroPreferenceConfigs()
    setRetroPreferenceConfigs(_retroPreferenceConfigs)
  }

  useEffect(() => {
    getRetroPreferenceConfigs()
  }, [])

  return (
    <>
      {isValidArray(retroPreferenceConfigs) &&
        retroPreferenceConfigs.map((e, index) => (
          <ProFormRadio.Group
            label={e?.label}
            name={e?.field}
            key={`${index}-retroConfigs`}
            options={e.options}
          />
        ))}
    </>
  )
}
