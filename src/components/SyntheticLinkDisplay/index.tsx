import { SyntheticLink } from '@/types/SyntheticRoute/SyntheticLink'
import React, { ReactElement } from 'react'
import Arrow from '../Arrow'
import SmilesDrawerMoleculeStructure from '../MoleculeStructure'
import './index.less'

export type SyntheticLinkDisplayProps = {
  node: SyntheticLink
  withWrapper?: boolean
  customChildren?: {
    structure?: (smiles: string, node: SyntheticLink) => ReactElement
    reaction?: (
      target: string,
      reactant: string,
      node: SyntheticLink
    ) => ReactElement
  }
  customProps?: {
    structureWrapper?: React.HTMLAttributes<HTMLDivElement>
    reactionWrapper?: React.HTMLAttributes<HTMLDivElement>
  }
}

const SyntheticLinkDisplay: React.FC<SyntheticLinkDisplayProps> = ({
  node,
  node: { value, child, rxn },
  withWrapper,
  customChildren = {},
  customProps = {}
}) => {
  const { structure, reaction } = customChildren
  const { structureWrapper, reactionWrapper } = customProps

  const context = (
    <>
      <div {...structureWrapper}>
        <SmilesDrawerMoleculeStructure
          structure={value}
          className="synthetic-link-display-node"
        />
        {structure?.(value, node)}
      </div>
      {child && (
        <>
          <div
            title={rxn}
            {...reactionWrapper}
            className={`arrow-wrapper ${reactionWrapper?.className}`}
          >
            <Arrow />
            {reaction?.(value, child.value, node)}
          </div>
          <SyntheticLinkDisplay
            node={child}
            customChildren={customChildren}
            customProps={customProps}
          />
        </>
      )}
    </>
  )
  return withWrapper ? (
    <div className="synthetic-link-display-node-root">{context}</div>
  ) : (
    <>{context}</>
  )
}

export default SyntheticLinkDisplay
