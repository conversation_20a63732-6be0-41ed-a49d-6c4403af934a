import { ReactComponent as LangENIcon } from '@/assets/svgs/systems/lang_EN.svg'
import { ReactComponent as LangCNIcon } from '@/assets/svgs/systems/lang_ZH.svg'
import { isEN } from '@/utils'
import { setLocale } from '@umijs/max'
export default function SwichLang() {
  return (
    <div onClick={() => (isEN() ? setLocale('zh-CN') : setLocale('en-US'))}>
      {isEN() ? <LangENIcon /> : <LangCNIcon />}
    </div>
  )
}
