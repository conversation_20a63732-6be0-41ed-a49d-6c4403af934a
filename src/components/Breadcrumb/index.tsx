import { IRout<PERSON> } from '@/types/Common'
import { Breadcrumb } from 'antd'
import cs from 'classnames'
import { isEmpty, isNil } from 'lodash'
import { history, Link, useLocation, useParams } from 'umi'
import routes from '../../../config/routes'
import type { CustomBreadcrumbProps, IRouteParams } from './index.d'
import styles from './index.less'

export default function CustomBreadcrumb(props: CustomBreadcrumbProps) {
  const { id, routeId } = useParams() as IRouteParams
  const { theme, customRoutes } = props
  const { pathname } = useLocation()
  const routesData = routes[0].routes.filter((e) => e.path !== '/'),
    isDetailPage = !isNil(id)
  const getCurRoutes = () => {
    if (routeId) {
      const basePath: string = pathname.split(`/${routeId}`)[0]
      const detailPath: string = basePath.split(`/${id}`)[1]
      const curRoutePath: string | undefined = routesData.find((e) =>
        e.path.includes(detailPath)
      )?.path
      if (!curRoutePath || !id) return
      const filterRoutes = routesData.filter((e) =>
        curRoutePath.includes(e.path)
      )
      const finalRoutes: IRoute[] = JSON.parse(JSON.stringify(filterRoutes))
      finalRoutes.map((e: IRoute) => {
        if (e.path.includes(':id') && !e.path.includes(':routeId'))
          e.path = e.path.replace(/:id/g, id)
      })
      return finalRoutes
    } else {
      const curRoutePath: string = pathname.split(`/${id}`)[0]
      return isDetailPage
        ? routesData.filter(
            (e) =>
              `${curRoutePath}/:id`.includes(e.path) || curRoutePath === e.path
          )
        : routesData.filter((e) => curRoutePath === e.path)
    }
  }

  function itemRender(route, _params, routes, paths) {
    const isLastRoute: boolean = routes.indexOf(route) === routes.length - 1
    return isLastRoute || !route?.path ? (
      <span>{route?.name || route?.breadcrumbName}</span>
    ) : (
      <Link
        onClick={(e) => {
          e.preventDefault()
          history.replace(route?.path)
        }}
        to={paths.join('/')}
      >
        {route?.name || route?.breadcrumbName}
      </Link>
    )
  }

  return (
    <div
      className={cs(styles.breadcrumb, {
        [styles['breadcrumb_light']]: theme === 'light',
        [styles['breadcrumb_dark']]: theme === 'dark'
      })}
    >
      {!isEmpty(customRoutes) ? (
        <Breadcrumb items={customRoutes} />
      ) : (
        <Breadcrumb itemRender={itemRender} items={getCurRoutes()} />
      )}
    </div>
  )
}
