import { reloadEvent } from '@/utils'
import { But<PERSON> } from 'antd'
import cs from 'classnames'
import type { TipCardProps } from './index.d'
import styles from './index.less'

export default function TipCard(props: TipCardProps) {
  return (
    <div className={cs(styles.tipCard, 'flex-center')}>
      <div className={styles.tipContent}>
        <img src={props.imgSrc} className={styles.tipImg} />
        <div className={styles.errorWord}>{props?.des}</div>
        {props?.buttonDes && (
          <Button
            className={styles.retryButton}
            shape="round"
            size="large"
            onClick={props?.buttonEvent || reloadEvent}
          >
            {props?.buttonDes}
          </Button>
        )}
      </div>
    </div>
  )
}
