import { WebRTCPlayer } from '@eyevinn/webrtc-player'
import { useEffect, useRef } from 'react'
import type { PacketsLost, RTCVideoProps } from './index.d'
import styles from './index.less'
const WebRTCVideoPlayer = (props: RTCVideoProps) => {
  const videoRef = useRef(null)
  /* let player
  const initPlayer = async () => {
    const video = document.querySelector('video')
    player = new WebRTCPlayer({
      video: video,
      type: 'whep',
      statsTypeFilter: '^candidate-*|^inbound-rtp'
    })
    await player.load(new URL(props?.rtcLink))
    player.unmute()
    player.on('no-media', () => {
      console.log('media timeout occured')
    })
    player.on('media-recovered', () => {
      console.log('media recovered')
    })

    // Subscribe for RTC stats: `stats:${RTCStatsType}`
    player.on('stats:inbound-rtp', (report) => {
      if (report.kind === 'video') {
        console.log(report)
      }
    })
  }
 */

  const playVideo = async () => {
    let player: WebRTCPlayer
    let channelUrl: string = props?.rtcLink
    if (videoRef?.current) {
      // video
      player = new WebRTCPlayer({
        video: videoRef?.current, // video
        type: 'whep',
        // iceServers: iceServers,
        debug: true,
        vmapUrl: undefined,
        statsTypeFilter: '^candidate-*|^inbound-rtp'
      })
    }

    const packetsLost: PacketsLost = { video: 0, audio: 0 }

    player.on('stats:candidate-pair', (report) => {
      const currentRTTElem =
        document.querySelector<HTMLSpanElement>('#stats-current-rtt')
      const incomingBitrateElem = document.querySelector<HTMLSpanElement>(
        '#stats-incoming-bitrate'
      )
      if (report.nominated && currentRTTElem) {
        currentRTTElem.innerHTML = `RTT: ${
          report.currentRoundTripTime * 1000
        }ms`
        if (report.availableIncomingBitrate && incomingBitrateElem) {
          incomingBitrateElem.innerHTML = `Bitrate: ${Math.round(
            report.availableIncomingBitrate / 1000
          )}kbps`
        }
      }
    })

    player.on('stats:inbound-rtp', (report) => {
      if (report.kind === 'video' || report.kind === 'audio') {
        const packetLossElem =
          document.querySelector<HTMLSpanElement>('#stats-packetloss')
        packetsLost[report.kind] = report.packetsLost
        if (packetLossElem) {
          packetLossElem.innerHTML = `Packets Lost: A=${packetsLost.audio},V=${packetsLost.video}`
        }
      }
    })

    await player.load(new URL(channelUrl))
  }

  useEffect(() => {
    playVideo()
  }, [])

  return (
    <>
      <video
        className={styles.livePlayer}
        ref={videoRef}
        autoPlay
        playsInline
        controls
      />
      {/* <Button onClick={() => playVideo()}>Play</Button> */}
    </>
  )
}

export default WebRTCVideoPlayer
