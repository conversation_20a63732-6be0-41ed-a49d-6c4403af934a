import { getReactionFromRxn } from '@/pages/reaction/util'
import { Image } from 'antd'
import axios from 'axios'
import React, { useEffect, useRef, useState } from 'react'

import { ReactComponent as AddIcon } from '@/assets/svgs/route-operation/add.svg'
import { ReactComponent as ToIcon } from '@/assets/svgs/route-operation/to.svg'
import { getEnvConfig } from '@/utils'
import { inchifySmiles } from '@/utils/smiles'
import classNames from 'classnames'
import './index.less'
import { useInchiSmilesToNameStore } from './store'

const nameToSvgDataString = (name: string, fontSize = 16): string => {
  const width = fontSize * name.length * 0.7
  const height = fontSize * 2
  const svgStr = `
    <svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}">
      <text x="50%" y="${fontSize}" font-family="Arial" font-size="${fontSize}px" fill="black" text-anchor="middle" dominant-baseline="middle">
        ${name}
      </text>
    </svg>`

  const encoded = encodeURIComponent(svgStr)
    .replace(/'/g, '%27')
    .replace(/"/g, '%22')

  return 'data:image/svg+xml;charset=utf-8,' + encoded
}

export interface ImageFromSrcsProps {
  srcs: string[]
  inchiSmiles: string[]
  width?: number
  height?: number
  maxHeight?: number
  maxWidth?: number
  fitContentHeight?: boolean
}

const ImageFromSrcs: React.FC<ImageFromSrcsProps> = ({
  srcs,
  inchiSmiles,
  height,
  width,
  maxHeight,
  maxWidth,
  fitContentHeight = srcs.length > 1
}) => {
  const [getMap, smilesToName] = useInchiSmilesToNameStore((s) => [
    s.getMap,
    s.inchiSmilesToName
  ])
  useEffect(() => {
    getMap()
  })

  if (srcs.length === 1) {
    const showName = smilesToName?.[inchiSmiles[0]]
    return (
      <div
        className={classNames('images-wrapper', {
          'fit-content-height': fitContentHeight
        })}
      >
        <Image
          src={showName?.length ? nameToSvgDataString(showName) : srcs[0]}
          placeholder={true}
          preview={false}
          wrapperClassName="img"
          height={height}
          width={width}
          style={{ maxHeight, maxWidth }}
          fallback="data:image/png;base64,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"
        />
      </div>
    )
  }
  return (
    <div
      className={classNames('images-wrapper', {
        'fit-content-height': fitContentHeight
      })}
    >
      {srcs.map((src, index) => {
        const showName = smilesToName?.[inchiSmiles[index]]
        return (
          <>
            <Image
              key={`${src}-${index}`}
              src={showName ? nameToSvgDataString(showName) : src}
              placeholder
              preview={false}
              wrapperClassName="img"
            />
            {index === srcs.length - 2 ? (
              <div className="icon" key={index}>
                <ToIcon />
              </div>
            ) : index < srcs.length - 2 ? (
              <div className="icon" key={index}>
                <AddIcon />
              </div>
            ) : null}
          </>
        )
      })}
    </div>
  )
}

export default ImageFromSrcs

const getFilename = async (smiles: string): Promise<string> =>
  (
    await axios.get(`${getEnvConfig().apiBase}/api/smiles/to-image/svg`, {
      params: { smiles }
    })
  )?.data?.filename || ''

const getSmilesFromRxn = (smiles: string): string[] => {
  if (smiles.includes('>')) {
    const { reactants, product } = getReactionFromRxn(smiles)
    return [...reactants, product]
  }
  return [smiles]
}

const getSrcs = async (
  smiles: string
): Promise<{ srcs: string[]; inchiSmiles: string[] }> => {
  const smileses = getSmilesFromRxn(smiles)
  const srcsPromise = Promise.all(
    smileses.map(async (s) => {
      const filename = await getFilename(s).catch(() => '')
      if (filename)
        return `${getEnvConfig().apiBase}/api/smiles/image-file/${filename}`
      return ''
    })
  )
  const inchifiedPromise = inchifySmiles(smileses)
  const [srcs, inchis] = await Promise.all([srcsPromise, inchifiedPromise])
  return { srcs, inchiSmiles: inchis }
}

export const useImageFromSmiles = (smiles: string) => {
  const [srcs, setSrcs] = useState<string[]>([])
  const [inchiSmiles, setInchiSmiles] = useState<string[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [invalid, setInvalid] = useState<boolean>(false)
  const counter = useRef<number>(0)

  useEffect(() => {
    if (!smiles) return

    setLoading(true)
    counter.current = counter.current + 1
    const curCounter = counter.current
    getSrcs(smiles)
      .then(({ srcs, inchiSmiles }) => {
        if (counter.current !== curCounter) return
        setSrcs(srcs)
        setInvalid(srcs.every((f) => !f))
        setInchiSmiles(inchiSmiles)
      })
      .finally(() => setLoading(false))
  }, [smiles])

  return { srcs, inchiSmiles, loading, invalid }
}
