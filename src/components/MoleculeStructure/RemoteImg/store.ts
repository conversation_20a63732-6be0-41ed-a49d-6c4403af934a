import { query } from '@/services/brain'
import { MaterialDisplayByName } from '@/services/brain/types/material-display-by-name'

import { create } from 'zustand'
import { combine, subscribeWithSelector } from 'zustand/middleware'

const fetchMaterialWithNames = async (): Promise<Record<string, string>> => {
  const { data } = await query<MaterialDisplayByName>(
    'material-display-by-names'
  )
    .paginate(1, 10000)
    .get()
  const ms = data?.length ? data : []
  const map = ms.reduce<Record<string, string>>((acc, cur) => {
    acc[cur.inchi_smiles] = cur.display_name
    return acc
  }, {})
  return map
}

interface State {
  inchiSmilesToName?: Record<string, string>
  fetching?: Promise<Record<string, string>>
}

const initState: State = {}

export const useInchiSmilesToNameStore = create(
  subscribeWithSelector(
    combine({ ...initState }, (set, get) => ({
      getMap: async () => {
        const cached = get().inchiSmilesToName
        if (cached === undefined) {
          const fetching = get().fetching
          if (!fetching) {
            const fetching = async () => {
              const map = await fetchMaterialWithNames()
              set((s) => ({
                ...s,
                inchiSmilesToName: map,
                fetching: undefined
              }))
              return map
            }
            const re = fetching()
            set((s) => ({ ...s, fetching: re }))
            return re
          }
          return fetching
        }
        return cached
      }
    }))
  )
)
