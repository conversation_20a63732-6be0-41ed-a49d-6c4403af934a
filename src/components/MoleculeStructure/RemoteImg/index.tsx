import { ReactComponent as CopyMaterialIcon } from '@/assets/svgs/route-operation/copy-material.svg'
import { ReactComponent as ZoomInIcon } from '@/assets/svgs/route-operation/zoom-in.svg'
import ModalBase from '@/components/ModalBase'
import { useModalBase } from '@/components/ModalBase/useModalBase'
import { MoleculeStructureProps } from '@/components/MoleculeStructure'
import { useCopyToClipboard } from '@/components/MoleculeStructure/util'
import { ExperimentOutlined } from '@ant-design/icons'
import { Skeleton } from 'antd'
import React, { useRef } from 'react'
import ImageFromSrcs, { useImageFromSmiles } from './ImageFromSrcs'

export interface RemoteMoleculeStructureProps extends MoleculeStructureProps {
  clickEvent?: () => void
  copyBtn?: boolean
  expandBtn?: boolean
  filename?: string
  loading?: boolean
}

const RemoteMoleculeStructure: React.FC<RemoteMoleculeStructureProps> = ({
  structure = '',
  clickEvent,
  copyBtn = true,
  expandBtn = true,
  fitContentHeight,
  className,
  height,
  width,
  maxHeight,
  maxWidth
}) => {
  const wrapperRef = useRef<HTMLDivElement | null>(null)
  const { dialogProps, confirm } = useModalBase()
  const { copy } = useCopyToClipboard()
  const { srcs, inchiSmiles, loading, invalid } = useImageFromSmiles(structure)

  return (
    <>
      <div
        title={structure}
        className={`molecule-structure-root ${className}`}
        onClick={clickEvent}
        ref={wrapperRef}
      >
        <div onClick={(e) => e.stopPropagation()} className="buttons-wrapper">
          {expandBtn && !invalid && (
            <div
              onClick={() => confirm()}
              className="expand-button-wrapper button-wrapper"
            >
              <ZoomInIcon />
            </div>
          )}
          {copyBtn && (
            <div
              onClick={() => copy(structure || '')}
              className="copy-button-wrapper button-wrapper"
            >
              <CopyMaterialIcon />
            </div>
          )}
        </div>
        {invalid && (
          <span title={`Cannot render structure: ${structure}`}>
            Render Error.
          </span>
        )}

        {loading || !srcs.length ? (
          <Skeleton.Node active rootClassName="skeleton-wrapper">
            <ExperimentOutlined style={{ fontSize: 40, color: '#bfbfbf' }} />
          </Skeleton.Node>
        ) : (
          <ImageFromSrcs
            srcs={srcs}
            inchiSmiles={inchiSmiles}
            width={width}
            height={height}
            maxHeight={maxHeight}
            maxWidth={maxWidth}
            fitContentHeight={fitContentHeight}
          />
        )}
      </div>

      <div onClick={(e) => e.stopPropagation()}>
        <ModalBase
          {...dialogProps}
          footer={null}
          cancelButtonProps={{ hidden: true }}
          width={structure?.includes('>') ? '80%' : 800}
          centered
        >
          <RemoteMoleculeStructure
            structure={structure}
            copyBtn={false}
            expandBtn={false}
          />
        </ModalBase>
      </div>
    </>
  )
}

export default RemoteMoleculeStructure
