.molecule-structure-root {
  position: relative;
  display: flex;
  flex: 0 0 auto;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: auto;

  &:hover .buttons-wrapper {
    display: flex;
  }

  .svg {
    width: 100% !important;
    height: 100% !important;
  }

  .skeleton-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
    .ant-skeleton-image {
      width: 100%;
      height: 100%;
    }
  }

  .buttons-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    display: none;
    gap: 2px;
    padding: 4px;
    .button-wrapper {
      position: relative;
      display: flex;
      width: 16px;
      height: 16px;
      cursor: pointer;

      svg > path {
        fill: #bfbfbf;
      }
      &:hover {
        svg > path {
          fill: #1a90ff;
        }
      }
    }
    .expand-button-wrapper {
      svg > path:last-child {
        stroke: #bfbfbf;
      }
      &:hover {
        svg > path:last-child {
          stroke: #1a90ff;
        }
      }
    }
  }
}

.expand {
  position: absolute;
  top: 5px;
  left: 5px;
}

.expand:hover {
  cursor: pointer !important;
}
