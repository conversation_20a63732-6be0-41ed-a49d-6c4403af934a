import RemoteMoleculeStructure from './RemoteImg'

export interface MoleculeStructureProps {
  structure: string
  className?: string
  width?: number
  height?: number
  fitContentHeight?: boolean
  maxWidth?: number
  maxHeight?: number
  clickEvent?: () => void
  copyBtn?: boolean
  expandBtn?: boolean
}

export { default as RemoteMoleculeStructure } from './RemoteImg'
export { default as RdKitMoleculeStructure } from './rdkit'
export { default as SmilesDrawerMoleculeStructure } from './smilesDrawer'

export default RemoteMoleculeStructure
