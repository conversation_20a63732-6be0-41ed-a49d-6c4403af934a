import { getWord } from '@/utils'
import { RDKitModule } from '@rdkit/rdkit'
import { App } from 'antd'
import copyToClipBoard from 'copy-to-clipboard'
export const initRDKit = (() => {
  let rdkitLoadingPromise: Promise<RDKitModule>

  return () => {
    /**
     * Utility function ensuring there's only one call made to load RDKit
     * It returns a promise with the resolved RDKit API as value on success,
     * and a rejected promise with the error on failure.
     *
     * The RDKit API is also attached to the global object on successful load.
     */
    if (!rdkitLoadingPromise) {
      rdkitLoadingPromise = new Promise((resolve, reject) => {
        window
          .initRDKitModule()
          .then((RDKit) => {
            window.RDKit = RDKit
            resolve(RDKit)
          })
          .catch(() => {
            reject()
          })
      })
    }

    return rdkitLoadingPromise
  }
})()

export const useCopyToClipboard = () => {
  const { message } = App.useApp()

  const copy = (text: string, info: string = getWord('copy-to-clipboard')) => {
    copyToClipBoard(text)
    message.success(info)
  }
  return { copy }
}
