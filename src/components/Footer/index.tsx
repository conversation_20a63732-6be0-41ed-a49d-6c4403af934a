import { DefaultFooter } from '@ant-design/pro-components'
import { useIntl } from '@umijs/max'
import React from 'react'

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear()
  const intl = useIntl()
  let linksData = [
    {
      key: 'c12',
      title: 'C12.ai',
      href: 'https://www.c12.ai/',
      blankTarget: true
    },
    {
      key: 'ICP',
      title: `${intl.formatMessage({
        id: 'beian'
      })}`,
      href: 'https://beian.miit.gov.cn/',
      blankTarget: true
    }
  ]
  return (
    <DefaultFooter
      style={{ background: 'none' }}
      copyright={`${currentYear} ${intl.formatMessage({ id: 'company' })}`}
      links={linksData}
    />
  )
}

export default Footer
