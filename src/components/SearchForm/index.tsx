import {
  But<PERSON>,
  <PERSON>box,
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Select,
  Space
} from 'antd'
import { isEmpty } from 'lodash'
import { useEffect, useRef, useState } from 'react'

import DateRangePicker from '@/components/date'
import { IOption } from '@/types/common'
import {
  ExportOutlined,
  FileAddOutlined,
  RollbackOutlined,
  SearchOutlined
} from '@ant-design/icons'
import type { IFormData, IQuery, SearchFormItemComponentType } from './index.d'

import { getWord } from '@/utils'
import styles from './index.less'
const timeStr = new Date().getTime()
export default function Query(props: IQuery) {
  const [form] = Form.useForm()
  const [showCollapse, setShowCollapse] = useState(false)
  const [_, setIsFold] = useState(false) // isFold
  const { initData } = props

  const ref: any = useRef(null)
  function submit() {
    form.validateFields().then((values) => props?.onSubmit(values))
  }

  function resize() {
    if (ref.current) {
      const height = ref.current.offsetHeight
      if (height > 128) {
        /* 高度超出展示收缩按钮 */
      } else if (Object.is(height, 128)) {
        setShowCollapse(false)
        setIsFold(false)
      }
    }
  }

  useEffect(() => {
    resize()
  }, [])

  useEffect(() => {
    resize()
  }, [props.formData])

  function renderFormItemComponent(
    type: SearchFormItemComponentType,
    key: string,
    enums?: IOption[],
    placeholder?: string,
    id?: string,
    showSearch?: boolean,
    mode?: 'multiple' | 'tags' | undefined,
    forbiddenDisabled?: boolean
  ) {
    switch (type) {
      case 'input':
        return (
          <Input
            onPressEnter={submit}
            allowClear
            placeholder={placeholder || getWord('input-tip')}
            name={`${key}_${timeStr}`}
            autoComplete="off"
          />
        )
      case 'textarea':
        return (
          <Input.TextArea
            id={id}
            placeholder={placeholder || getWord('input-tip')}
            allowClear
            autoSize={{ minRows: 1, maxRows: 3 }}
            name={`${key}_${timeStr}`}
          />
        )
      case 'date':
        return (
          <DateRangePicker
            forbiddenDisabled={forbiddenDisabled}
            placeholder={[
              getWord(
                'pages.searchTable.updateForm.schedulingPeriod.timeLabel'
              ),
              '结束时间'
            ]}
            format={'YYYY-MM-DD'}
          />
        )
      case 'showTimeDate':
        return (
          <DateRangePicker
            placeholder={[
              getWord(
                'pages.searchTable.updateForm.schedulingPeriod.timeLabel'
              ),
              '结束时间'
            ]}
            format={'YYYY-MM-DD'}
            showTime={true}
          />
        )
      case 'cascader':
        return (
          <DatePicker
            options={enums}
            placeholder="全部"
            changeOnSelect
            name={`${key}_${timeStr}`}
          />
        )
      case 'select':
        /* TODO better components 封装Select */
        return (
          <Select
            allowClear
            onChange={(value: string) =>
              props?.selectChange && props?.selectChange(value, key)
            }
            getPopupContainer={(triggerNode) => triggerNode.parentElement}
            options={enums as { label: string; value: string }[]}
            placeholder={placeholder || getWord('select-tip')}
            showArrow
            mode={mode}
            showSearch={showSearch}
            filterOption={(input, option) =>
              option.label
                .toString()
                .toLowerCase()
                .indexOf(input.trim().toLowerCase()) >= 0
            }
          />
        )
      case 'checkbox':
        return (
          <Checkbox.Group
            options={enums as { label: string; value: string }[]}
            name={`${key}_${timeStr}`}
          />
        )
      default:
        return null
    }
  }

  function renderSearchFormItem(items: IFormData[]) {
    return items.map((item) => {
      return item?.hidden ? (
        ''
      ) : (
        <Col
          lg={{ span: item?.LG?.col || 6, offset: item?.LG?.offset || 0 }}
          xl={{ span: item?.XL?.col || 6, offset: item?.XL?.offset || 0 }}
          xxl={{ span: item?.XXL?.col || 6, offset: item?.XXL?.offset || 0 }}
          key={item.key}
        >
          <Form.Item
            name={item.key}
            label={item.label}
            labelCol={{
              lg: item?.LG?.labelWidth || 4,
              xl: item?.XL?.labelWidth || 8,
              xxl: item?.XXL?.labelWidth || 8
            }}
            wrapperCol={{
              lg: item?.LG?.wrapperWidth || 20,
              xl: item?.XL?.wrapperWidth || 16,
              xxl: item?.XXL?.wrapperWidth || 16
            }}
            rules={[{ required: item.required, message: `${item.label}必填` }]}
          >
            {renderFormItemComponent(
              item.ctype,
              item.key,
              item.enums,
              item?.placeholder,
              item?.id,
              item?.showSearch,
              item?.mode,
              item?.forbiddenDisabled
            )}
          </Form.Item>
        </Col>
      )
    })
  }

  const reset = () => {
    form.resetFields()
    if (initData) {
      form.setFieldsValue(initData)
    }
    props?.onReset()
  }

  return (
    <div
      className={styles.SearchForm}
      ref={ref}
      style={{ marginBottom: showCollapse ? '0px' : '20px' }}
    >
      <Form form={form} initialValues={initData}>
        <div
          // TODO better styels 面板折叠收起 className={cs(styles['SearchFomFieldWrapper'], {
          //   [styles['SearchFormFieldWrapper_collapse']]: showCollapse && !isFold
          // })}
          style={{ ...styles }}
        >
          <Row gutter={[0, 24]}>
            {renderSearchFormItem(props.formData)}
            <div className="query-button" style={{ marginTop: '-7px' }}>
              <Space>
                {props.onHandleExportFile && (
                  <Button
                    onClick={props.onHandleExportFile}
                    loading={props?.isEport}
                    icon={<ExportOutlined />}
                    style={{ backgroundColor: 'orange', color: '#FFFFFF' }}
                    shape="round"
                  >
                    {props?.isEport ? '正在导出' : '导出Excel'}
                  </Button>
                )}
                {!isEmpty(props?.btnGroupsConfig) &&
                  (
                    props.btnGroupsConfig as {
                      clickFn: () => void
                      text: string
                      hidden?: boolean
                    }[]
                  ).map((item, index: number) => {
                    if (item.hidden) return null
                    return (
                      <Button
                        shape="round"
                        icon={<FileAddOutlined />}
                        onClick={item.clickFn}
                        key={`${item?.text}-${index}`}
                      >
                        {item?.text}
                      </Button>
                    )
                  })}
                <Button
                  icon={<SearchOutlined />}
                  shape="round"
                  type="primary"
                  onClick={submit}
                >
                  {getWord('Search')}
                </Button>
                <Button
                  shape="round"
                  type="primary"
                  danger
                  icon={<RollbackOutlined />}
                  onClick={reset}
                >
                  {getWord('reset')}
                </Button>
              </Space>
            </div>
          </Row>
        </div>
      </Form>
      {/*  TODO better styels 超长折叠收起效果  */}
      {/* {showCollapse && (
        <section
          className={styles.unfold}
          style={{ transform: isFold ? 'rotatex(180deg)' : 'rotateX(0)' }}
        >
          <div onClick={() => setIsFold(!isFold)}>
            <DownOutlined />
          </div>
        </section>
      )} */}
    </div>
  )
}
