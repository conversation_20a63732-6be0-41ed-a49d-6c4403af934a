import { IOption } from '@/types/common'
export interface IQuery {
  formData: any
  onHandleExportFile?: () => void
  Add?: () => void
  onReset: () => void
  isEport?: boolean
  onSubmit: Function
  selectChange?: (value: string, key: string) => void
  initData?: any
  style?: any
  addText?: string
  btnGroupsConfig?: { clickFn: () => void; text: string }[]
}
export type SearchFormItemComponentType =
  | 'input'
  | 'select'
  | 'textarea'
  | 'date'
  | 'showTimeDate'
  | 'cascader'
  | 'checkbox'

interface ICol {
  col: number
  labelWidth: number
  wrapperWidth: number
}

export interface IFormData {
  label: string
  ctype: SearchFormItemComponentType
  key: string
  enums?: IOption[]
  placeholder?: string
  id?: string
  hidden?: boolean
  XL: ICol
  showSearch?: boolean
  mode?: 'multiple' | 'tags' | undefined
  forbiddenDisabled?: booealn // 是否禁用部分选择时段
}

export interface ISearchForm {
  formData: IFormData[]
  onSubmit: () => void
  onReset: () => void
  initalData?: any
  col?: number
  offset?: number
  LC_xxl?: number
  WC_xxl?: number
}
