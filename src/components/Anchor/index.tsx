import { Anchor } from 'antd'
import cs from 'classnames'
import styles from './index.less'

interface IAnchor {
  key: string
  href: string
  title: string
}

interface CustomAnchorProps {
  items: IAnchor[]
  wrapClassName?: any
}

export default function CustomAnchor(props: CustomAnchorProps) {
  return (
    <Anchor
      onClick={(e, link) => location.replace(link.href)}
      className={cs(styles.anchor, props?.wrapClassName)}
      affix={false}
      items={props?.items}
    />
  )
}
