import { getWord, isEN } from '@/utils'
import { CopyrightOutlined } from '@ant-design/icons'
import { Layout } from 'antd'
import cs from 'classnames'
import dayjs from 'dayjs'
import styles from './index.less'

export default function GlobalFooter() {
  const { Footer } = Layout

  const link: string = 'https://www.c12.ai/'
  return (
    <Footer className={styles.globalFooter}>
      <div className={styles.footer}>
        {isEN() ? `${getWord('copyright')} ` : ''}
        <CopyrightOutlined />
        &nbsp;{dayjs().year()} &nbsp;
        <a
          onClick={(e) => {
            if (link) window.open(link)
            else e.preventDefault()
          }}
        >
          {getWord('company')}
        </a>
        {!isEN() ? ` ${getWord('copyright')}` : ''}
        {!isEN() ? (
          <>
            &nbsp;| &nbsp;
            <a
              className={cs(styles.filing)}
              onClick={() => window.open('https://beian.miit.gov.cn/')}
            >
              {getWord('beian')}
            </a>
          </>
        ) : (
          ''
        )}
      </div>
    </Footer>
  )
}
