import { SyntheticLink } from '@/types/SyntheticRoute/SyntheticLink'
import message from '@/utils/message'
import React from 'react'
import Arrow from '../Arrow'
import SmilesDrawerMoleculeStructure from '../MoleculeStructure'
import RdKitMoleculeStructure from '../MoleculeStructure/rdkit'

const unsecuredCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  try {
    document.execCommand('copy')
  } catch (err) {
    console.error('Unable to copy to clipboard', err)
  }
  document.body.removeChild(textArea)
}

export type SyntheticRouteLinkNodeCompProps = {
  node: SyntheticLink
  display?: string
}

const SyntheticRouteLinkNodeComp: React.FC<SyntheticRouteLinkNodeCompProps> = ({
  node: { value, child, rxn },
  display = 'smilesDrawer'
}) => {
  return (
    <>
      {display === 'rdKit' ? (
        <RdKitMoleculeStructure structure={value} />
      ) : (
        <div
          onDoubleClick={() => {
            unsecuredCopyToClipboard(value)
            message.info({
              message: `Molecule smiles copied (${value})`,
              description: ''
            })
          }}
          style={{ width: 200, height: 200 }}
        >
          <SmilesDrawerMoleculeStructure structure={value} />
        </div>
      )}
      {child && (
        <>
          <div
            title={rxn}
            onDoubleClick={() => {
              unsecuredCopyToClipboard(rxn || '')
              message.info({
                message: `Reaction smiles copied (${rxn})`,
                description: ''
              })
            }}
          >
            <Arrow />
          </div>
          <SyntheticRouteLinkNodeComp node={child} display={display} />
        </>
      )}
    </>
  )
}

export default SyntheticRouteLinkNodeComp
