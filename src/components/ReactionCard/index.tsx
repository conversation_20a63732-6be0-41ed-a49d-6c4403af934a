import LazySmileDrawer from '@/components/LazySmileDrawer'
import { ProjectRoute } from '@/services/brain'
import { formatYMDHMTime, getWord, isValidArray } from '@/utils'
import { MessageOutlined } from '@ant-design/icons'
import { Button, Card, Popover, Tag } from 'antd'
import cs from 'classnames'
import { history, useParams } from 'umi'
import type { ReactionCardProps } from './index.d'
import styles from './index.less'

export default function ReactionCard(props: ReactionCardProps) {
  const { reaction, isPlayground, onAction, enableToReaction, displayProject } =
    props

  const renderSolvents = (curValue: ProjectRoute[]) => {
    const routesList: string[] = []
    curValue.map((item: ProjectRoute) => routesList.push(item?.id))
    return routesList
  }
  const { id: projectId } = useParams<{
    id: string
    compoundId: string
  }>()
  const hasCommendCount =
    reaction?.commendCount && Number(reaction?.commendCount) > 0
  const des: string = hasCommendCount
    ? `${getWord('comment')}（${reaction?.commendCount}）`
    : getWord('comment')
  return (
    <Card className={styles.reactionCard}>
      {reaction?.reaction ? (
        <LazySmileDrawer
          structure={reaction?.reaction}
          className={cs(styles.structure, 'enablePointer')}
          clickEvent={
            enableToReaction
              ? () => {
                  history.push(
                    `/projects/${projectId || reaction.project?.id}/reaction/${
                      reaction?.id
                    }`
                  )
                }
              : undefined
          }
        />
      ) : (
        ''
      )}
      <div className={styles.reactionInfo}>
        <div className={cs(styles.reactionTitle, 'flex-justify-space-between')}>
          <div className={cs(styles.no, 'flex-justify-space-between')}>
            <span className={styles.title}>
              {getWord('reaction-no')}
              {reaction?.id}
            </span>
            {isPlayground && hasCommendCount && reaction?.updatedAt ? (
              <div>
                &nbsp;&nbsp;
                {getWord('last-comment-date')}:{' '}
                {formatYMDHMTime(reaction?.updatedAt)}
              </div>
            ) : (
              ''
            )}
            {reaction?.collection_subject ? (
              <div>
                &nbsp;&nbsp;
                {getWord('reaction-step-ID')}: {reaction?.collection_subject}
              </div>
            ) : (
              ''
            )}
          </div>
          <div className={styles.status}>
            {!isPlayground ? (
              <>
                {reaction?.progress ? (
                  <Tag color="processing">{getWord('proceeded')}</Tag>
                ) : (
                  <Tag color="warning">{getWord('to-be-proceeded')}</Tag>
                )}
              </>
            ) : (
              ''
            )}
            {isPlayground ? (
              <Button type="link" onClick={onAction}>
                <Popover content={des}>
                  <MessageOutlined />
                  {hasCommendCount ? `（${reaction?.commendCount}）` : ''}
                </Popover>
              </Button>
            ) : (
              ''
            )}
          </div>
        </div>
        <div className="display-flex">
          <div>
            {getWord('nums-of-my-reactions')}：
            {reaction?.effective_procedures?.length ? (
              <span
                className="enablePointer"
                onClick={() =>
                  history.push(
                    `/projects/${projectId || reaction.project?.id}/reaction/${
                      reaction?.id
                    }?tab=my-reaction-design`
                  )
                }
              >
                {reaction?.effective_procedures?.length}
              </span>
            ) : (
              0
            )}
          </div>
          &nbsp;&nbsp;&nbsp;
          <div>
            {getWord('nums-of-my-experiments')}：
            {reaction?.experiment_count ? (
              <span
                className="enablePointer"
                style={{ color: 'black' }}
                onClick={() =>
                  history.push(
                    `/projects/${projectId || reaction.project?.id}/reaction/${
                      reaction?.id
                    }?tab=my-experiment`
                  )
                }
              >
                {reaction?.experiment_count}
              </span>
            ) : (
              0
            )}
          </div>
        </div>
        {isPlayground ? (
          ''
        ) : (
          <>
            {displayProject && (
              <div className="flex-align-items-center">
                <div>{getWord('menu.list.project-list')}：</div>
                <div style={{ color: 'black' }}>{reaction.project?.no}</div>
              </div>
            )}
            <div className="display-flex">
              {getWord('associated-routes')}：
              <span className={styles.routes}>
                {isValidArray(reaction?.project_routes)
                  ? renderSolvents(reaction?.project_routes).join('、')
                  : '无'}
              </span>
            </div>
          </>
        )}
      </div>
    </Card>
  )
}
