@import '@/style/variables.less';
.reactionCard {
  height: auto;
  min-height: 168px;
  margin: 4px;
  .reactionInfo {
    padding: 0px 16px 8px;
    color: #626262;
    .reactionTitle {
      width: 100%;
      height: 26px;
      line-height: 26px;
      .no {
        .title {
          height: 24px;
          color: black;
        }
      }
      .status {
        width: auto;
        min-width: 48px;
      }
    }
    .routes {
      margin-left: 5px;
      color: @fill-apply;
    }
    :global {
      .ant-card-body {
        padding: 10px 20px !important;
      }
    }
  }
  .structure {
    width: auto;
    height: 110px;
  }
}
