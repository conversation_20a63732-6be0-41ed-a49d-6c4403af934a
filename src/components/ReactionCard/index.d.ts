import type { ProjectReaction } from '@/services/brain'
import type { Dayjs } from 'Dayjs'
interface newReaction extends ProjectReaction {
  commendCount?: number
  updatedAt?: Dayjs
  project_routes?: string[]
  collection_subject?: string
}

export interface ReactionCardProps {
  reaction: newReaction
  isPlayground?: boolean
  onAction?: () => void
  enableToReaction?: boolean
  displayProject?: boolean
}
