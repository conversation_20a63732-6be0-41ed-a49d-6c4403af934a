import { getEnvConfig } from '@/utils'
import axios from 'axios'

const windowConfig = {
  width: 1000,
  height: 700,
  top: 10,
  left: 10,
  scrollbars: 'no',
  resizable: 'no',
  menubar: 'no',
  toolbar: 'no',
  location: 'no',
  status: 'no',
  directories: 'no'
}

const parseWindowFeatures = (
  config: Record<string, string | number>
): string => {
  return Object.entries(config)
    .map(([key, value]) => `${key}=${value}`)
    .join(',')
}

let openedWindow: WindowProxy | null = null

export const openSmallWindow = (url: string, name: string) => {
  if (openedWindow && !openedWindow.closed) {
    return
  }
  openedWindow = window.open(url, name, parseWindowFeatures(windowConfig))
}

interface AiSession {
  session_id: string
  status_code: number
  initialize_finished: boolean
  msg: string
}

export const getAiSession = async (data: {
  project_id: number | string
  compound_id?: number | string
  retro_id?: number | string
  simple_rxn?: string
  rxns?: string[]
}): Promise<AiSession> => {
  return (
    await axios.post(
      `${getEnvConfig().apiBase}/api/ai-analyze/create-session`,
      data
    )
  ).data
}
