import { ReactComponent as LabBotIcon } from '@/assets/svgs/navbar/labbot.svg'
import useRetroHistory from '@/pages/compound/components/SearchHistory/useRetroHistory'
import {
  fetchAllRetroReactions,
  getReactionFromRxn
} from '@/pages/reaction/util'
import { getRxnFromReaction } from '@/pages/route/util'
import { ProjectReaction, service } from '@/services/brain'
import { getEnvConfig } from '@/utils'
import { useParams } from '@umijs/max'
import cs from 'classnames'
import { useModel } from 'umi'
import styles from './index.less'
import { getAiSession, openSmallWindow } from './utils'
import { useWs } from './ws'

const fetchRxnAndRetroRxns = async (
  reactionId: string
): Promise<[string, string[]]> => {
  const { data } = await service<ProjectReaction>('project-reactions')
    .selectManyByID([reactionId])
    .get()
  if (!data?.length) return ['', []]
  const reaction = getReactionFromRxn(data[0].reaction)
  const rxns = (
    await fetchAllRetroReactions(reaction, false, ['reactants', 'product'])
  )?.map((r) => getRxnFromReaction(r))
  return [getRxnFromReaction(reaction), rxns]
}

const getAiSessionId = async (data: {
  project_id: number | string
  compound_id?: number | string
  retro_id?: number | string
  simple_rxn?: string
  rxns?: string[]
}): Promise<string> => {
  return (await getAiSession(data)).session_id
}

export default function Labbot() {
  const { initialState } = useModel('@@initialState')
  const {
    id: project_id,
    compoundId,
    reactionId
  } = useParams<{
    id: string
    compoundId: string
    reactionId: string
  }>()
  const { selected: { id: retroId } = {} } = useRetroHistory(compoundId)

  const { botDomain } = getEnvConfig()
  const botBaseUrl = botDomain?.length ? `https://${botDomain}` : ''

  const openLabot = async () => {
    let sessionId: string = '',
      type: 'reaction' | 'route' = 'route'
    if (retroId && project_id && compoundId) {
      type = 'route'
      sessionId = await getAiSessionId({
        project_id,
        compound_id: compoundId,
        retro_id: retroId
      })
    } else if (project_id && reactionId) {
      type = 'reaction'
      const [simple_rxn, rxns] = await fetchRxnAndRetroRxns(reactionId)
      sessionId = await getAiSessionId({ project_id, simple_rxn, rxns })
    }

    if (sessionId) {
      openSmallWindow(
        `${botBaseUrl}/text-only?sessionId=${sessionId}&type=${type}`,
        'Labbot'
      )
    }
  }

  useWs({
    onNewSession: (sessionId: string) => {
      openSmallWindow(`${botBaseUrl}/${sessionId}`, 'Labbot')
    },
    onOpenUrl: (url: string) => {
      window.location.href = url
    }
  })

  return (
    <div
      className={cs(styles.labbotButton, {
        [styles.collapsed]: initialState?.isMenuCollapsed
      })}
    >
      <div
        onClick={openLabot}
        className={cs(styles.svgWrapper, {
          [styles.collapsedItem]: initialState?.isMenuCollapsed
        })}
      >
        <LabBotIcon />
      </div>
    </div>
  )
}
