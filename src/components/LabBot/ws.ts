import { getEnvConfig } from '@/utils'
import { useEffect, useRef } from 'react'
import useWebSocket from 'react-use-websocket'
import { Message } from './type'

interface UseWsProps {
  onNewSession?: (s: string) => void
  onOpenUrl?: (url: string) => void
}

const { botDomain } = getEnvConfig()
const domain = botDomain?.length ? botDomain : location.origin
const WS_URL = `wss://${domain}/ws`

export const useWs = ({ onNewSession, onOpenUrl }: UseWsProps) => {
  const sessionIdSet = useRef<Set<string>>(new Set())
  const { lastMessage } = useWebSocket<Message>(WS_URL, {
    onOpen: () => console.log('WebSocket connection opened'),
    onClose: () => console.log('WebSocket connection closed'),
    onError: (e) => console.log('WebSocket error', e)
  })

  useEffect(() => {
    if (!lastMessage) {
      return
    }

    const data: Message = JSON.parse(lastMessage.data)
    if (!sessionIdSet.current.has(data.session_id)) {
      if (data.role === 'user' && data.new_session) {
        sessionIdSet.current.add(data.session_id)
        onNewSession?.(data.session_id)
        return
      }
      return
    }

    if (data.role === 'bot' && data.jump_url) {
      onOpenUrl?.(data.jump_url)
      return
    }
  }, [lastMessage])
}
