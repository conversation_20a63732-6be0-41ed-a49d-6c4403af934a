interface UserMessage {
  role: 'user'
  text: string
  session_id: string
  new_session?: boolean
}

interface BotMessage {
  role: 'bot'

  session_id: string
  // 一个消息气泡有唯一id
  message_id: string
  // 这段话在气泡中的顺序，从0开始
  order: number
  // 这个消息是否是这个气泡的最后一段话
  finished: boolean
  // 消息的文本
  text: string
  // 消息的语音链接
  audio_url: string

  // 文本播放时长
  display_time_in_ms?: number
  // 消息播放延迟，在服务端收到该消息后延迟对应ms后再发送到客户端
  delay_time_in_ms?: number

  // 以下属性仅在`order == 0`时生效

  // 需要brain跳转的url
  jump_url?: string
  // 在气泡结束后，紧跟一个气泡直接显示的文本
  direct?: string
  // 在气泡结束后，bot进入忙碌状态
  busy?: string
}

export type Message = BotMessage | UserMessage
