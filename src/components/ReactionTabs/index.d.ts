import { MaterialTable } from '@/services/brain'
import { Material } from '@/types/models'
import type { ReactElement } from 'react'
export interface ExperimentMaterialTableItem extends MaterialTable {
  weight: number
  weight_unit: 'mg' | 'g'
}

export interface ExperimentCreateParams {
  project_no: string
  project_reaction_id: number
  experiment_design_no: string
  name?: string
  priority: 'P0' | 'P1' | 'P2'
  material_table: ExperimentMaterialTableItem[]
  start_type: 'auto' | 'asap' | 'custom'
  earliest_start_time?: string
  latest_start_time?: string
  experiment_type: 'test' | 'scale_up'
  experiment_owner: number
}

export interface EditExperimentModalProps {
  projectId?: number
  projectReactionId?: number
  experiementDesignNo?: string
  open?: boolean
  setOpen?: (open: boolean) => void
  materialTable?: Material[]
  triggerCom?: ReactElement
  experiment?: ExperimentCreateParams & { id?: number }
  onSuccess?: () => void
}
