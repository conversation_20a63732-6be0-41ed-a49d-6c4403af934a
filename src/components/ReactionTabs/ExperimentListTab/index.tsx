import { useModalBase } from '@/components/ModalBase/useModalBase'
import ReagentList from '@/components/ReagentList'
import StatusRender from '@/components/StatusRender'
import { priorityOptions } from '@/constants'
import { useFormStorage } from '@/hooks/useFormStorage'
import { useProjectMembers } from '@/hooks/useProjectMembers'
import StatusUpdateModel from '@/pages/projects/components/StatusUpdateModel'
import { getProjectOptions } from '@/pages/workspace/utils'
import {
  apiExperimentList,
  apiExperimentPlanDetail,
  apiUpdateExperimentPlan,
  apiUpdateExperimentStatus,
  parseResponseResult
} from '@/services'
import { MaterialTable } from '@/services/brain'
import {
  encodeString,
  getWord,
  isEN,
  isReactionDetail,
  reloadEvent
} from '@/utils'
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps
} from '@ant-design/pro-components'
import { history, useModel, useParams, useSearchParams } from '@umijs/max'
import { App } from 'antd'
import cs from 'classnames'
import { isEmpty, isNil, mapValues, omitBy } from 'lodash'
import React, { ReactElement, useRef, useState } from 'react'
import EditExperimentModal from '../EditExperimentModal'
import TabWithNumber from '../TabWithNumber'
import type { ExperimentCreateParams } from '../index.d'
import type { ExperimentListTabProps } from './index.d'
import './index.less'
import styles from './index.less'
import { Experiment, ExperimentStatus, experimentStatus } from './util'

const columns: ProColumns<Experiment>[] = [
  {
    title: getWord('pages.experiment.label.name'),
    sorter: true,
    width: 96,
    dataIndex: 'experiment_name',
    fixed: 'left'
  },
  {
    title: getWord('pages.experiment.label.no'),
    sorter: true,
    width: 100,
    dataIndex: 'experiment_no',
    fixed: 'left'
  },
  {
    title: getWord('pages.experiment.label.type'),
    sorter: true,
    width: 100,
    dataIndex: 'experiment_type',
    valueEnum: {
      test: getWord('pages.experimentDesign.label.test'),
      scale_up: getWord('pages.experimentDesign.label.scale_up')
    }
  },
  {
    title: getWord('pages.experiment.label.experimentDesignName'),
    sorter: true,
    dataIndex: 'design_name',
    width: 100,
    render: (dom, entity) => {
      if (!entity?.design_id) return dom
      return (
        <a
          onClick={() => {
            history.push(
              `/projects/${entity?.project_no}/reaction/${
                entity?.project_reaction_id
              }/experimental-procedure/detail/${encodeString(
                JSON.stringify(entity?.design_id)
              )}?type=editor`
            )
          }}
        >
          {dom}
        </a>
      )
    }
  },
  {
    title: getWord('pages.experiment.label.status'),
    sorter: true,
    dataIndex: 'status',
    width: 90,
    valueType: 'checkbox',
    valueEnum: experimentStatus.reduce<Record<string, ReactElement>>(
      (acc, cur) => {
        acc[cur] = getWord(`pages.experiment.statusLabel.${cur}`)
        return acc
      },
      {}
    ),
    initialValue: experimentStatus.filter((s) => s !== 'canceled'),
    render: (_, item) => {
      return (
        <StatusRender
          status={item.status}
          labelPrefix="pages.experiment.statusLabel"
        />
      )
    }
  },
  {
    title: getWord('pages.experiment.label.priority'),
    sorter: true,
    dataIndex: 'priority',
    width: 100,
    valueEnum: priorityOptions.reduce<Record<string, string>>((acc, cur) => {
      acc[cur.value] = cur.label
      return acc
    }, {})
  },
  {
    title: getWord('pages.experiment.label.actualStartTime'),
    sorter: true,
    dataIndex: 'start_time',
    width: 100,
    valueType: 'date',
    hideInSearch: true
  },
  {
    title: getWord('pages.experiment.label.actualStartTime'),
    sorter: true,
    dataIndex: 'start_time',
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (values) => ({
        start_time: values.map((v: string) => `${v} 00:00:00`)
      })
    }
  },

  {
    title: getWord('pages.experiment.label.actualEndTime'),
    sorter: true,
    dataIndex: 'end_time',
    valueType: 'date',
    width: 100,
    hideInSearch: true
  },
  {
    title: getWord('pages.experiment.label.actualEndTime'),
    sorter: true,
    dataIndex: 'end_time',
    valueType: 'dateRange',
    width: 120,
    search: {
      transform: (values) => ({
        end_time: values.map((v: string) => `${v} 00:00:00`)
      })
    },
    hideInTable: true
  }
]

const ignoreFields = [
  'current',
  'pageSize',
  'project_reaction_id',
  'project_id'
]
const workbenchStatus = ['created', 'running', 'completed'] as const

const experimentActions = [
  'view',
  'edit',
  'cancel',
  'resume',
  'viewConclusion',
  'conclusion',
  'detectRecord'
] as const
type ExperimentAction = (typeof experimentActions)[number]
const statusActionMap: Record<ExperimentStatus, ExperimentAction[]> = {
  created: ['edit', 'cancel'],
  canceled: [],
  running: ['view', 'detectRecord'],
  hold: ['view', 'resume', 'detectRecord'],
  completed: ['view', 'viewConclusion', 'detectRecord'],
  success: ['view', 'viewConclusion'],
  failed: ['view', 'viewConclusion']
}

const operationColumn = (
  handlers: Record<ExperimentAction, (item: Experiment) => void>
): ProColumns<Experiment> => {
  return {
    title: getWord('pages.experiment.label.operation'),
    dataIndex: 'operation',
    valueType: 'option',
    fixed: 'right',
    width: 126,
    render: (_, item) => {
      return (
        <>
          {statusActionMap[item.status]?.map((key) => {
            return (
              <>
                <a key={key} onClick={() => handlers[key]?.(item)}>
                  {getWord(`pages.experiment.label.operation.${key}`)}
                </a>
                &nbsp;
              </>
            )
          })}
          {item.status !== 'created' ? (
            <>
              &nbsp;
              <a
                key="material-sheet"
                onClick={() => handlers['conclusion']?.(item)}
              >
                {getWord('pages.reaction.label.material-sheet')}
              </a>
            </>
          ) : (
            ''
          )}
        </>
      )
    }
  }
}

const ExperimentListTab: React.FC<ExperimentListTabProps> = ({
  projectId,
  projectReactionId,
  ownerId,
  isWorkbench
}) => {
  const { modal } = App.useApp()
  const [open, setOpen] = useState<boolean>(false)
  const [update, setUpdate] = useState<{
    status: string
    update: (reason?: string) => Promise<void>
  }>()
  const [projectIds, setProjectIds] = useState<number[]>([])
  const { members } = useProjectMembers(projectId ? projectId : projectIds)
  const ref = useRef<ActionType>()
  const [get, set] = useFormStorage({ suffix: 'experimentList' })
  const [editingExperiment, setEditingExperiment] = useState<
    ExperimentCreateParams & { id: number }
  >()
  const request: ProTableProps<
    Experiment,
    {
      project_reaction_id?: number
      project_id?: number
      filter_project_id?: string
    }
  >['request'] = async (params, sort) => {
    const filterParams = omitBy(
      params,
      (v, k) => ignoreFields.includes(k) || isNil(v)
    )

    const res = await apiExperimentList({
      data: {
        ...(isWorkbench ? { status: workbenchStatus } : filterParams),
        filter_by:
          params?.project_reaction_id ||
          params?.project_id ||
          params?.filter_project_id
            ? {
                project_reaction_id: params?.project_reaction_id,
                project_id: params?.project_id || params?.filter_project_id
              }
            : undefined,
        order_by: sort
          ? mapValues(sort, (v) => (v === 'ascend' ? 'asc' : 'desc'))
          : { created_date: 'desc' },
        page_no: params.current,
        page_size: params.pageSize
      }
    })
    if (parseResponseResult(res).ok) {
      if (!projectId) {
        setProjectIds(
          res.data.data
            .map((d: Experiment) => Number.parseInt(d.project_no))
            .filter((no: number) => !Number.isNaN(no))
        )
      }
      return {
        data: isWorkbench ? res.data.data.splice(0, 6) : res.data.data,
        success: true,
        total: res.data.meta.total
      }
    }
    return { success: false }
  }

  const [editReactionId, setEditReactionId] = useState<number>()
  const updateStatus = async (
    item: Experiment,
    newStatus: ExperimentStatus,
    needConfirm?: string
  ) => {
    const update = async (cancel_reason?: string) => {
      await (needConfirm === 'cancel'
        ? apiUpdateExperimentPlan({
            data: {
              id: item.experiment_plan_id,
              status: newStatus,
              cancel_reason
            }
          })
        : apiUpdateExperimentStatus({
            data: { experiment_no: item.experiment_no, status: newStatus }
          }))
      ref.current?.reload()
    }
    if (needConfirm === 'cancel') {
      setUpdate({ update, status: newStatus })
    } else if (needConfirm) {
      modal.confirm({
        title: (
          <>
            {getWord('pages.route.edit.label.confirm')}
            {getWord(`pages.experiment.label.operation.${needConfirm}`)}
            {getWord('pages.experiment？')}
          </>
        ),
        onOk: update
      })
    } else {
      await update()
    }
  }

  const { id, reactionId } = useParams()
  const { fetchConclusion, conclusion } = useModel('conclusion')
  const { dialogProps, confirm } = useModalBase()
  const [_, setSearchParams] = useSearchParams()
  const handlers: Record<ExperimentAction, (item: Experiment) => void> = {
    view: (item: Experiment) => {
      history.push(
        `/projects/${item.project_no || id}/reaction/${
          item.project_reaction_id || reactionId
        }/experiment-execute/detail/${encodeString(
          JSON.stringify(item.experiment_no)
        )}`
      )
    },
    edit: async (item: Experiment) => {
      const res = await apiExperimentPlanDetail({
        routeParams: `${item.experiment_plan_id}`
      })
      if (!parseResponseResult(res).ok) return
      setEditReactionId(item?.project_reaction_id)
      setEditingExperiment({ ...res.data, id: item.experiment_plan_id })
      setOpen(true)
    },
    cancel: (item: Experiment) => updateStatus(item, 'canceled', 'cancel'),
    resume: (item: Experiment) => updateStatus(item, 'running', 'resume'),
    viewConclusion: (item: Experiment) =>
      history.push(
        `/projects/${item.project_no || projectId || id}/reaction/${
          item.project_reaction_id || reactionId
        }/experimental-procedure/conclusion/${encodeString(
          JSON.stringify(item.experiment_no)
        )}${['completed', 'failed'].includes(item.status) ? '#conclusion' : ''}`
      ),
    conclusion: (item: Experiment) => {
      fetchConclusion(item?.experiment_no)
      confirm()
    },
    detectRecord: (item: Experiment) => {
      if (isReactionDetail()) {
        setSearchParams(
          { tab: 'detect-record', experimentNo: item?.experiment_no as string },
          { replace: true }
        )
        reloadEvent()
      } else {
        window.open(
          `${window.location.origin}/projects/${item?.project_no}/reaction/${
            item?.project_reaction_id
          }?tab=detect-record&experimentNo=${encodeURIComponent(
            item?.experiment_no
          )}`
        )
      }
    }
  }

  const projectColumns: ProColumns<Experiment>[] = [
    {
      title: getWord('project-ID'),
      hideInTable: true,
      valueType: 'select',
      dataIndex: 'filter_project_id',
      request: ({ userId }) => getProjectOptions(userId),
      params: { userId: ownerId },
      debounceTime: 300,
      fieldProps: { onClick: (e) => e.stopPropagation() }
    },
    {
      title: getWord('project-ID'),
      hideInSearch: true,
      valueType: 'select',
      dataIndex: 'project_no',
      width: 120,
      request: ({ userId }) => getProjectOptions(userId, true),
      params: { userId: ownerId },
      debounceTime: 300,
      fieldProps: { onClick: (e) => e.stopPropagation() }
    }
  ]

  const owerColumn: ProColumns<Experiment> = {
    title: getWord('pages.experiment.label.personInCharge'),
    sorter: true,
    hideInSearch: !!ownerId,
    width: 100,
    dataIndex: 'experiment_owner',
    valueType: 'select',
    valueEnum: members.reduce<Record<string, string>>((acc, cur) => {
      acc[cur.value] = cur.label
      return acc
    }, {})
  }

  const displayColumns: ProColumns<Experiment, 'text'>[] = []
  if (!projectId && ownerId) displayColumns.push(...projectColumns)
  displayColumns.push(...columns, owerColumn, operationColumn(handlers))
  const { initialState } = useModel('@@initialState')

  return (
    <>
      <div
        className={cs({
          [styles['workbench_fold']]:
            isWorkbench && initialState?.isMenuCollapsed,
          [styles['workbench_unFold']]:
            isWorkbench && !initialState?.isMenuCollapsed
        })}
      >
        <ProTable<Experiment>
          className="experiment-list-root"
          request={request}
          actionRef={ref}
          rowKey="id"
          scroll={{ x: isWorkbench ? 650 : undefined }}
          form={{
            onValuesChange: async (_, values) => set(values),
            initialValues: {
              status: experimentStatus.filter((s) => s !== 'canceled'),
              ...get()
            }
          }}
          search={isWorkbench ? false : { labelWidth: isEN() ? 148 : 120 }}
          columns={displayColumns}
          params={{
            project_reaction_id: projectReactionId,
            project_id: projectId,
            experiment_owner: ownerId
          }}
          pagination={
            isWorkbench
              ? false
              : { defaultPageSize: 10, showSizeChanger: false }
          }
        />
      </div>
      <EditExperimentModal
        projectId={
          Number.parseInt(editingExperiment?.project_no || '0') || projectId
        }
        projectReactionId={
          editingExperiment?.project_reaction_id ||
          projectReactionId ||
          editReactionId
        }
        experiementDesignNo={editingExperiment?.experiment_design_no}
        open={open}
        setOpen={setOpen}
        experiment={editingExperiment}
        onSuccess={() => ref.current?.reload()}
      />
      {update && (
        <StatusUpdateModel
          operateTargetName={getWord('pages.experiment')}
          status={update.status}
          trigger={{ open: !!update }}
          onFinished={async ({ status_update_note }) => {
            await update.update(status_update_note)
            setUpdate()
          }}
          onCancel={() => setUpdate(undefined)}
        />
      )}
      {!isEmpty(conclusion) ? (
        <ReagentList
          dialogProps={dialogProps}
          structure={conclusion?.rxn_smiles || ''}
          material_table={conclusion?.materials as MaterialTable[]}
        />
      ) : (
        ''
      )}
    </>
  )
}

export default ExperimentListTab
export const getExperimentListTabConfig = (
  projectId?: number,
  projectReactionId?: number,
  updateEvent?: Record<never, never>
) => ({
  key: 'my-experiment',
  label: (
    <TabWithNumber
      title={getWord('menu.list.experiment.execute')}
      getNumber={async () => {
        if (!projectId || !projectReactionId) return
        const res = await apiExperimentList({
          data: {
            filter_by: {
              project_reaction_id: projectReactionId,
              project_id: projectId
            },
            page_no: 1,
            page_size: 1
          }
        })
        return res?.data?.meta?.total || undefined
      }}
      refetchEvent={updateEvent}
    />
  ),
  children:
    projectId && projectReactionId ? (
      <ExperimentListTab
        projectId={projectId}
        projectReactionId={projectReactionId}
      />
    ) : null
})
