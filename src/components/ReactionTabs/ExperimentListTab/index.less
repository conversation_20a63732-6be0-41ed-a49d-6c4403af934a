@import '@/style/variables.less';
.experiment-list-root {
  .ant-pro-table-list-toolbar {
    display: none;
  }
}

.smiles-list {
  .ant-upload-list-picture-card-container,
  .ant-upload-select-picture-card {
    width: 60px;
    height: 60px;
  }
  &.reaction-list .ant-upload-list-picture-card-container {
    width: fit-content;
  }
  .ant-upload-list-picture-card
    .ant-upload-list-item-file
    + .ant-upload-list-item-name {
    display: none !important; // FIXME
  }
  .add-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  &.hide-upload-btn {
    .ant-upload {
      display: none;
    }
  }
}

.workbench_fold {
  width: calc(
    100vw - @fold-menu-width - @workbench-fold-summary-width - 50px
  ) !important;
}

.workbench_unFold {
  width: calc(
    100vw - @siderWidth - @workbench-unfold-summary-width - 50px
  ) !important;
}
