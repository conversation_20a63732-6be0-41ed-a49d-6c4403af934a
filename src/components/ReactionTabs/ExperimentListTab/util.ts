import { ExperimentDetail } from '@/types/models'

export const experimentStatus = [
  'created',
  'running',
  'hold',
  'canceled',
  'completed',
  'failed',
  'success'
] as const

export type ExperimentStatus = (typeof experimentStatus)[number]

export interface Experiment extends Omit<ExperimentDetail, 'status'> {
  priority: 'P0' | 'P1' | 'P2'
  status: ExperimentStatus
  experiment_owner?: string
  project_reaction_id?: number
  experiment_plan_id?: number
  design_id?: number
  experiment_no?: string
}

export const mockExperiment = (e: Partial<Experiment>): Experiment => {
  return {
    experiment_no: 'experiment_no',
    project_no: 'project_no',
    experiment_design_no: 'experiment_design_no',
    design_name: 'design_name',
    experiment_name: 'experiment_name',
    status: 'created',
    rxn_no: 'rxn_no',
    rxn: 'rxn',
    start_time: new Date(),
    end_time: new Date(),
    owner: 'owner',
    experiment_type: 'experiment_type',
    predict_end_date: new Date(),
    progress: 'progress',
    predict_yield: 'predict_yield',
    flow_data: 'flow_data',
    priority: 'P0',
    ...e
  }
}
