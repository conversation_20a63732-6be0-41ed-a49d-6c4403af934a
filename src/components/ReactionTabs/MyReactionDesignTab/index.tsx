import { Reaction } from '@/pages/route/util'
import { Paginate, Procedure, ProjectReaction } from '@/services/brain'
import { ExperimentDesignModel } from '@/types/models'
import { getWord } from '@/utils'
import { ProList, ProSkeleton } from '@ant-design/pro-components'
import { groupBy } from 'lodash'
import React, { useEffect, useState } from 'react'
import TabWithNumber from '../TabWithNumber'
import {
  fetchDesignTotal,
  fetchDesigns,
  fetchEffectiveProcedureIds,
  fetchProcedures
} from '../util'
import ReactionDesignCard from './ReactionDesignCard'

export interface ReactionDesign {
  procedure: Procedure
  design: ExperimentDesignModel
}

export interface MyReactionDesignTabProps {
  reaction?: Reaction
  projectReaction?: ProjectReaction
  showCanceled?: boolean
  renderer?: (
    procedure: Procedure,
    experiment: ExperimentDesignModel
  ) => React.ReactNode
}

const MyReactionDesignTab: React.FC<MyReactionDesignTabProps> = ({
  reaction,
  projectReaction,
  showCanceled,
  renderer
}) => {
  const [designs, setDesigns] = useState<ReactionDesign[]>([])
  const [paginate, setPaginate] = useState<Paginate>({ page: 1, pageSize: 10 })
  const [loading, setLoading] = useState<boolean>(false)

  const updateDesigns = async (
    reaction?: Reaction,
    projectReaction?: ProjectReaction
  ) => {
    const procedureIds = await fetchEffectiveProcedureIds(
      reaction,
      projectReaction
    )
    const procedureMap = groupBy(await fetchProcedures(procedureIds), 'id')
    const designs = await fetchDesigns(procedureIds)
    setDesigns(
      designs.map((design) => ({
        design,
        procedure: procedureMap[design.rxn_no]?.[0] || {}
      }))
    )
  }

  useEffect(() => {
    if (!reaction || !projectReaction) return
    setLoading(true)
    updateDesigns(reaction, projectReaction).then(() => setLoading(false))
  }, [reaction, projectReaction])

  const filteredDesigns = showCanceled
    ? designs
    : designs.filter((d) => d.design.status !== 'canceled')

  if (!designs) {
    return <ProSkeleton type="list" />
  }
  return (
    <ProList<ReactionDesign>
      ghost
      loading={loading}
      pagination={{
        current: paginate.page,
        pageSize: paginate.pageSize,
        simple: true,
        total: filteredDesigns.length,
        onChange: (page, pageSize) => {
          setPaginate({ page, pageSize })
        }
      }}
      renderItem={({ procedure, design }) => renderer?.(procedure, design)}
      grid={{ column: 1 }}
      dataSource={filteredDesigns}
    />
  )
}

export default MyReactionDesignTab

export const getMyReactionDesignTabConfig = (
  reaction?: Reaction,
  projectReaction?: ProjectReaction,
  renderer: (
    procedure: Procedure,
    design: ExperimentDesignModel
  ) => React.ReactNode = (procedure, design) => (
    <ReactionDesignCard
      procedure={procedure}
      design={design}
      projectReactionId={projectReaction?.id}
    />
  ),
  showCanceled?: boolean
) => ({
  key: 'my-reaction-design',
  label: (
    <TabWithNumber
      title={getWord('my-reaction-experimental')}
      getNumber={async () => {
        if (!reaction || !projectReaction) return undefined
        return await fetchDesignTotal(reaction, projectReaction, showCanceled)
      }}
    />
  ),
  children:
    reaction && projectReaction ? (
      <MyReactionDesignTab
        reaction={reaction}
        projectReaction={projectReaction}
        renderer={renderer}
        showCanceled={showCanceled}
      />
    ) : null
})
