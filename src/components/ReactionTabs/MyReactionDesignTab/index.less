.my-reaction-design-card-root {
  .actions-wrapper {
    margin-left: auto;
  }
  .divider-wrapper {
    display: flex;
    justify-content: center;
    .divider {
      height: 100%;
    }
  }
}

.create-form-root {
  .smiles-list {
    .ant-upload-list-picture-card-container,
    .ant-upload-select-picture-card {
      width: 60px;
      height: 60px;
    }
    &.reaction-list .ant-upload-list-picture-card-container {
      width: fit-content;
    }
    .ant-upload-list-picture-card
      .ant-upload-list-item-file
      + .ant-upload-list-item-name {
      display: none !important; // FIXME
    }
    .add-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
    &.hide-upload-btn {
      .ant-upload {
        display: none;
      }
    }
  }
}

.procedurePopover {
  width: 350px;
}
