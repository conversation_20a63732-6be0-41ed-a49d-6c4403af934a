import ButtonWithLoading from '@/components/ButtonWithLoading'
import MoleculeStructure from '@/components/MoleculeStructure'
import { apiUpdateExperimentDesigns } from '@/services'
import { Procedure } from '@/services/brain'
import { DesignStatus, ExperimentDesignModel } from '@/types/models'
import { encodeString, getWord, toInt } from '@/utils'
import { ProDescriptions } from '@ant-design/pro-components'
import {
  App,
  Button,
  Card,
  Col,
  Divider,
  Popover,
  Row,
  Space,
  Tag,
  Typography
} from 'antd'
import { TooltipPlacement } from 'antd/es/tooltip'
import { isNil } from 'lodash'
import React from 'react'
import { useAccess, useParams } from 'umi'
import EditExperimentModal from '../EditExperimentModal'
import './index.less'

export interface ReactionDesignCardProps {
  procedure: Procedure
  design: ExperimentDesignModel
  projectReactionId?: number
  onEdit?: (procedure: Procedure) => void
  onDelete?: (procedure: Procedure) => void
  onUpdated?: (action?: 'cancel' | 'create') => void
}

const ReactionDesignCard: React.FC<ReactionDesignCardProps> = ({
  procedure,
  design,
  projectReactionId,
  onEdit: propOnEdit,
  onDelete: propOnDelete,
  onUpdated
}) => {
  const access = useAccess()
  const { yields, smiles } = procedure
  const { modal } = App.useApp()
  const { id: projectId, reactionId } = useParams<{ id: string }>()
  const onEdit = () => propOnEdit?.(procedure)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onDelete = () => {
    return
    modal.confirm({
      title: `确认删除该反应？`,
      onOk: () => propOnDelete?.(procedure)
    })
  }
  const onCancelExperimentPlan = async () => {
    modal.confirm({
      title: `确认取消该实验设计？`,
      onOk: async () => {
        await apiUpdateExperimentDesigns({
          data: { id: design.id, status: DesignStatus.Canceled }
        })
        onUpdated?.('cancel')
      }
    })
  }

  const PopoverCom = ({
    content,
    placement
  }: {
    content: string
    placement?: TooltipPlacement
  }) => {
    return (
      <Popover
        overlayStyle={{
          width: '30vw'
        }}
        placement={placement || 'top'}
        content={content}
        arrow={false}
      >
        <Typography.Link>Procedure</Typography.Link>
      </Popover>
    )
  }

  return (
    <Card
      style={{ margin: '4px 0' }}
      size="small"
      className="my-reaction-design-card-root"
    >
      <Row>
        <Col sm={24} md={11}>
          <Row>
            <Typography.Title level={5}>{getWord('reaction')}</Typography.Title>
            <Space className="actions-wrapper" size="small">
              {access?.authCodeList?.includes(
                'reaction-detail.myReactionDesign.editMolecules'
              ) && (
                <Button type="link" onClick={onEdit}>
                  {getWord('edit-molecules')}
                </Button>
              )}
              {/* <Button type="link" onClick={onDelete}>
                删除
              </Button> */}
            </Space>
          </Row>
          <Row>
            <Space>
              <Typography.Text>{getWord('yield')}: </Typography.Text>
              <Typography.Text type="secondary">
                {isNil(yields) ? '-' : `${yields}%`}
              </Typography.Text>
            </Space>
          </Row>
          <Row>
            <MoleculeStructure structure={smiles} />
          </Row>
          <Row>
            <PopoverCom placement="topLeft" content={procedure.procedure} />
          </Row>
        </Col>
        <Col sm={0} md={1} className="divider-wrapper">
          <Divider type="vertical" className="divider" />
        </Col>
        <Col sm={24} md={11}>
          <ProDescriptions<ExperimentDesignModel>
            title={getWord('experiment-design')}
            column={1}
            dataSource={design}
            extra={
              <Space>
                {access?.authCodeList?.includes(
                  'projects_reaction_experimentDesign_view'
                ) ? (
                  <Button
                    type="link"
                    onClick={() => {
                      window.open(
                        `/projects/${projectId}/reaction/${reactionId}/experimental-procedure/detail/${encodeString(
                          JSON.stringify(design.id)
                        )}?type=editor`,
                        '_blank'
                      )
                    }}
                  >
                    {getWord('pages.projectTable.actionLabel.viewDetail')}
                  </Button>
                ) : (
                  ''
                )}
                {design.status === DesignStatus.Published && (
                  <EditExperimentModal
                    materialTable={procedure.material_table}
                    projectId={toInt(design.project_no)}
                    projectReactionId={projectReactionId}
                    experiementDesignNo={design.experiment_design_no}
                    onSuccess={() => onUpdated?.('create')}
                  />
                )}
                {access?.authCodeList?.includes(
                  'projects_reaction_experimentDesign_cancel'
                ) &&
                  design.status !== DesignStatus.Canceled && (
                    <ButtonWithLoading
                      type="link"
                      onClick={onCancelExperimentPlan}
                    >
                      {getWord('pages.experiment.label.operation.cancel')}
                    </ButtonWithLoading>
                  )}
              </Space>
            }
          >
            <ProDescriptions.Item
              label={getWord('pages.experiment.label.experimentDesignName')}
              dataIndex="name"
            />
            <ProDescriptions.Item
              label={getWord('experiment-design-status')}
              dataIndex="status"
              valueEnum={{
                canceled: {
                  text: (
                    <Tag color="#979797">
                      {getWord('pages.experimentDesign.statusLabel.canceled')}
                    </Tag>
                  )
                },
                created: {
                  text: (
                    <Tag color="#FAAD14">
                      {getWord('pages.experimentDesign.statusLabel.created')}
                    </Tag>
                  )
                },
                published: {
                  text: (
                    <Tag color="#1890FF">
                      {getWord('pages.experimentDesign.statusLabel.published')}
                    </Tag>
                  )
                },
                validated: {
                  text: (
                    <Tag color="#FAAD14">
                      {getWord('pages.experimentDesign.statusLabel.validated')}
                    </Tag>
                  )
                }
              }}
            />
            <ProDescriptions.Item
              label={getWord('creator')}
              dataIndex="creator"
            />
            <ProDescriptions.Item
              label={getWord('last-modified-time')}
              dataIndex="modified_date"
              valueType="dateTime"
            />
            <ProDescriptions.Item label={getWord('reaction-parameters')} />
            <ProDescriptions.Item label={getWord('experiment-log')} />
          </ProDescriptions>
          <PopoverCom
            content={
              (design.reference_text as string) ||
              (design.reference_text as string)
            }
          />
        </Col>
      </Row>
    </Card>
  )
}

export default ReactionDesignCard
