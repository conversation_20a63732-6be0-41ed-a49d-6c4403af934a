import {
  getReactionFromRxn,
  matchResultToProcedure
} from '@/pages/reaction/util'
import { Reaction, getRxnFromReaction } from '@/pages/route/util'
import { apiExperimentDesigns } from '@/services'
import {
  Procedure,
  ProcedureRxnMatchResult,
  ProjectReaction,
  query,
  service
} from '@/services/brain'
import { ExperimentDesignModel } from '@/types/models'
import { uniqBy } from 'lodash'

export const fetchEffectiveProcedureIds = async (
  reaction?: Reaction,
  projectReaction?: ProjectReaction
) => {
  const request = query<ProjectReaction>('project-reactions', undefined, [
    'effective_procedures'
  ])
  if (projectReaction?.id) {
    request.equalTo('id', projectReaction.id)
  } else if (reaction && projectReaction?.project?.id) {
    request
      .equalTo(
        'reaction',
        getRxnFromReaction(reaction, true).replace(/\+/g, '%2B')
      )
      .filterDeep('project.id', 'eq', projectReaction?.project?.id)
  }
  const { data } = await request.get()
  return data?.[0]?.effective_procedures || []
}

export const fetchProcedures = async (ids: number[]): Promise<Procedure[]> => {
  if (!ids.length) {
    return []
  }
  const res = await service<Procedure>('procedure/query', {
    method: 'post',
    data: { ids },
    normalizeData: false
  })
    .select()
    .get()
  return (res as unknown as ProcedureRxnMatchResult[])
    .sort((a, b) => b.id - a.id)
    .map(matchResultToProcedure)
}

export const fetchDesigns = async (
  procedureIds: number[]
): Promise<ExperimentDesignModel[]> => {
  return (
    (await apiExperimentDesigns({ data: { rxn_nos: procedureIds } }))?.data
      ?.data || []
  )
}

export const fetchDesignTotal = async (
  reaction?: Reaction,
  projectReaction?: ProjectReaction,
  showCanceled?: boolean
): Promise<number> => {
  return (
    (await fetchDesigns(
      await fetchEffectiveProcedureIds(reaction, projectReaction)
    )) || []
  )?.filter((d) => (showCanceled ? true : d.status !== 'canceled'))?.length
}

export const getProceduresWithLeastReactents = (
  procedures: Procedure[],
  reactents: string[]
): Procedure[] =>
  procedures.filter((p) => {
    const rs = new Set(getReactionFromRxn(p.smiles).reactants)
    return reactents.every((r) => rs.has(r))
  })

export const fetchUniqueProceduresWithReaction = async (
  reaction?: Reaction,
  projectReaction?: ProjectReaction
) => {
  const procedures = await fetchProcedures(
    await fetchEffectiveProcedureIds(reaction, projectReaction)
  )
  return uniqBy(procedures, (p) => p.smiles)
}

export const fetchProceduresWithReaction = async (
  reaction?: Reaction,
  projectReaction?: ProjectReaction
) => {
  const procedures = await fetchProcedures(
    await fetchEffectiveProcedureIds(reaction, projectReaction)
  )
  return procedures
}
