import SmilesInput from '@/components/SmilesInput'
import { priorityOptions, reactionUnitOptions } from '@/constants'
import useOptions from '@/hooks/useOptions'
import { useProjectMembers } from '@/hooks/useProjectMembers'
import {
  apiCreateExperimentPlan,
  apiExperimentFeed,
  apiUpdateExperimentPlan,
  parseResponseResult
} from '@/services'
import { getWord } from '@/utils'
import { CalculatorOutlined } from '@ant-design/icons'
import {
  ModalForm,
  ProForm,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSegmented,
  ProFormSelect,
  ProFormText
} from '@ant-design/pro-components'

import { App, Button, Form, Space, Tooltip } from 'antd'
import { isNil, round, update } from 'lodash'
import React, { useState } from 'react'
import type {
  EditExperimentModalProps,
  ExperimentCreateParams,
  ExperimentMaterialTableItem
} from './index.d'
import './index.less'
import { useMolWeightMap } from './useMolWeightMap'

const EditExperimentModal: React.FC<EditExperimentModalProps> = ({
  projectId,
  projectReactionId,
  experiementDesignNo,
  open,
  setOpen: setOpenProp,
  triggerCom,
  materialTable,
  experiment,
  onSuccess
}) => {
  const { message } = App.useApp()
  const { reactionRoleOptions } = useOptions()

  const [form] = Form.useForm<ExperimentCreateParams>()
  const [updatingFeed, setUpdatingFeed] = useState<boolean>(false)
  const { members, loading } = useProjectMembers(projectId || 0)
  const editMode = !!experiment
  const labelKey = `pages.experimentDesign.label.${
    editMode ? 'update' : 'new'
  }Experiment`
  const experimentId = experiment?.id
  const { molWeightMap } = useMolWeightMap(
    materialTable?.map((t) => t.smiles).filter((s) => !!s) || []
  )

  const initExperiment = () => {
    const initExperiment = experiment || {
      experiment_type: 'test',
      priority: 'P1',
      material_table: materialTable?.filter((m) => m.role !== 'product') || []
    }
    update(initExperiment, 'experiment_owner', (v) => {
      const n = Number.parseInt(v)
      if (Number.isNaN(n)) return undefined
      return n
    })
    form.setFieldsValue(initExperiment)
  }

  const setOpen = (open: boolean) => {
    setOpenProp?.(open)
    if (open) {
      initExperiment()
    }
  }

  const saveExperiment = async (experiment: ExperimentCreateParams) => {
    const data = {
      ...experiment,
      id: experimentId,
      project_no: `${projectId}`,
      project_reaction_id: projectReactionId,
      experiment_design_no: experiementDesignNo
    }
    let res
    if (editMode) {
      res = await apiUpdateExperimentPlan({ data })
    } else {
      res = await apiCreateExperimentPlan({ data })
    }
    if (parseResponseResult(res).ok) {
      message.success(
        <>
          {getWord(labelKey)}
          {getWord('app.general.message.success')}
        </>
      )
      setOpen?.(false)
      onSuccess?.()
      return true
    }
    return false
  }

  const handleUpdateFeed = async ({
    weight,
    weight_unit,
    smiles
  }: ExperimentMaterialTableItem) => {
    const oldMaterials = form.getFieldValue(
      'material_table'
    ) as ExperimentMaterialTableItem[]

    setUpdatingFeed(true)
    const { data: { material_dict = {} } = {} } = await apiExperimentFeed({
      data: {
        material_table: oldMaterials,
        input_smiles: smiles,
        weight,
        weight_unit
      }
    }).finally(() => setUpdatingFeed(false))

    const newMaterials = oldMaterials.map((m) => {
      const { weight, weight_unit } = material_dict[m.smiles] || {}
      if (!isNil(weight)) {
        return { ...m, weight, weight_unit: weight_unit || m.weight_unit }
      }
      return m
    })
    form.setFieldValue('material_table', newMaterials)
    form.validateFields()
  }

  if (!(projectId && projectReactionId && experiementDesignNo)) {
    return null
  }
  return (
    <ModalForm<ExperimentCreateParams>
      className="create-form-root"
      form={form}
      open={open}
      onOpenChange={setOpen}
      title={getWord(labelKey)}
      trigger={
        isNil(open) ? (
          triggerCom ? (
            triggerCom
          ) : (
            <Button type="link">{getWord(labelKey)}</Button>
          )
        ) : undefined
      }
      autoFocusFirstInput
      width={1140}
      onFinish={saveExperiment}
      modalProps={{
        destroyOnClose: true,
        centered: true
      }}
    >
      <ProFormGroup key="1">
        <ProFormText
          width="lg"
          name="name"
          label={getWord('pages.experiment.label.name')}
        />
        <ProFormSelect
          width="lg"
          name="experiment_type"
          label={getWord('pages.experiment.label.type')}
          required
          rules={[{ required: true }]}
          valueEnum={{
            test: getWord('pages.experimentDesign.label.test'),
            scale_up: getWord('pages.experimentDesign.label.scale_up')
          }}
        />
      </ProFormGroup>

      <ProFormGroup key="2">
        <ProFormSelect
          width="lg"
          name="priority"
          label={getWord('pages.experiment.label.priority')}
          required
          rules={[{ required: true }]}
          options={priorityOptions}
        />
        <ProFormSelect
          width="lg"
          name="experiment_owner"
          transform={(value) => {
            const returnValue = typeof value === 'number' ? value : value.value
            return { experiment_owner: `${returnValue}` }
          }}
          label={getWord('pages.experiment.label.personInCharge')}
          disabled={loading}
          options={members}
          initialValue={members[0]}
          required
          rules={[{ required: true }]}
        />
      </ProFormGroup>
      <ProFormList
        name="material_table"
        label={getWord('material-sheet')}
        copyIconProps={false}
        deleteIconProps={false}
        creatorButtonProps={false}
      >
        <ProFormGroup key="group">
          <ProFormSelect
            disabled
            name="role"
            label={getWord('role')}
            width="xs"
            options={reactionRoleOptions.filter((r) => r.value !== 'product')}
            required
            rules={[{ required: true }]}
          />
          <ProFormText
            disabled
            width="xs"
            name="no"
            label={getWord('material-ID')}
            required
            rules={[{ required: true }]}
          />
          <ProForm.Item
            className="filter-form-root"
            name="smiles"
            label={getWord('structural')}
            required
            rules={[{ required: true }]}
          >
            <SmilesInput disabled multiple={false} />
          </ProForm.Item>
          <ProFormDigit
            width="xs"
            name="equivalent"
            label={getWord('EWR')}
            disabled
            required
            rules={[{ required: true }]}
          />
          <ProFormSelect
            width="xs"
            name="unit"
            label={getWord('unit')}
            disabled
            options={reactionUnitOptions}
            initialValue={'eq'}
            required
            rules={[{ required: true }]}
          />

          <ProFormDependency name={['smiles']}>
            {({ smiles }) => {
              return (
                <ProFormDigit
                  width="xs"
                  label={getWord('molecular-mass')}
                  disabled
                  fieldProps={{
                    value:
                      molWeightMap[smiles] && round(molWeightMap[smiles], 2),
                    placeholder: '...'
                  }}
                />
              )
            }}
          </ProFormDependency>
          <ProForm.Item label={getWord('Mass')} required>
            <Space.Compact>
              <ProFormDigit
                width="xs"
                name="weight"
                required
                disabled={updatingFeed}
                rules={[{ required: true }]}
              />
              <ProFormSelect
                width="xs"
                name="weight_unit"
                options={['mg', 'g']}
                initialValue="mg"
                required
                disabled={updatingFeed}
                rules={[{ required: true }]}
              />
              <ProFormDependency
                name={['smiles', 'weight', 'weight_unit']}
                ignoreFormListField={false}
              >
                {(item) => {
                  return (
                    <Tooltip
                      placement="top"
                      title={getWord('calculate-materials')}
                    >
                      <Button
                        type="link"
                        disabled={
                          updatingFeed || !(item.weight && item.weight_unit)
                        }
                        onClick={() =>
                          handleUpdateFeed(item as ExperimentMaterialTableItem)
                        }
                      >
                        <CalculatorOutlined />
                      </Button>
                    </Tooltip>
                  )
                }}
              </ProFormDependency>
            </Space.Compact>
          </ProForm.Item>
        </ProFormGroup>
      </ProFormList>

      <ProFormGroup key="3">
        <ProFormSegmented
          label={getWord('start-time')}
          name="start_type"
          initialValue="auto"
          required
          rules={[{ required: true }]}
          valueEnum={{
            auto: getWord('pages.experimentDesign.label.auto'),
            asap: getWord('pages.experimentDesign.label.asap'),
            custom: getWord('pages.experimentDesign.label.custom')
          }}
        />
        <ProFormDependency name={['start_type']}>
          {({ start_type }) => {
            if (start_type === 'custom') {
              return (
                <ProFormDateRangePicker
                  label={getWord('start-time-limit')}
                  name="start_time_range"
                  transform={(values) => ({
                    earliest_start_time: values?.[0],
                    latest_start_time: values?.[1]
                  })}
                  required
                  rules={[{ required: true }]}
                />
              )
            }
            return null
          }}
        </ProFormDependency>
      </ProFormGroup>
    </ModalForm>
  )
}

export default EditExperimentModal
