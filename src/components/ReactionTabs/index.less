.retroReactionRowRoot {
  .riskTag {
    color: #b41500;
    background: linear-gradient(0deg, #f7e8e5, #f7e8e5),
      linear-gradient(0deg, #e8b9b2, #e8b9b2);
    border: 1px solid #e8b9b2;
  }
  :global {
    .ant-pro-card .ant-pro-card-body {
      padding-top: 0 !important; // for override importent style
    }
    .ant-pro-checkcard-content {
      display: none;
    }
  }
  .retroReactionRoot {
    width: 100%;
    cursor: auto;
    &.selectable {
      cursor: pointer;
      .smiles {
        cursor: pointer;
      }
    }
    .actionsWrapper {
      display: flex;
      flex-direction: row;
      padding: 0 6px;
      .rightActionWrapper {
        margin-left: auto;
      }
    }
    .sourceWrapper {
      padding-left: 4px;
      font-size: 12px;
      line-height: 14px;
    }
  }
}

.retroReactionTabRoot {
  :global {
    .ant-pro-card-body {
      padding: 8px 4px !important;
    }
  }
}

.materialsPriceTable {
  :global {
    .ant-modal-content {
      .ant-modal-header {
        padding-left: 15px;
      }
      padding: 20px 5px 2px !important;
    }
  }
}

.viwePrice {
  height: 24px;
  .warningIcon {
    position: relative;
    top: 4px;
    right: -6px;
    height: 24px;
  }
}
