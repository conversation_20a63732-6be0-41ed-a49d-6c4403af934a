import { apiGetSmilesWeight, parseResponseResult } from '@/services'
import { useEffect, useState } from 'react'

export const getMolWeightMap = async (
  smiles: string[]
): Promise<Record<string, number>> => {
  const res = await apiGetSmilesWeight({ data: smiles })
  console.log(res)
  if (parseResponseResult(res).ok) {
    return res.data
  }
  return {}
}

export const useMolWeightMap = (
  smiles: string[]
): { molWeightMap: Record<string, number> } => {
  const [map, setMap] = useState<Record<string, number>>({})

  const updateMap = async (smileses: string[]) => {
    setMap(await getMolWeightMap(smileses))
  }

  useEffect(() => {
    updateMap(smiles)
  }, [])

  return { molWeightMap: map }
}
