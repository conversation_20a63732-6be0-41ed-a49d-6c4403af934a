import { ReactComponent as WarningIcon } from '@/assets/svgs/warning.svg'
import Launcher from '@/components/Launcher'
import MoleculeStructure from '@/components/MoleculeStructure'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { Reaction, getRxnFromReaction } from '@/pages/route/util'
import {
  MaterialItem,
  MaterialItemSearchResponse,
  RetroReaction,
  query,
  queryWithDefaultOrder
} from '@/services/brain'
import { getWord } from '@/utils'
import {
  ProList,
  ProSkeleton,
  useDeepCompareEffect
} from '@ant-design/pro-components'
import { Modal, Popover, Space, Tag, Typography, message } from 'antd'
import { default as classNames, default as cs } from 'classnames'
import { isEmpty, round } from 'lodash'
import React, { useEffect, useState } from 'react'
import { useAccess, useModel } from 'umi'
import ButtonWithLoading from '../ButtonWithLoading'
import MaterialPriceTable from './MaterialPriceTable'
import TabWithNumber from './TabWithNumber'
import styles from './index.less'

const getNameOfReaction = (i: number): string =>
  `${getWord('reaction')}${i + 1}`

const isCurrentProcess = (
  reaction: RetroReaction,
  processId: number
): boolean => {
  return reaction.retro_processes?.findIndex((p) => p.id === processId) !== -1
}

export interface RetroReactionTabProps {
  reaction?: Reaction
  projectId?: number
  onSelectProcedure?: (p?: string) => void
  onSelect?: (reactions: { rxn: RetroReaction; name: string }[]) => void
  selectAllWhenEmpty?: boolean
  retroProcessId?: number
  fullReactionConfig?: {
    fullReaction?: Reaction
    onSelectFullReaction?: (r: Reaction) => void
  }
}

const RetroReactionTab: React.FC<RetroReactionTabProps> = ({
  reaction,
  projectId,
  onSelectProcedure,
  onSelect,
  selectAllWhenEmpty,
  retroProcessId,
  fullReactionConfig
}) => {
  const {
    getProfileInfo,
    getCommonExpression,
    showLauncher,
    sendMessage,
    reload,
    finishedReload,
    reactionStepNo,
    isOpen
  } = useModel('commend')
  const { getMaterialCodeOptions } = useModel('compound')
  const access = useAccess()
  const [retroReactions, setRetroReactions] = useState<RetroReaction[]>([])
  const { fetch, loading } = useBrainFetch()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [materialsPrice, setMaterialsPrice] = useState<MaterialItem[]>([])
  const fetchReaction = async () => {
    if (!reaction?.reactants?.length) return
    const { data } = await fetch(
      queryWithDefaultOrder<RetroReaction>(
        'retro-reactions/list?comment=true',
        { method: 'post', data: { filters: { ...reaction, projectId } } }
      )
        .populateWith('retro_processes', ['id'])
        .sortBy([{ field: 'updatedAt', order: 'desc' }])
        .paginate(1, 1000)
        .get()
    )
    if (data) {
      setRetroReactions(data)
      onSelect?.(data.map((r, i) => ({ rxn: r, name: getNameOfReaction(i) })))
    }
  }
  const { fullReaction } = fullReactionConfig || {}

  useDeepCompareEffect(() => {
    fetchReaction()
    getCommonExpression('retro-reaction')
  }, [reaction])

  useEffect(() => {
    getMaterialCodeOptions()
  }, [])

  useEffect(() => {
    if (reload) {
      fetchReaction()
      finishedReload()
    }
  }, [reload])

  if (!retroReactions) {
    return <ProSkeleton type="list" />
  }
  return (
    <>
      <Modal
        title={getWord('material-sheet')}
        open={isModalOpen}
        className={styles.materialsPriceTable}
        onCancel={() => setIsModalOpen(false)}
        footer={false}
        centered
        width={920}
      >
        <MaterialPriceTable materialsPrice={materialsPrice} />
      </Modal>
      <ProList
        ghost
        loading={loading || !reaction?.reactants?.length}
        className={styles.retroReactionTabRoot}
        pagination={false}
        rowClassName={styles.retroReactionRowRoot}
        rowSelection={
          onSelect
            ? {
                onChange: (indexs) => {
                  let selected = indexs.map((i) => ({
                    rxn: retroReactions[i as number],
                    name: getNameOfReaction(i as number)
                  }))
                  if (indexs.length === 0 && selectAllWhenEmpty) {
                    selected = retroReactions.map((r, i) => ({
                      rxn: r,
                      name: getNameOfReaction(i)
                    }))
                  }
                  onSelect(selected)
                }
              }
            : undefined
        }
        metas={{
          content: {
            render: (_, entity, index) => {
              const active =
                !!fullReaction &&
                getRxnFromReaction(entity, true) ===
                  getRxnFromReaction(fullReaction, true)
              return (
                <div
                  className={classNames(styles?.retroReactionRoot, {
                    [styles.selectable]: !!onSelect
                  })}
                >
                  {retroProcessId && (
                    <Typography.Text
                      type="secondary"
                      className={styles?.sourceWrapper}
                    >
                      {isCurrentProcess(entity, retroProcessId)
                        ? getWord('from-this-search')
                        : getWord('from-other-search')}
                    </Typography.Text>
                  )}
                  <div className={styles?.actionsWrapper}>
                    <Space className={styles?.leftActionWrapper}>
                      <div>
                        {getWord('reaction')}
                        {index + 1}
                      </div>
                      {typeof entity?.reliability_score === 'number' ? (
                        <span>
                          {getWord('reaction-reliability')}：
                          {round(entity.reliability_score, 2)}
                        </span>
                      ) : (
                        ''
                      )}
                      {entity?.is_dangerous && (
                        <Tag color="error" className={styles.riskTag}>
                          {getWord('danger-reaction')}
                        </Tag>
                      )}
                      {entity.is_known_reaction && (
                        <Tag color="purple">{getWord('reported')}</Tag>
                      )}
                      {entity.is_selective_risk && (
                        <Tag color="error">{getWord('regioselectivity')}</Tag>
                      )}
                    </Space>
                    <Space
                      className={styles?.rightActionWrapper}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {access?.authCodeList?.includes(
                        'view-by-backbone.tab.recommend.comment'
                      ) ? (
                        <ButtonWithLoading
                          className={styles?.commendButton}
                          type="link"
                          onClick={() =>
                            getProfileInfo({
                              _commendSuject: {
                                id: entity?.id
                              },
                              collection_class: 'retro-reaction',
                              reaction_step_no: reactionStepNo
                            })
                          }
                          size="small"
                        >
                          {entity?.content_count && entity?.content_count > 0
                            ? `${getWord('comment')}（${
                                entity?.content_count
                              }）`
                            : getWord('comment')}
                        </ButtonWithLoading>
                      ) : (
                        ''
                      )}
                      {access?.authCodeList?.includes(
                        'view-by-backbone.tab.recommend.add-to-route'
                      ) && onSelectProcedure ? (
                        <ButtonWithLoading
                          size="small"
                          type="link"
                          disabled={active}
                          className={styles?.addToRouteButton}
                          onClick={() =>
                            onSelectProcedure(getRxnFromReaction(entity))
                          }
                        >
                          {getWord(active ? 'added-to-route' : 'add-to-route')}
                        </ButtonWithLoading>
                      ) : null}
                      <div
                        className={cs(
                          styles.viwePrice,
                          'flex-align-items-center'
                        )}
                      >
                        {entity?.material_warning && (
                          <Popover content={getWord('materials-not-available')}>
                            <WarningIcon
                              width={18}
                              className={styles.warningIcon}
                            />
                          </Popover>
                        )}
                        {access?.authCodeList?.includes(
                          'view-by-backbone.tab.recommend.view-materials'
                        ) ? (
                          <ButtonWithLoading
                            onClick={async () => {
                              const { data, error } = await query<MaterialItem>(
                                'material-items/search',
                                {
                                  method: 'POST',
                                  data: {
                                    data: {
                                      reactants: entity?.reactants
                                    }
                                  }
                                }
                              )
                                .populateWith('material_lib', [
                                  'name',
                                  'version',
                                  'description'
                                ])
                                .populateWith('material_tags', ['name'])
                                .get()
                              if (error?.message)
                                return message.error(error?.message)
                              const { missed, found } =
                                data as unknown as MaterialItemSearchResponse
                              const result = [
                                ...found,
                                ...missed.map((m) => ({
                                  canonical_smiles: m
                                }))
                              ]
                              // Note 原料表一样的分子放在一起展示
                              result.sort((a, b) =>
                                a.canonical_smiles.localeCompare(
                                  b.canonical_smiles
                                )
                              )
                              setMaterialsPrice(result)
                              if (!isEmpty(data)) setIsModalOpen(true)
                            }}
                            size="small"
                            type="link"
                          >
                            {getWord('view-materials')}
                          </ButtonWithLoading>
                        ) : (
                          ''
                        )}
                      </div>
                    </Space>
                  </div>
                  <div className={styles?.compoundWrapper}>
                    <MoleculeStructure
                      className={styles?.smiles}
                      structure={getRxnFromReaction(entity)}
                    />
                  </div>
                  <div onClick={(e) => e.stopPropagation()}>
                    <Launcher
                      commendType="reaction"
                      onMessageWasSent={sendMessage}
                      hiddenLauncher={showLauncher}
                      isOpen={isOpen}
                    />
                  </div>
                </div>
              )
            }
          }
        }}
        grid={{ column: 1 }}
        dataSource={
          retroProcessId
            ? retroReactions.sort((a, b) => {
                const ac = isCurrentProcess(a, retroProcessId)
                const bc = isCurrentProcess(b, retroProcessId)
                if (ac === bc) {
                  if (a.updatedAt === b.updatedAt) return a.id - b.id
                  return (a.updatedAt || '') >= (b.updatedAt || '') ? -1 : 1
                }
                return ac ? -1 : 1
              })
            : retroReactions
        }
      />
    </>
  )
}

export default RetroReactionTab

export const getRetroReactionTabConfig = (
  reaction?: Reaction,
  onSelectProcedure?: (p?: string) => void
) => ({
  key: 'retro-reaction',
  label: (
    <TabWithNumber
      title={getWord('generated-reaction')}
      getNumber={async () => {
        if (!reaction || !reaction.reactants.length) return
        const { meta, error } = await queryWithDefaultOrder<RetroReaction>(
          'retro-reactions/list',
          { method: 'post', data: { filters: reaction } },
          ['id']
        )
          .paginate(1, 1)
          .get()
        if (error || !meta?.pagination) return undefined
        return meta.pagination.total
      }}
    />
  ),
  children: reaction ? (
    <RetroReactionTab
      reaction={reaction}
      onSelectProcedure={onSelectProcedure}
    />
  ) : null
})
