import LazySmileDrawer from '@/components/LazySmileDrawer'
import type { MaterialItem } from '@/services/brain'
import { getWord, isEN, isValidArray } from '@/utils'
import { useModel } from '@umijs/max'
import { Table } from 'antd'
import Paragraph from 'antd/lib/typography/Paragraph'
import { isEmpty } from 'lodash'

interface MaterialTableProps {
  materialsPrice: MaterialItem[]
}
export default function MaterialPriceTable(props: MaterialTableProps) {
  const { materialCodeOptions } = useModel('compound')
  const getTheLabel = (newCode: string) => {
    if (!materialCodeOptions) return ''
    let targetTag = ''
    Object.keys(materialCodeOptions).map((key: string) => {
      let curOption: string[] = materialCodeOptions[key]
      if (curOption && curOption.includes(newCode)) targetTag = key
      return null
    })
    return getWord(targetTag)
  }

  const getTags = (codes: string[], link: string) => {
    let tags: string[] = []
    for (const code of codes) {
      let tag = getTheLabel(code)
      if (tag) tags.push(tag)
    }
    if (isEmpty(tags)) return '-'
    return link ? <a href={link}>{tags.join('、')}</a> : tags.join('、')
  }

  const coloumns = [
    {
      title: getWord('structural'),
      dataIndex: 'canonical_smiles',
      render: (smiles: string) =>
        smiles ? (
          <LazySmileDrawer structure={smiles} className="smilesItem" />
        ) : (
          ''
        )
    },
    {
      title: 'CAS',
      dataIndex: 'cas_no',
      render: (text: string) =>
        text && text !== '-' ? (
          <Paragraph ellipsis={false} copyable style={{ marginTop: '15px' }}>
            {text}
          </Paragraph>
        ) : (
          '-'
        )
    },
    {
      title: getWord('unit-price'),
      dataIndex: 'min_unit_price',
      render: (text: number, record: MaterialItem) =>
        text
          ? isEN()
            ? `From ￥${text.toFixed(2)}/${
                record?.unified_unit.includes('g') ? 'g' : 'ml'
              }`
            : `￥${text.toFixed(2)}/${
                record?.unified_unit.includes('g') ? 'g' : 'ml'
              }起`
          : '-'
    },
    {
      title: getWord('amount'),
      dataIndex: 'unit_range',
      render: (_text: string, record: MaterialItem) =>
        isValidArray(record?.unit_range) &&
        record?.unit_range[0] &&
        record?.unit_range[1]
          ? `${record?.unit_range[0]}${record?.unified_unit}-${record?.unit_range[1]}${record?.unified_unit}`
          : '-'
    },
    {
      title: getWord('label'),
      dataIndex: 'codes',
      width: 110,
      render: (_text: string, record: MaterialItem) =>
        !isEmpty(record?.codes)
          ? getTags(record?.codes as string[], record?.pubchem_safety_link)
          : '-'
    },
    {
      title: getWord('supplier'),
      dataIndex: 'source',
      render: (text: string, record: MaterialItem) =>
        record?.source_link ? (
          <a onClick={() => window.open(record?.source_link)}>{text}</a>
        ) : (
          text
        )
    },
    {
      title: getWord('version'),
      dataIndex: 'material_lib',
      render: (_text: string, record: MaterialItem) =>
        record?.material_lib?.version
    }
  ]
  return (
    <Table
      columns={coloumns}
      dataSource={props?.materialsPrice}
      pagination={false}
    />
  )
}
