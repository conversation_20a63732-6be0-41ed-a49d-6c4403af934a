.reaction-procedure-tab-root {
  --root-height: calc(100vh - 64px - 64px);
  --half-content-height: calc(var(--root-height) / 2 - 40px);
  --full-content-height: calc(var(--root-height) - 80px);
  display: flex;
  flex-direction: column;
  height: var(--root-height);
  .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }
  .ant-collapse-item:first-child .ant-collapse-content-box {
    max-height: var(--full-content-height);
    overflow-y: auto;
  }
  &.both-active .ant-collapse-item:first-child .ant-collapse-content-box {
    max-height: var(--half-content-height);
  }

  .ant-collapse-item:not(:first-child) {
    display: flex;
    flex-direction: column;
    overflow: auto;
    .ant-collapse-header {
      flex: 0;
    }
    .ant-collapse-content {
      flex: auto;
      overflow: auto;
    }
  }
}
