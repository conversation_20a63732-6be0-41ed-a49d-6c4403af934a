import MyReactionDialog from '@/pages/reaction/component/MyReactionDialog'
import { aiProcedureToProcedure } from '@/pages/reaction/util'
import { Reaction, getRxnFromReaction } from '@/pages/route/util'
import { ProjectReaction } from '@/services/brain'
import { AiProcedure } from '@/types/Procedure'
import { getWord } from '@/utils'
import { useModel } from '@umijs/max'
import { Collapse, CollapseProps } from 'antd'
import classNames from 'classnames'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import ReactionLibTab from '../ReactionLibTab'
import AiProcedureCard from '../ReactionLibTab/AiProcedureCard'
import RetroReactionTab from '../RetroReactionTab'
import './index.less'
import { useMyReactionDialog } from './useMyReactionDialog'

type Tab = 'reaction-lib' | 'retro-reactions'

export interface ReactionProcedureTabProps {
  reference?: {
    projectId: number
    onCreated?: (projectReaction: ProjectReaction) => void
  }
  projectId?: number
  reaction: Reaction
  onSelectProcedure?: (p?: string) => void
  retroProcessId?: number
  fullReactionConfig?: {
    fullReaction?: Reaction
    onSelectFullReaction?: (r: Reaction) => void
  }
}

const ReactionProcedureTab: React.FC<ReactionProcedureTabProps> = ({
  reaction,
  projectId,
  onSelectProcedure,
  retroProcessId,
  reference,
  fullReactionConfig
}) => {
  const [opens, setOpens] = useState<Tab[]>(['retro-reactions'])
  const [retros, setRetros] = useState<{ rxn: string; name: string }[]>([])
  const ref = useRef<HTMLDivElement>()
  const { props, setProcedure, setOpen } = useMyReactionDialog(
    reaction,
    'reference',
    reference?.projectId,
    reference?.onCreated
  )
  const { initialState: { userInfo = undefined } = {} } =
    useModel('@@initialState')

  useEffect(() => {
    if (!opens.includes('reaction-lib')) {
      setOpens((o) => [...o, 'reaction-lib'])
    }
  }, [retros])

  const items: () => CollapseProps['items'] = useCallback(
    () => [
      {
        key: 'retro-reactions',
        label: getWord('recommended-reactions'),
        children: (
          <RetroReactionTab
            projectId={projectId}
            reaction={reaction}
            fullReactionConfig={fullReactionConfig}
            onSelectProcedure={onSelectProcedure}
            selectAllWhenEmpty
            retroProcessId={retroProcessId}
            onSelect={(rs) =>
              setRetros(
                rs.map((r) => ({
                  rxn: getRxnFromReaction(r.rxn),
                  name: r.name
                }))
              )
            }
          />
        )
      },
      {
        key: 'reaction-lib',
        label: getWord('reaction-lib'),
        extra: <div ref={(e) => (ref.current = e || undefined)} />,
        children: (
          <ReactionLibTab
            actionSlot={ref.current}
            reaction={reaction}
            rxns={retros}
            renderer={(procedure: AiProcedure, isSame?: boolean) => (
              <AiProcedureCard
                procedure={procedure}
                isSame={isSame}
                name={retros?.find((r) => r.rxn === procedure.query)?.name}
                onReference={
                  reference
                    ? (p) => {
                        setProcedure(
                          aiProcedureToProcedure(p, `${userInfo?.id || 0}`)
                        )
                        setOpen(true)
                      }
                    : undefined
                }
              />
            )}
          />
        )
      }
    ],
    [reaction, retros]
  )

  return (
    <>
      <Collapse
        onChange={(s) => {
          if (typeof s === 'string') setOpens([s as Tab])
          else setOpens(s as Tab[])
        }}
        className={classNames('reaction-procedure-tab-root', {
          'both-active': opens.length === 2
        })}
        activeKey={opens}
        items={items()}
        size="small"
        ghost
      />
      <MyReactionDialog {...props} />
    </>
  )
}

export default ReactionProcedureTab

export const getReactionProcedureTabConfig = (
  projectId?: number,
  reaction?: Reaction,
  onSelectProcedure?: (p?: string) => void,
  retroProcessId?: number,
  onReferenced?: (p: ProjectReaction) => void,
  fullReactionConfig?: {
    fullReaction?: Reaction
    onSelectFullReaction?: (r: Reaction) => void
  }
) => ({
  key: 'reaction-procedure-lib',
  label: <>{getWord('reaction-search')}</>,
  children: reaction ? (
    <ReactionProcedureTab
      reference={projectId ? { projectId, onCreated: onReferenced } : undefined}
      projectId={projectId}
      reaction={reaction}
      fullReactionConfig={fullReactionConfig}
      onSelectProcedure={onSelectProcedure}
      retroProcessId={retroProcessId}
    />
  ) : null
})
