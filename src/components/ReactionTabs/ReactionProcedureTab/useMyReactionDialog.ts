import { MyReactionDialogProps } from '@/pages/reaction/component/MyReactionDialog'
import { getRxnFromReaction, Reaction } from '@/pages/route/util'
import { Procedure, ProjectReaction, query } from '@/services/brain'
import { useState } from 'react'

export const useMyReactionDialog = (
  reaction: Reaction,
  mode: 'create' | 'edit' | 'reference',
  projectId?: number,
  onCreated?: (projectReaction: ProjectReaction) => void
): {
  props: MyReactionDialogProps
  setProcedure: (p?: Procedure) => void
  setOpen: (open: boolean) => void
  setProjectReaction: (p: ProjectReaction) => void
} => {
  const [open, setOpen] = useState<boolean>(false)
  const [projectReaction, setProjectReaction] = useState<ProjectReaction>({
    id: 0,
    reaction: getRxnFromReaction(reaction)
  })
  const [procedure, setProcedure] = useState<Procedure>()

  const updateOpen = async (open: boolean) => {
    if (open && projectReaction.id === 0) {
      if (!projectId) {
        console.warn('dont provide project id when open my reaction dialog')
        return
      }
      const data = await query<ProjectReaction>(
        'project-reaction/get-or-create',
        {
          method: 'post',
          data: { reaction: getRxnFromReaction(reaction), projectId },
          normalizeData: false
        }
      ).get()
      setProjectReaction((data as unknown as ProjectReaction[])?.[0])
    }
    setOpen(open)
  }

  return {
    props: {
      projectReaction,
      procedure,
      open,
      mode,
      onOpenChange: setOpen,
      onCreated,
      projectId
    },
    setProcedure,
    setOpen: updateOpen,
    setProjectReaction
  }
}
