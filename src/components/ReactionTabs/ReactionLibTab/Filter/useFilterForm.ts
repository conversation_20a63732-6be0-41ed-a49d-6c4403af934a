import { Form } from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import { useEffect, useState } from 'react'
import { ProcedureFilter } from '../useProcedure'

export interface FilterFieldsForForm {
  yearRange?: (Dayjs | undefined)[]
  impactFactor?: (number | undefined)[]
  validProcedure?: boolean
  scalable?: boolean
  same?: boolean
  similar?: boolean
}

export const frontToBack = (values: FilterFieldsForForm): ProcedureFilter => {
  const { yearRange, impactFactor, validProcedure, scalable, same, similar } =
    values
  return {
    year_min: yearRange?.[0]?.year(),
    year_max: yearRange?.[1]?.year(),
    impact_factor_min: impactFactor?.[0],
    impact_factor_max: impactFactor?.[1],
    has_valid_procedure: validProcedure,
    need_scalable: scalable,
    same,
    similar
  }
}

export const backToFront = (values: ProcedureFilter): FilterFieldsForForm => {
  const {
    year_min,
    year_max,
    impact_factor_min,
    impact_factor_max,
    has_valid_procedure,
    need_scalable,
    same,
    similar
  } = values
  return {
    yearRange: [year_min, year_max].map((y) =>
      y === undefined ? undefined : dayjs(`${y}-01-01`)
    ),
    impactFactor: [impact_factor_min, impact_factor_max],
    validProcedure: has_valid_procedure,
    scalable: need_scalable,
    same,
    similar
  }
}

export const useFilterForm = (init?: ProcedureFilter) => {
  const [form] = Form.useForm<FilterFieldsForForm>()
  const [latestInit, setLatestInit] = useState<ProcedureFilter>()

  useEffect(() => {
    const latest = backToFront(init || {})
    form.setFieldsValue(latest)
    setLatestInit(latest)
  }, [form, init])

  const handleFinish = (onFinished?: (values: ProcedureFilter) => void) => {
    return async () => {
      const values = form.getFieldsValue()
      onFinished?.(frontToBack(values))
      setLatestInit(values)
      return true
    }
  }

  return {
    form,
    handleFinish,
    reset: () => form.setFieldsValue(latestInit || {}),
    touched: form.isFieldsTouched()
  }
}
