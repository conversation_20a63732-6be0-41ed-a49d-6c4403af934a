import { getWord } from '@/utils'
import { FilterOutlined } from '@ant-design/icons'
import { ModalForm } from '@ant-design/pro-components'
import { Button } from 'antd'
import React from 'react'
import { ProcedureFilter } from '../useProcedure'
import Fields from './Fields'
import { FilterFieldsForForm, useFilterForm } from './useFilterForm'

interface FilterModalProps {
  initialFilter?: ProcedureFilter
  onFinished?: (values: ProcedureFilter) => void
}

const FilterModal: React.FC<FilterModalProps> = ({
  onFinished,
  initialFilter
}) => {
  const { form, handleFinish, reset } = useFilterForm(initialFilter)

  return (
    <ModalForm<FilterFieldsForForm>
      title={getWord('procedure-filter.title')}
      width={400}
      size="small"
      trigger={<Button size="small" icon={<FilterOutlined />} type="text" />}
      form={form}
      onFinish={handleFinish(onFinished)}
      modalProps={{ onCancel: reset }}
    >
      <Fields />
    </ModalForm>
  )
}

export default FilterModal
