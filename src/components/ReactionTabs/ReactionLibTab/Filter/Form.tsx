import { ProForm } from '@ant-design/pro-components'
import React from 'react'
import { ProcedureFilter } from '../useProcedure'
import Fields from './Fields'
import { FilterFieldsForForm, useFilterForm } from './useFilterForm'

export interface FilterProps {
  onFinished?: (values: ProcedureFilter) => void
  initialFilter?: ProcedureFilter
}

const Filter: React.FC<FilterProps> = ({ onFinished, initialFilter }) => {
  const { form, handleFinish } = useFilterForm(initialFilter)

  return (
    <ProForm<FilterFieldsForForm>
      form={form}
      size="small"
      submitter={false}
      onFinish={handleFinish(onFinished)}
    >
      <Fields />
    </ProForm>
  )
}

export default Filter
