import { getWord } from '@/utils'
import {
  ProFormDateYearRangePicker,
  ProFormSlider,
  ProFormSwitch
} from '@ant-design/pro-components'
import React from 'react'
import styles from './index.less'

const maxImpactFactor = 50.5
const step = 10
const Fields: React.FC = () => {
  return (
    <div className={styles.fieldsWrapper}>
      <ProFormDateYearRangePicker
        name="yearRange"
        label={getWord('procedure-filter.year-range')}
        fieldProps={{
          allowEmpty: [true, true],
          placeholder: [
            getWord('procedure-filter.year-range-empty'),
            getWord('procedure-filter.year-range-empty')
          ]
        }}
      />
      <ProFormSlider
        name="impactFactor"
        range
        max={maxImpactFactor}
        min={0}
        marks={Object.fromEntries(
          [...Array(Math.floor(maxImpactFactor / step))].map((_, i) => [
            i * step,
            `${i * step}`
          ])
        )}
        label={getWord('procedure-filter.impact-factor')}
        fieldProps={{ range: true, className: styles.slider }}
      />
      <ProFormSwitch
        name="validProcedure"
        label={getWord('procedure-filter.has-procedure-only')}
      />
      <ProFormSwitch
        name="scalable"
        label={getWord('procedure-filter.scalable')}
      />
      <ProFormSwitch name="same" label={getWord('same-reaction')} />
      <ProFormSwitch name="similar" label={getWord('reference')} />
    </div>
  )
}

export default Fields
