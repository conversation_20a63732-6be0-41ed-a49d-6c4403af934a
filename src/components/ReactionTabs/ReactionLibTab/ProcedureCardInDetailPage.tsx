import ButtonWithLoading from '@/components/ButtonWithLoading'
import MoleculeStructure from '@/components/MoleculeStructure'
import StatusRender from '@/components/StatusRender'
import { useProjectMembers } from '@/hooks/useProjectMembers'
import { apiGetExperimentDesignResults, parseResponseResult } from '@/services'
import { ExperimentResult } from '@/services/experiment-design/index.d'
import { AiProcedure } from '@/types/Procedure'
import { IOption } from '@/types/common'
import { encodeString, getWord } from '@/utils'
import {
  ProColumns,
  ProSchemaValueEnumObj,
  ProTable
} from '@ant-design/pro-components'
import { history, useAccess } from '@umijs/max'
import { App, Button, Card, Col, Row, Space, Tag } from 'antd'
import Link from 'antd/lib/typography/Link'
import Paragraph from 'antd/lib/typography/Paragraph'
import Text from 'antd/lib/typography/Text'
import Title from 'antd/lib/typography/Title'
import React, { useState } from 'react'
import { AiProcedureCardProps } from './AiProcedureCard'
import './index.less'
import { getTagForReferenceType, getTagForSameOrSimilerType } from './util'

const columns = (
  projectId: number,
  reactionId: number,
  members: IOption[],
  onJump?: () => void
): ProColumns<ExperimentResult>[] => [
  {
    valueType: 'text',
    title: getWord('experiment-ID'),
    dataIndex: 'experiment_no',
    render: (_, item) => {
      return (
        <a
          onClick={() => {
            onJump?.()
            history.push(
              `/projects/${projectId}/reaction/${reactionId}/experimental-procedure/conclusion/${encodeString(
                JSON.stringify(item.experiment_no)
              )}${
                ['completed', 'failed'].includes(item.experiment_result)
                  ? '#conclusion'
                  : ''
              }`
            )
          }}
        >
          {item.experiment_no}
        </a>
      )
    }
  },
  {
    valueType: 'select',
    title: getWord('pages.experiment.label.personInCharge'),
    dataIndex: 'experiment_owner',
    valueEnum: members.reduce<ProSchemaValueEnumObj>((acc, cur) => {
      if (cur.value && !(cur.value in acc)) {
        acc[cur.value] = { text: cur.label }
      }
      return acc
    }, {})
  },
  {
    valueType: 'dateTime',
    title: getWord('start-time'),
    dataIndex: 'experiment_start_time'
  },
  {
    valueType: 'select',
    title: getWord('conclusion'),
    dataIndex: 'experiment_result',
    render: (_, result) => {
      return (
        <StatusRender
          status={result.experiment_result}
          labelPrefix="pages.experiment.statusLabel"
        />
      )
    }
  },
  {
    valueType: 'text',
    title: getWord('yield'),
    dataIndex: 'experiment_yield',
    renderText: (_, result) => `${result.experiment_yield.toFixed(0)}%`
  }
]

export interface ProcedureCardInDetailPageProps extends AiProcedureCardProps {
  onReference?: (procedure: AiProcedure) => void
  projectId: number
  reactionId: number
}

const ProcedureCardInDetailPage: React.FC<ProcedureCardInDetailPageProps> = ({
  projectId,
  reactionId,
  procedure,
  isSame,
  onReference
}) => {
  const [expanded, setExpanded] = useState<boolean>(false)
  const { members } = useProjectMembers(projectId)
  const { modal } = App.useApp()
  const access = useAccess()
  const {
    text,
    rxn,
    similarity,
    experimentalProcedure,
    yieldString,
    reference: {
      type,
      link,
      title,
      authors,
      date,
      no,
      assignees,
      reference_text
    }
  } = procedure

  const expandText = (
    <Text
      style={{ color: '#1890ff', cursor: 'pointer' }}
      onClick={(e) => {
        e.stopPropagation()
        setExpanded((pre) => !pre)
      }}
    >
      {expanded ? 'less' : 'more'}
    </Text>
  )

  return (
    <Card size="small" className="ai-card-root">
      <Row>
        <Col span={12}>
          <Space className="title" size="large">
            <div>
              <Text type="secondary">{getWord('similarity')}</Text>:
              <Text style={{ color: '#027AFF', paddingLeft: 8 }}>
                {similarity}
              </Text>
            </div>
            <div>
              <Text type="secondary">{getWord('yield')}</Text>:
              <Text style={{ color: '#027AFF', paddingLeft: 8 }}>
                {yieldString}
              </Text>
            </div>
            {!!procedure.scalable && (
              <>
                <Space />
                <Tag color="lime">{getWord('procedure.scalable')}</Tag>
              </>
            )}
            {isSame !== undefined ? getTagForSameOrSimilerType(isSame) : null}
          </Space>
        </Col>
        <Col style={{ marginLeft: 'auto' }}>
          <Space>
            {access?.authCodeList?.includes(
              'reaction-detail.reactionLib.addToMyReaction'
            ) &&
              onReference && (
                <Button
                  type="primary"
                  size="small"
                  onClick={() => onReference(procedure)}
                >
                  {getWord('pages.route.label.addToMyReaction')}
                </Button>
              )}
          </Space>
        </Col>
      </Row>
      <Row gutter={{ md: 16 }}>
        <Col md={8}>
          <MoleculeStructure structure={rxn} />
        </Col>
        <Col md={16}>
          <Text type="secondary" strong>
            Procedure
          </Text>
          <Paragraph
            ellipsis={
              expanded
                ? false
                : { rows: 5, expandable: true, symbol: expandText }
            }
            copyable={{ text }}
          >
            {text || experimentalProcedure || ''}
            {expanded && expandText}
          </Paragraph>
          {isSame && (
            <Text type="secondary">
              {getWord('source')}：
              <ButtonWithLoading
                type="link"
                onClick={async () => {
                  const res = await apiGetExperimentDesignResults({
                    routeParams: `${procedure.id}`
                  })
                  if (parseResponseResult(res).ok) {
                    const ins = modal.info({
                      title: getWord('experiment-log'),
                      width: 800,
                      content: (
                        <ProTable<ExperimentResult>
                          pagination={false}
                          toolbar={{ settings: [], search: false }}
                          search={false}
                          columns={columns(projectId, reactionId, members, () =>
                            ins.destroy()
                          )}
                          dataSource={res.data.experiment_results}
                        />
                      )
                    })
                  }
                }}
              >
                {getWord('experiment-history')}
              </ButtonWithLoading>
            </Text>
          )}

          {!isSame && (
            <div className="reference-wrapper">
              <Title level={5}>Reference {getTagForReferenceType(type)}</Title>
              {type === 'patent' && (no || assignees) && (
                <Text
                  style={{
                    textAlign: 'right',
                    display: 'block',
                    width: '100%'
                  }}
                >
                  {no && (
                    <>
                      Patent No: <Text copyable>{no}</Text>
                    </>
                  )}
                  {assignees && <Text>, assigned by {assignees}</Text>}
                  <br />
                </Text>
              )}
              {link && (
                <>
                  <Text type="secondary" copyable>
                    {link ? <Link href={link}>{title}</Link> : title}
                  </Text>
                  <br />
                </>
              )}
              {reference_text && (
                <>
                  <Text type="secondary" copyable>
                    {reference_text}
                  </Text>
                  <br />
                </>
              )}
              <Text
                italic
                style={{ textAlign: 'right', display: 'block', width: '100%' }}
              >
                {authors}
              </Text>
              {date && (
                <Text
                  style={{
                    textAlign: 'right',
                    display: 'block',
                    width: '100%'
                  }}
                >
                  {date}
                </Text>
              )}
            </div>
          )}
        </Col>
      </Row>
    </Card>
  )
}

export default ProcedureCardInDetailPage
