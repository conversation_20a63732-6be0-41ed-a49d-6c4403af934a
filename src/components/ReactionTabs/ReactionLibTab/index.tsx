import { ReactComponent as EmptyImage } from '@/assets/svgs/nodata.svg'
import StatusTip from '@/components/StatusTip'
import { useUserSettingQuery } from '@/hooks/useUserSetting'
import { Reaction } from '@/pages/route/util'
import { Paginate } from '@/services/brain'
import { AiProcedure } from '@/types/Procedure'
import { getWord } from '@/utils'
import { ProList } from '@ant-design/pro-components'
import { ConfigProvider } from 'antd'
import React, { useState } from 'react'
import ReactDOM from 'react-dom'
import AiProcedureCard from './AiProcedureCard'
import FilterModal from './Filter/Modal'
import { defaultTotal, ProcedureFilter, useProcedure } from './useProcedure'

const defaultRender =
  (rxns?: { rxn: string; name: string }[]) =>
  (procedure: AiProcedure, isSame?: boolean) =>
    (
      <AiProcedureCard
        procedure={procedure}
        isSame={isSame}
        name={rxns?.find((r) => r.rxn === procedure.query)?.name}
      />
    )

export interface ReactionLibTabProps {
  reaction?: Reaction
  rxns?: { rxn: string; name: string }[]
  renderer?: (procedure: AiProcedure, isSame?: boolean) => React.ReactNode
  actionSlot?: HTMLElement
}

const ReactionLibTab: React.FC<ReactionLibTabProps> = ({
  reaction,
  actionSlot,
  rxns,
  renderer = defaultRender(rxns)
}) => {
  const { setting: { procedure = {} } = {} } = useUserSettingQuery()
  const [filtersUpdate, setFiltersUpdate] = useState<ProcedureFilter>()
  const filters = { ...procedure, ...filtersUpdate }
  const { isLoading, procedures } = useProcedure(
    reaction,
    rxns?.map((r) => r.rxn),
    filters
  )

  const [paginate, setPaginate] = useState<Paginate>({ page: 1, pageSize: 10 })

  return (
    <ConfigProvider
      renderEmpty={() => (
        <StatusTip image={<EmptyImage />} des={getWord('noticeIcon.empty')} />
      )}
    >
      <ProList
        ghost
        loading={isLoading}
        pagination={
          procedures?.length
            ? {
                current: paginate.page,
                pageSize: paginate.pageSize,
                simple: true,
                total: Math.min(procedures?.length || 0, defaultTotal),
                onChange: (page, pageSize) => setPaginate({ page, pageSize })
              }
            : false
        }
        renderItem={(item) => renderer(item, item.isSame)}
        grid={{ column: 1 }}
        dataSource={procedures}
      />
      {actionSlot &&
        ReactDOM.createPortal(
          <FilterModal onFinished={setFiltersUpdate} initialFilter={filters} />,
          actionSlot
        )}
    </ConfigProvider>
  )
}

export default ReactionLibTab
