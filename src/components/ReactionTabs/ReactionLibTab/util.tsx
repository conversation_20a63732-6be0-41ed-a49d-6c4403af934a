import { ProcedureRxnMatchResult } from '@/services/brain'
import { AiProcedure } from '@/types/Procedure'
import { getWord, isEN } from '@/utils'
import { Tag } from 'antd'
import { ReactElement } from 'react'

export const transformReferenceType = (
  type: ProcedureRxnMatchResult['reference_type']
): AiProcedure['reference']['type'] => {
  switch (type) {
    case 'JOURNAL':
      return 'journal'
    case 'Patent':
      return 'patent'
    default:
      return type
  }
}

export const transformProcedureMatchResponse = (
  {
    id,
    experimental_procedure = '',
    query = '',
    procedure = '',
    similarity = 0,
    rxn = '',
    rxn_yields = '',
    yields,
    transformation = '',
    reference_type = 'JOURNAL',
    title = '',
    authors = '',
    date = '',
    assignees = '',
    reference_text = '',
    patent_id = '',
    is_scalable = false
  }: ProcedureRxnMatchResult,
  isSame: boolean = false
): AiProcedure => ({
  id,
  rxn,
  isSame,
  text: procedure || '',
  experimentalProcedure: experimental_procedure || '',
  transformation: transformation || '',
  query: query || '',
  reference: {
    type: transformReferenceType(reference_type),
    title: title || '',
    authors: authors || '',
    date: date || '',
    no: patent_id || '',
    reference_text: reference_text || '',
    assignees: assignees || ''
  },
  yieldString: rxn_yields || '',
  yieldNumber: yields || undefined,
  similarity: similarity || 0,
  scalable: is_scalable || false
})

export const getTagForReferenceType = (
  type: AiProcedure['reference']['type']
): ReactElement => {
  switch (type) {
    case 'journal':
      return <Tag color="green">Journal</Tag>
    case 'patent':
      return <Tag color="blue">Patent</Tag>
    default:
      return <Tag color="orange">Custom</Tag>
  }
}

export const getTagForSameOrSimilerType = (
  isSame: boolean,
  name?: string
): ReactElement => {
  switch (isSame) {
    case true:
      return (
        <Tag color="green">
          {name
            ? isEN()
              ? `The Same Reaction as ${name}`
              : `与${name}是${getWord('same-reaction')}`
            : getWord('same-reaction')}
        </Tag>
      )

    case false:
      return (
        <Tag color="blue">
          {name
            ? isEN()
              ? `Reference for ${name}`
              : `与${name}是${getWord('reference')}`
            : getWord('reference')}
        </Tag>
      )
    default:
      return <></>
  }
}
