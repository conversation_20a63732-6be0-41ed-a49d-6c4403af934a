import MoleculeStructure from '@/components/MoleculeStructure'
import { AiProcedure } from '@/types/Procedure'
import { getWord } from '@/utils'
import { PlusOutlined } from '@ant-design/icons'
import { useAccess } from '@umijs/max'
import { Button, Card, Space, Tag } from 'antd'
import Link from 'antd/lib/typography/Link'
import Paragraph from 'antd/lib/typography/Paragraph'
import Text from 'antd/lib/typography/Text'
import Title from 'antd/lib/typography/Title'
import React, { useState } from 'react'
import './index.less'
import { getTagForReferenceType, getTagForSameOrSimilerType } from './util'

export interface AiProcedureCardProps {
  procedure: AiProcedure
  name?: string
  isSame?: boolean
  onReference?: (procedure: AiProcedure) => void
}

const AiProcedureCard: React.FC<AiProcedureCardProps> = ({
  procedure,
  name,
  isSame,
  onReference
}) => {
  const access = useAccess()
  const [expanded, setExpanded] = useState<boolean>(false)
  const {
    text,
    reference,
    rxn,
    similarity,
    experimentalProcedure,
    yieldString
  } = procedure
  const { type, link, title, authors, date, no, assignees, reference_text } =
    reference

  const expandText = (
    <Text
      style={{ color: '#1890ff', cursor: 'pointer' }}
      onClick={(e) => {
        e.stopPropagation()
        setExpanded((pre) => !pre)
      }}
    >
      {expanded ? 'less' : 'more'}
    </Text>
  )

  return (
    <Card size="small" className="ai-card-root">
      <div className="title">
        <div className="info-wrapper">
          <div>
            <Text type="secondary">{getWord('similarity')}</Text>:
            <Text style={{ color: '#027AFF', paddingLeft: 8 }}>
              {similarity}
            </Text>
          </div>
          <Space />
          <div>
            <Text type="secondary">{getWord('yield')}</Text>:
            <Text style={{ color: '#027AFF', paddingLeft: 8 }}>
              {yieldString}
            </Text>
          </div>
          <div className="buttons-wrapper">
            {!!procedure.scalable && (
              <>
                <Space />
                <Tag color="lime">{getWord('procedure.scalable')}</Tag>
              </>
            )}
            {isSame !== undefined ? (
              <>
                <Space />
                {getTagForSameOrSimilerType(isSame, name)}
              </>
            ) : null}
            {access?.authCodeList?.includes(
              'view-by-backbone.tab.recommend.addToMyReaction'
            ) &&
              onReference && (
                <Button
                  icon={<PlusOutlined />}
                  size="small"
                  onClick={() => onReference(procedure)}
                >
                  {getWord('pages.route.label.addToMyReaction')}
                </Button>
              )}
          </div>
        </div>
      </div>
      <Space />
      <div>
        <MoleculeStructure structure={rxn} />
      </div>
      <div className="procedure-wrapper">
        <Title level={5}>Procedure</Title>
        <Paragraph
          ellipsis={
            expanded
              ? false
              : {
                  rows: 5,
                  expandable: true,
                  symbol: expandText
                }
          }
          copyable={{ text }}
        >
          {text || experimentalProcedure || ''}
          {expanded && expandText}
        </Paragraph>
      </div>
      <div className="reference-wrapper">
        <Title level={5}>Reference {getTagForReferenceType(type)}</Title>
        {type === 'patent' && (no || assignees) && (
          <Text style={{ textAlign: 'right', display: 'block', width: '100%' }}>
            {no && (
              <>
                Patent No: <Text copyable>{no}</Text>
              </>
            )}
            {assignees && <Text>, assigned by {assignees}</Text>}
            <br />
          </Text>
        )}
        {link && (
          <>
            <Text type="secondary" copyable>
              {link ? <Link href={link}>{title}</Link> : title}
            </Text>
            <br />
          </>
        )}
        {reference_text && (
          <>
            <Text type="secondary" copyable>
              {reference_text}
            </Text>
            <br />
          </>
        )}
        <Text
          italic
          style={{ textAlign: 'right', display: 'block', width: '100%' }}
        >
          {authors}
        </Text>
        {date && (
          <Text style={{ textAlign: 'right', display: 'block', width: '100%' }}>
            {date}
          </Text>
        )}
      </div>
    </Card>
  )
}

export default AiProcedureCard
