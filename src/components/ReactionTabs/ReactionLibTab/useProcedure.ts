import { fetchAllRetroReactions } from '@/pages/reaction/util'
import { getRxnFromReaction, Reaction } from '@/pages/route/util'
import { ProcedureRxnMatchResponse, service } from '@/services/brain'
import { AiProcedure } from '@/types/Procedure'
import { useQuery } from '@tanstack/react-query'
import { transformProcedureMatchResponse } from './util'

export interface ProcedureFilter {
  has_valid_procedure?: boolean
  same?: boolean
  similar?: boolean
  need_scalable?: boolean // true表示必须适合工艺放大，缺省为false
  year_min?: number // int, 表示过滤最早年限(包含), null表示不限制, 缺省为null,
  year_max?: number // int, 表示过滤最晚年限(包含), null表示不限制, 缺省为null,
  impact_factor_min?: number // float, 缺省为null, 表示不限制
  impact_factor_max?: number // float, 缺省为null，表示不限制
}

export const defaultTotal = 30

const fetchInfo = async (
  reaction: Reaction,
  searchRxns?: string[],
  filters?: ProcedureFilter
): Promise<AiProcedure[]> => {
  if (!reaction.reactants.length) return []

  const rxns =
    searchRxns ||
    (await fetchAllRetroReactions(reaction))?.map((r) => getRxnFromReaction(r))
  const simple_rxn = getRxnFromReaction(reaction)

  const res = await service('procedure/rxn-match', {
    method: 'post',
    normalizeData: false,
    data: {
      simple_rxn,
      rxns: rxns?.length ? rxns : [simple_rxn],
      similar_topk: defaultTotal,
      ...filters
    }
  })
    .select()
    .get()

  if (res) {
    const { similar_procedures, same_procedures } =
      res as unknown as ProcedureRxnMatchResponse

    return [
      ...same_procedures.map((p) => transformProcedureMatchResponse(p, true)),
      ...similar_procedures.map((p) =>
        transformProcedureMatchResponse(p, false)
      )
    ]
  }
  throw new Error('Network response was not ok')
}

export const useProcedure = (
  reaction?: Reaction,
  rxns?: string[],
  filters?: ProcedureFilter
) => {
  const { data, error, isLoading, refetch } = useQuery({
    queryKey: ['procedure/rxn-match', reaction, rxns, filters],
    queryFn: () => (reaction ? fetchInfo(reaction, rxns, filters) : []),
    enabled: !!reaction
  })

  const procedures = data?.filter((p) =>
    (p.isSame ? filters?.same : filters?.similar) === false ? false : true
  )

  return { procedures, total: procedures?.length, error, isLoading, refetch }
}
