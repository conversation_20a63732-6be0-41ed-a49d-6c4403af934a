.ai-card-root {
  margin: 12px 0;
  border-radius: 12px;
  .title {
    display: flex;
    .info-wrapper {
      display: flex;
      gap: 20px;
      width: 100%;
      div {
        display: flex;
        align-items: center;
      }
    }
    .buttons-wrapper {
      margin-left: auto;
    }
    .reference-btn {
      margin-left: auto;
    }
  }
  .procedure-wrapper {
    margin: 8px 0;
  }
}

.procedure-detail-card-root {
  margin: 8px 0;
  border-radius: 8px;
  .reference-type-wrapper {
    padding-left: 8px;
  }
  .title {
    display: flex;
    .yield-wrapper {
      margin: auto;
    }
  }
  .cancel-apply-btn {
    background-color: #52c41a;
    border-color: #52c41a;
    &:hover {
      background-color: #52c41a;
      border-color: #52c41a;
    }
  }
  .apply-btn {
    background-color: #faad14;
    border-color: #faad14;
    &:hover {
      background-color: #faad14;
      border-color: #faad14;
    }
  }
  .smiles-input {
    .ant-upload-list-picture-card-container,
    .ant-upload-select-picture-card {
      width: 60px;
      height: 60px;
    }
    .ant-upload-list-item-name {
      display: none !important; // FIXME
    }
    &.hide-upload-btn {
      .ant-upload {
        display: none;
      }
    }
  }
  .smiles-display {
    width: 60px;
    height: 60px;
  }
}
