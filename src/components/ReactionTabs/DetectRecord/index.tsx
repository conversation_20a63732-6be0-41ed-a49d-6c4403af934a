import AnalysisRecord from '@/pages/experimental-procedure/conclusion/sections/AnalysisRecord'
import { apiSearchExperimentCheck, parseResponseResult } from '@/services'
import { OperationCheckResponse } from '@/types/models/check-record'
import { getWord } from '@/utils'
import { useModel, useParams, useSearchParams } from '@umijs/max'
import cs from 'classnames'
import { useEffect } from 'react'
import TabWithNumber from '../TabWithNumber'
import styles from './index.less'

export default function DetectRecordTab() {
  const { searchExperimentCheck, experimentCheckList } = useModel('experiment')
  const [searchParams, setSearchParams] = useSearchParams()
  const { reactionId } = useParams()
  useEffect(() => {
    searchExperimentCheck({ project_reaction_id: reactionId })
  }, [])

  return (
    <div className={cs(styles.detectRecord)}>
      <AnalysisRecord
        isAnalysisTab={true}
        analysisData={experimentCheckList}
        requestEvent={(params) => {
          searchExperimentCheck({ ...params, project_reaction_id: reactionId })
          let tab = searchParams.get('tab')
          if (tab) {
            setSearchParams(
              {
                tab
              },
              { replace: true }
            )
          }
        }}
      />
    </div>
  )
}

export const getDetectRecordTabConfig = (
  experimentNo: number,
  projectId?: number,
  projectReactionId?: number,
  updateEvent?: Record<never, never>
) => ({
  key: 'detect-record',
  label: (
    <TabWithNumber
      title={getWord('pages.experiment.label.operation.detectRecord')}
      getNumber={async () => {
        if (!projectReactionId) return
        const res: { data: OperationCheckResponse } =
          await apiSearchExperimentCheck({
            data: {
              project_reaction_id: projectReactionId
            }
          })
        if (parseResponseResult(res).ok) return res.data.length
      }}
      refetchEvent={updateEvent}
    />
  ),
  children: projectId && projectReactionId ? <DetectRecordTab /> : null
})
