import { LoadingOutlined } from '@ant-design/icons'
import { isNil } from 'lodash'
import React, { useEffect, useState } from 'react'

export interface TabWithNumberProps {
  title: string
  getNumber?: () => Promise<number | void> | number | void
  refetchEvent?: Record<never, never>
}

const TabWithNumber: React.FC<TabWithNumberProps> = ({
  title,
  getNumber,
  refetchEvent
}) => {
  const [loading, setLoading] = useState<boolean>(false)
  const [number, setNumber] = useState<number | void>()
  useEffect(() => {
    let unmount = false
    if (!getNumber) return
    Promise.resolve(getNumber())
      .then((n) => !unmount && setNumber(isNil(n) ? undefined : n))
      .finally(() => !unmount && setLoading(false))

    return () => {
      unmount = true
    }
  }, [getNumber, refetchEvent])

  return (
    <>
      {title}
      {loading ? <LoadingOutlined /> : isNil(number) ? '' : `(${number})`}
    </>
  )
}

export default TabWithNumber
