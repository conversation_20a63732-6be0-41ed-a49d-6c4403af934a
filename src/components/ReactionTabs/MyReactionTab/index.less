.my-reaction-card-root {
  .ant-pro-card-hoverable {
    cursor: default;
  }
  .ant-pro-card {
    .ant-pro-card .ant-pro-card-header {
      padding: 0;
    }
    .ant-pro-card-body {
      padding: 8px 4px !important;
    }
    .ant-pro-checkcard-content {
      display: none;
    }
    .ant-pro-checkcard-body {
      padding: 8px;
    }
  }
  .divider-wrapper {
    display: flex;
    justify-content: center;
    .divider {
      height: 100%;
    }
  }
  .card-root {
    padding: 4px;
    > .ant-row {
      width: 100%;
    }
    .structrue-wrapper {
      height: 200px;
    }
    .actions-wrapper {
      margin-left: auto;
    }
  }
}
