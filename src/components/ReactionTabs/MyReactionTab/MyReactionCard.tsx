import MoleculeStructure from '@/components/MoleculeStructure'
import { useCopyToClipboard } from '@/components/MoleculeStructure/util'

import { Procedure } from '@/services/brain'
import { getWord, isEN } from '@/utils'
import { CopyOutlined } from '@ant-design/icons'
import { useAccess } from '@umijs/max'
import { App, Button, Row, Space, Typography } from 'antd'
import { isNil } from 'lodash'
import React, { useState } from 'react'
import './index.less'

export interface MyReactionCardProps {
  procedure: Procedure
  onEdit?: (procedure: Procedure) => void
  onDelete?: (procedure: Procedure) => void
  onSelectProcedure?: (p?: string, procedures?: Procedure[]) => void
}

const MyReactionCard: React.FC<MyReactionCardProps> = ({
  procedure,
  onEdit: propOnEdit,
  onDelete: propOnDelete
}) => {
  const access = useAccess()
  const { yields, smiles, procedure: experimentalProcedure } = procedure
  const { modal } = App.useApp()
  const [expanded, setExpanded] = useState<boolean>(false)
  const onEdit = () => propOnEdit?.(procedure)
  const { copy } = useCopyToClipboard()
  const onDelete = () => {
    modal.confirm({
      title: getWord('pages.route.label.confirmToDeleteMyReaction'),
      onOk: () => propOnDelete?.(procedure)
    })
  }

  const expandText = (
    <Typography.Text
      style={{ color: '#1890ff', cursor: 'pointer' }}
      onClick={(e) => {
        e.stopPropagation()
        setExpanded((pre) => !pre)
      }}
    >
      {isEN() ? (expanded ? 'less' : 'more') : expanded ? '收起' : '展开'}
    </Typography.Text>
  )

  return (
    <Row className="card-root">
      <Row>
        <Space>
          <Typography.Text>{getWord('yield')}: </Typography.Text>
          <Typography.Text type="secondary">
            {isNil(yields) ? '-' : `${yields}%`}
          </Typography.Text>
        </Space>

        <Space className="actions-wrapper" size="small">
          {access?.authCodeList?.includes(
            'view-by-backbone.tab.myReaction.edit'
          ) ? (
            <Button type="link" onClick={onEdit}>
              {getWord('edit')}
            </Button>
          ) : (
            ''
          )}
          {access?.authCodeList?.includes(
            'view-by-backbone.tab.myReaction.del'
          ) ? (
            <Button type="link" onClick={onDelete}>
              {getWord('del')}
            </Button>
          ) : (
            ''
          )}
        </Space>
      </Row>
      <Row>
        <MoleculeStructure structure={smiles} className="structrue-wrapper" />
      </Row>
      <Row>
        <Row style={{ width: '100%' }}>
          <Typography.Title level={5}>Procedure</Typography.Title>
          <Button
            type="link"
            icon={<CopyOutlined />}
            style={{ marginLeft: 'auto' }}
            onClick={() => copy(experimentalProcedure)}
          >
            {getWord('copy-full')}
          </Button>
        </Row>
        <Typography.Paragraph
          ellipsis={
            expanded
              ? false
              : {
                  rows: 5,
                  expandable: true,
                  symbol: expandText
                }
          }
        >
          {experimentalProcedure || ''}
          {expanded && expandText}
        </Typography.Paragraph>
      </Row>
    </Row>
  )
}

export default MyReactionCard
