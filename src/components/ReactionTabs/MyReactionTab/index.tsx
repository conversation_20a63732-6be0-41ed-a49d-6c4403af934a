import MyReactionDialog from '@/pages/reaction/component/MyReactionDialog'
import { getRxnFromReaction, Reaction } from '@/pages/route/util'
import {
  Paginate,
  Procedure,
  ProjectReaction,
  query,
  service
} from '@/services/brain'
import { getWord } from '@/utils'
import { ProList, ProListMetas, ProSkeleton } from '@ant-design/pro-components'
import React, { useEffect, useState } from 'react'
import { useMyReactionDialog } from '../ReactionProcedureTab/useMyReactionDialog'
import TabWithNumber from '../TabWithNumber'
import { fetchProceduresWithReaction } from '../util'
import './index.less'
import MyReactionCard from './MyReactionCard'

export interface MyReactionTabProps {
  reaction?: Reaction
  projectReaction?: ProjectReaction
  onSelectProcedure?: (p?: string, procedures?: Procedure[]) => void
  cardMeta?: ProListMetas
  onUpdate?: () => void
  refetch?: boolean
}

const MyReactionTab: React.FC<MyReactionTabProps> = ({
  reaction,
  projectReaction,
  onSelectProcedure,
  onUpdate,
  refetch
}) => {
  const [procedures, setProcedures] = useState<Procedure[]>([])
  const [paginate, setPaginate] = useState<Paginate>({ page: 1, pageSize: 10 })
  const [loading, setLoading] = useState<boolean>(false)
  const updateProcedures = async (
    reaction?: Reaction,
    projectReaction?: ProjectReaction
  ) => {
    const procedures = await fetchProceduresWithReaction(
      reaction,
      projectReaction
    )
    setProcedures(procedures)
    onUpdate?.()
  }

  const onDelete = async (procedure: Procedure): Promise<void> => {
    if (!reaction) return
    const data = await query<ProjectReaction>(
      'project-reaction/get-or-create',
      {
        method: 'post',
        data: {
          reaction: getRxnFromReaction(reaction),
          projectId: projectReaction?.project?.id
        },
        normalizeData: false
      }
    ).get()
    const fullProjectReaction = (data as unknown as ProjectReaction[])?.[0]

    if (!fullProjectReaction || !procedure.id) return
    const newEffective = new Set<number>(
      fullProjectReaction.effective_procedures
    )
    newEffective.delete(procedure.id)
    const newHistory = new Set<number>(fullProjectReaction.history_procedures)
    newHistory.add(procedure.id)

    const { data: newProjectReaction } = await service<ProjectReaction>(
      'project-reactions'
    ).update(fullProjectReaction.id, {
      effective_procedures: Array.from(newEffective.values()),
      history_procedures: Array.from(newHistory.values())
    })
    if (newProjectReaction) {
      await updateProcedures(reaction, projectReaction)
    }
  }

  const { props, setOpen, setProcedure } = useMyReactionDialog(
    reaction || { product: '', reactants: [] },
    'edit',
    projectReaction?.project?.id,
    () => {
      updateProcedures(reaction, projectReaction)
      setProcedure()
    }
  )

  useEffect(() => {
    if (refetch) {
      setLoading(true)
      updateProcedures(reaction, projectReaction).then(() => setLoading(false))
    }
  }, [refetch])

  useEffect(() => {
    setLoading(true)
    updateProcedures(reaction, projectReaction).then(() => setLoading(false))
  }, [reaction])

  if (!procedures) {
    return <ProSkeleton type="list" />
  }
  return (
    <>
      <ProList<Procedure>
        className="my-reaction-card-root"
        ghost
        loading={loading}
        pagination={{
          current: paginate.page,
          pageSize: paginate.pageSize,
          simple: true,
          total: procedures.length,
          onChange: (page, pageSize) => {
            setPaginate({ page, pageSize })
          }
        }}
        metas={{
          content: {
            render: (_, item) => (
              <MyReactionCard
                procedure={item}
                onEdit={(p) => {
                  setProcedure(p)
                  setOpen(true)
                }}
                onDelete={onDelete}
                onSelectProcedure={onSelectProcedure}
              />
            )
          }
        }}
        showActions="hover"
        showExtra="hover"
        grid={{ column: 1 }}
        dataSource={procedures}
      />
      <MyReactionDialog {...props} />
    </>
  )
}

export default MyReactionTab

export const useGetMyReactionTabConfig = (
  reaction?: Reaction,
  projectReaction?: ProjectReaction,
  onSelectProcedure?: (p?: string, procedures?: Procedure[]) => void,
  activeKey?: string,
  onUpdate?: () => void,
  cardMeta?: ProListMetas
) => {
  const [event, setEvent] = useState<Record<never, never>>({})

  const label = (
    <TabWithNumber
      title={getWord('menu.list.workspace.myReaction')}
      getNumber={async () => {
        if (!reaction?.reactants.length) return
        return (await fetchProceduresWithReaction(reaction, projectReaction))
          .length
      }}
      refetchEvent={event}
    />
  )
  const tab = (
    <MyReactionTab
      onSelectProcedure={onSelectProcedure}
      reaction={reaction}
      refetch={activeKey === 'my-reaction'}
      projectReaction={projectReaction}
      cardMeta={cardMeta}
      onUpdate={() => {
        setEvent({})
        onUpdate?.()
      }}
    />
  )

  return { key: 'my-reaction', label, children: reaction ? tab : null }
}
