import { getWord } from '@/utils'
import { LoadingComProps } from './index.d'
import styles from './index.less'
export default function LoadingCom(props: LoadingComProps) {
  return (
    <div className={styles.loadingCom}>
      <div>
        <img
          style={{ width: '200px', height: '200px' }}
          src={require('@/assets/svgs/lottie/search.gif')}
        />
      </div>
      <span>{props?.loadingText || getWord('data-loading')}</span>
    </div>
  )
}
