import LazySmileDrawer from '@/components/LazySmileDrawer'
import MaterialsTable from '@/components/MaterialsTable'
import ModalBase from '@/components/ModalBase'
import SectionTitle from '@/components/SectionTitle'
import { getWord } from '@/utils'
import type { ReagentListProps } from './index.d'
import styles from './index.less'

export default function ReagentList(props: ReagentListProps) {
  const { structure, material_table, dialogProps } = props
  return (
    <div onClick={(e) => e.stopPropagation()}>
      <ModalBase
        {...dialogProps}
        footer={null}
        cancelButtonProps={{ hidden: true }}
        width="80%"
        centered
      >
        <SectionTitle word={getWord('reaction')} />
        <LazySmileDrawer className={styles.structure} structure={structure} />
        <SectionTitle word={getWord('pages.reaction.label.material-sheet')} />
        <MaterialsTable material_table={material_table} />
      </ModalBase>
    </div>
  )
}
