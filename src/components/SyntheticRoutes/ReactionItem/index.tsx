import ReactionCard from '@/components/ReactionCard'
import { isValidArray } from '@/utils'
import { useModel } from '@umijs/max'
import type { ReactionItemProps } from './index.d'

export default function ReactionItem(props: ReactionItemProps) {
  const { item } = props
  const { getProfileInfo } = useModel('commend')
  return (
    <ReactionCard
      isPlayground={true}
      enableToReaction={false}
      reaction={{
        ...item,
        reaction: isValidArray(item?.reactants)
          ? `${item?.reactants.join('.')}>>${item?.product}`
          : null,
        updatedAt: item?.updated_at,
        commendCount: item?.content_count
        /* FIXME id not the reaction id; reaction?.id */
      }}
      onAction={() =>
        getProfileInfo({
          _commendSuject: item,
          collection_class: 'retro-reaction'
        })
      }
    />
  )
}
