import { ReactComponent as CopyMaterialIcon } from '@/assets/svgs/route-operation/copy-material.svg'
import { useCopyToClipboard } from '@/components/MoleculeStructure/util'
import SyntheticLinkDisplay from '@/components/SyntheticLinkDisplay'
import { backboneToLink } from '@/types/SyntheticRoute/SyntheticLink'
import { Card } from 'antd'
import React from 'react'
import { calDeepth } from '../util'
import RetroRouteTitle from './RetroRouteTitle'
import type { GeneratedResultCardProps } from './index.d'
import './index.less'
const GeneratedResultCard: React.FC<GeneratedResultCardProps> = ({
  result,
  isPlayground
}) => {
  const { copy } = useCopyToClipboard()
  return (
    <Card
      type="inner"
      title={<RetroRouteTitle route={result} isPlayground={isPlayground} />}
      className="ai-synthetic-route-card-root"
    >
      <SyntheticLinkDisplay
        node={backboneToLink(result.backbone)}
        withWrapper
        customProps={{
          reactionWrapper: { className: 'reaction-wrapper' },
          structureWrapper: { className: 'structure-wrapper' }
        }}
        customChildren={{
          structure: (_, node) => (
            <>
              <div className="structure-info">{calDeepth(node) + 1}</div>
            </>
          ),
          reaction: (target, reactant) => (
            <div className="reaction-btns">
              <div
                className="reaction-copy-btn reaction-btn"
                onClick={() => copy(`${reactant}>>${target}`)}
              >
                <CopyMaterialIcon />
              </div>
            </div>
          )
        }}
      />
    </Card>
  )
}

export default GeneratedResultCard
