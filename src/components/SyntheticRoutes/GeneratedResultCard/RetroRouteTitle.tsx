import { calHasBranch } from '@/types/SyntheticRoute/SyntheticTree'
import { useModel, useSearchParams } from '@umijs/max'
import { Dayjs } from 'dayjs'
import React from 'react'
import CardTitle from '../CardTitle'
import type { CardAction } from '../CardTitle/index'
import type { RetroRouteTitleProps } from '../CardTitle/index.d'
const RetroRouteTitle: React.FC<RetroRouteTitleProps> = ({
  route,
  isPlayground
}) => {
  const { updatePagenate, cacheFilterInfo, curFilterInfo } =
    useModel('compound')
  const { getProfileInfo } = useModel('commend')
  const [, setSearchParams] = useSearchParams()
  const treesNumHasBranch = route.main_trees
    .map(calHasBranch)
    .reduce((acc, cur) => (cur ? acc + 1 : acc), 0)

  let groupNum = route?.group_conditions
    ? (route.group_conditions[curFilterInfo?.group] as number)
    : undefined
  const actions: CardAction[] = isPlayground
    ? ['view', 'feedback']
    : ['view', 'collect', 'feedback']
  return (
    <CardTitle
      item={route}
      updatedAt={(route?.updated_at || route?.updatedAt) as Dayjs | undefined}
      isCollected={route?.collected}
      routeNo={isPlayground ? route?.collection_id : route?.id}
      commendCount={route?.content_count}
      stepNum={route.backbone.length - 1}
      hasBranches={treesNumHasBranch > 1}
      hasBranch={treesNumHasBranch > 0}
      group={groupNum}
      handleFilterSimilar={async () => {
        cacheFilterInfo({
          ...route,
          similarId: groupNum
        })
        updatePagenate({ page: 1, pageSize: 10 })
        setSearchParams({ page: '1', pageSize: '10' }, { replace: true })
      }}
      actions={actions}
      onAction={async (t) => {
        if (t === 'feedback') {
          getProfileInfo({
            _commendSuject: route,
            collection_class: 'retro-backbone',
            isPlayground
          })
        }
      }}
    />
  )
}

export default RetroRouteTitle
