.ai-synthetic-route-card-root {
  margin: 12px 0;
  border-radius: 4px;
  .ant-card-body {
    display: flex;
    align-items: center;
    height: 220px;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .synthetic-link-display-node-root {
    padding: 8px 0;
  }

  .reaction-wrapper,
  .structure-wrapper {
    position: relative;
    & .structure-info {
      position: absolute;
      top: 0;
      left: 4px;
    }
  }
  .structure-wrapper {
    width: 184px;
    border: 2px solid #6bd3fd;
  }
  .structure-wrapper:first-child {
    border-color: #fecf7c;
  }
  .structure-wrapper:last-child {
    border-color: #979797;
    border-style: dashed;
  }
  .reaction-btns {
    .reaction-btn {
      width: 16px;
      height: 16px;
      cursor: pointer;

      svg > path {
        fill: #bfbfbf;
      }
      &:hover {
        svg > path {
          fill: #1a90ff;
        }
      }
    }
    position: absolute;
    top: calc(50% - 20px);
    right: 0;
    left: 0;
    display: flex;
    justify-content: center;
    margin-right: auto;
    margin-left: auto;
  }
}
