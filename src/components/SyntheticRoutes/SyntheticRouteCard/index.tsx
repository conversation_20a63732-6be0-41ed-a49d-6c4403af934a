/* 我的路线 */
import { ReactComponent as CopyMaterialIcon } from '@/assets/svgs/route-operation/copy-material.svg'
import { useCopyToClipboard } from '@/components/MoleculeStructure/util'
import SyntheticLinkDisplay from '@/components/SyntheticLinkDisplay'
import { syntheticTreeToLink } from '@/types/SyntheticRoute/SyntheticLink'
import { useParams } from '@umijs/max'
import { Card } from 'antd'
import React from 'react'
import { calDeepth } from '../util'
import ProjectRouteTitle from './ProjectRouteTitle'
import type { SyntheticRouteCardProps } from './index.d'
import './index.less'

const SyntheticRouteCard: React.FC<SyntheticRouteCardProps> = ({
  route,
  hiddenTitle,
  isPlayground,
  targetMolecule
}) => {
  const { id: moleculeId } = useParams<{ id: string }>()
  const { copy } = useCopyToClipboard()

  return (
    <Card
      type="inner"
      title={
        !hiddenTitle && moleculeId ? (
          <ProjectRouteTitle
            route={route}
            isPlayground={isPlayground}
            targetMolecule={targetMolecule}
          />
        ) : null
      }
      className="synthetic-route-card-root"
    >
      {route.main_tree ? (
        <SyntheticLinkDisplay
          node={syntheticTreeToLink(route.main_tree)}
          withWrapper
          customProps={{
            reactionWrapper: { className: 'reaction-wrapper' },
            structureWrapper: { className: 'structure-wrapper' }
          }}
          customChildren={{
            structure: (_, node) => (
              <div className="structure-info">{calDeepth(node) + 1}</div>
            ),
            reaction: (target, reactant) => (
              <div className="reaction-btns">
                <div
                  className="reaction-copy-btn reaction-btn"
                  onClick={() => copy(`${reactant}>>${target}`)}
                >
                  <CopyMaterialIcon />
                </div>
              </div>
            )
          }}
        />
      ) : (
        ''
      )}
    </Card>
  )
}
export default SyntheticRouteCard
