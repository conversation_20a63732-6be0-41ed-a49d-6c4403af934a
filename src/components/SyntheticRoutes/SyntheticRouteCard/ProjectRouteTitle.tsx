import { ProjectCompound, ProjectRoute } from '@/services/brain'
import { useModel } from '@umijs/max'
import { Dayjs } from 'dayjs'
import React from 'react'
import CardTitle from '../CardTitle'
import type { CardAction } from '../CardTitle/index.d'

export interface ProjectRouteTitleProps {
  route: ProjectRoute
  isPlayground?: boolean
  targetMolecule?: ProjectCompound
}

const ProjectRouteTitle: React.FC<ProjectRouteTitleProps> = ({
  route,
  targetMolecule,
  isPlayground
}) => {
  const { getProfileInfo } = useModel('commend')

  const actions: CardAction[] =
    isPlayground || route.status !== 'confirmed'
      ? ['view', 'feedback']
      : ['view', 'setAsDefault', 'feedback']
  return (
    <CardTitle
      routeName={route?.name}
      updatedAt={(route?.updated_at || route?.updatedAt) as Dayjs | undefined}
      updateTime={(route?.updatedAt || route?.createdAt) as Dayjs | undefined}
      routeNo={route.id}
      route={route.main_tree}
      actions={actions}
      targetMolecule={targetMolecule}
      commendCount={route?.content_count as number | undefined}
      onAction={(t) => {
        if (t === 'feedback') {
          getProfileInfo({
            _commendSuject: route,
            collection_class: 'project-route'
          })
        }
      }}
      routeStatus={route.status}
    />
  )
}

export default ProjectRouteTitle
