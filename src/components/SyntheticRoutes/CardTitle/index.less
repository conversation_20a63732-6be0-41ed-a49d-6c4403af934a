@import '@/style/variables.less';
.card-title {
  display: flex;
  justify-content: space-between;
  width: 100%;
  overflow: hidden;
  //styleName: CN/Body/Regular;
  color: #626262;
  font-weight: 400 !important;
  font-size: 14px !important;
  .left-side {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    max-width: calc(100% - 120px);
    .groupItem {
      height: 24px;
      padding: 0 12px;
      border: 1px solid @fill-cursor;
      border-radius: 2px;
      svg {
        position: relative;
        top: 2px;
        margin-left: 8px;
      }
    }
    .barnchTag {
      color: @text-choosed;
      background: #e4edf8;
      border: 1px solid #ccdaf1;
      border-radius: 2px;
    }
  }
  .right-side {
    width: max-content;
    min-width: 120px;
    margin: auto 0;
    text-align: right;
    :global {
      .ant-btn {
        padding: 4px 5px;
      }
    }
  }
}
