import type {
  ProjectCompound,
  ProjectRouteStatus,
  RetroBackbone
} from '@/services/brain'
import type { Dayjs } from 'dayjs'
import type { SyntheticTree } from '../../../types/SyntheticRoute/SyntheticTree'
export type CardAction =
  | 'apply'
  | 'view'
  | 'edit'
  | 'collect'
  | 'setAsDefault'
  | 'copyRoute'
  | 'materialDosage'
  | 'experimentDesign'
  | 'feedback'

export interface CardTitleProps {
  updatedAt?: Dayjs
  isCollected?: boolean
  routeNo?: number
  routeName?: string
  route?: SyntheticTree
  item?: any
  updateTime?: Dayjs
  stepNum?: number
  hasBranch?: boolean
  hasBranches?: boolean
  actions: CardAction[]
  commendCount?: number
  onAction?: (action: CardAction) => void
  routeStatus?: ProjectRouteStatus
  group?: number
  handleFilterSimilar?: () => void
  targetMolecule?: ProjectCompound
}

export interface RetroRouteTitleProps {
  route: RetroBackbone
  moleculeId: number
  isPlayground?: boolean
}
