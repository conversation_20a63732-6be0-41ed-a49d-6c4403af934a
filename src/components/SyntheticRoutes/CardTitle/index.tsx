import { ReactComponent as CollectedIcon } from '@/assets/svgs/collected.svg'
import { ReactComponent as GroupIcon } from '@/assets/svgs/group.svg'
import { ReactComponent as HelpIcon } from '@/assets/svgs/help.svg'
import { ReactComponent as TipIcon } from '@/assets/svgs/route-operation/tip.svg'
import StatusRender from '@/components/StatusRender'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import EnumSwitcher from '@/pages/projects/components/EnumSwitcher'
import {
  ProjectCompound,
  ProjectRoute,
  ProjectRouteStatus,
  service
} from '@/services/brain'
import { CollectedRetroBackbones } from '@/services/brain/types/retro-backbones'
import {
  calHasBranch,
  calSyntheticStep
} from '@/types/SyntheticRoute/SyntheticTree'
import { IPagination } from '@/types/common'
import {
  detectDeviceInfo,
  formatYMDHMTime,
  formatYTSTime,
  getLocalValue,
  getWord,
  isEN,
  roundToOneDecimalPlaces,
  setLocalValue,
  toInt
} from '@/utils'
import {
  CheckOutlined,
  CopyOutlined,
  EditOutlined,
  ExperimentFilled,
  ExperimentOutlined,
  EyeOutlined,
  FieldTimeOutlined,
  FundOutlined,
  GoldOutlined,
  MessageOutlined,
  StarOutlined
} from '@ant-design/icons'
import { Button, Divider, Popover, Tag, message } from 'antd'
import cs from 'classnames'
import { cloneDeep, isNil } from 'lodash'
import { FC, ReactElement } from 'react'
import {
  history,
  useAccess,
  useLocation,
  useModel,
  useParams,
  useSearchParams
} from 'umi'
import type { CardAction, CardTitleProps } from './index.d'
import styles from './index.less'

const { winWidth } = detectDeviceInfo()
function OperateIcon({ icon, title }: { icon: ReactElement; title: string }) {
  return winWidth <= 1680 ? (
    <Popover content={title}>{icon}</Popover>
  ) : (
    <>
      {icon} {title}
    </>
  )
}

const projectStatusTransferMap: Record<
  ProjectRouteStatus,
  ProjectRouteStatus[]
> = {
  canceled: [],
  confirmed: ['canceled'],
  editing: ['canceled'],
  finished: []
}

const CardTitle: FC<CardTitleProps> = ({
  item,
  route,
  actions,
  stepNum = route ? calSyntheticStep(route) : 0,
  hasBranch = route ? calHasBranch(route) : false,
  hasBranches,
  updateTime,
  commendCount,
  routeName,
  onAction,
  updatedAt,
  routeNo,
  group,
  routeStatus,
  handleFilterSimilar,
  targetMolecule,
  isCollected
}) => {
  const [searchParams, setSearchParams] = useSearchParams()
  const { fetch } = useBrainFetch()
  const {
    updatePagenate,
    getMyRoutesData,
    routeType,
    curFilterInfo,
    collectedEvent,
    curIsCollected,
    curHistoryInfo
  } = useModel('compound')
  const access = useAccess()
  const { initialState } = useModel('@@initialState')
  const { id: projectId, compoundId } = useParams<{
    id: string
    compoundId: string
  }>()

  const isTemporaryRoute = curHistoryInfo?.status === 'running'

  const reload = async () => {
    let customPageSize = toInt(searchParams.get('pageSize') || '') || 10
    updatePagenate({
      page: 1,
      pageSize: customPageSize
    } as IPagination)
    await getMyRoutesData(toInt(compoundId) as number)
    setSearchParams(
      { page: '1', pageSize: customPageSize.toString() },
      { replace: true }
    )
  }

  const handleSetAsDefault = async () => {
    if (!compoundId || !routeNo) return
    const { data } = await fetch(
      service<ProjectCompound>('project-compounds').update(compoundId, {
        default_route: routeNo as unknown as ProjectRoute
      })
    )
    if (data) {
      message.success(getWord('default-route-set'))
      reload()
    }
  }

  const handleCollected = async () => {
    if (isTemporaryRoute) return
    const isCollected = curIsCollected(item)
    const { data } = isCollected
      ? await service('collected-retro-backbones').deleteOne(
          item?.collected_retro_backbones?.[0]?.id
        )
      : await service('collected-retro-backbones').create({
          user_id: String(initialState?.userInfo?.id),
          retro_backbone: routeNo
        })
    if (data as CollectedRetroBackbones) {
      let newCollectedRetroBackbones: CollectedRetroBackbones[] = cloneDeep(
        item?.collected_retro_backbones
      )
      if (isCollected) {
        newCollectedRetroBackbones = []
      } else {
        newCollectedRetroBackbones.unshift({
          id: data?.id,
          user_id: data?.user_id
        })
      }
      collectedEvent(routeNo, newCollectedRetroBackbones)
      message.success(getWord('operate-success'))
    }
  }

  const handleChangeStatus = async (status: ProjectRouteStatus) => {
    if (!routeNo) return
    const { data } = await fetch(
      service<ProjectRoute>('project-routes').update(routeNo, { status })
    )
    if (data) {
      message.success(getWord('success-update-status'))
      reload()
    }
  }

  const { pathname } = useLocation()
  const isPlayground: boolean = pathname.includes('/playground')
  const preViewdRoutes = getLocalValue('viewdRoute')
    ? JSON.parse(getLocalValue('viewdRoute'))
    : []
  const handleView = () => {
    let newViewdRoutes: string[] = [...preViewdRoutes, routeNo]
    setLocalValue('viewdRoute', JSON.stringify(newViewdRoutes))
    if (routeType === 'aiGenerated') {
      history.push(
        isPlayground
          ? `/playground/commend/view-by-backbone/${routeNo}`
          : `/projects/${projectId}/compound/${compoundId}/view-by-backbone/${routeNo}`
      )
    } else {
      history.push(
        `/projects/${projectId}/compound/${compoundId}/${
          routeStatus === 'editing' ? 'edit' : 'view'
        }/${routeNo}`
      )
    }
  }

  const hasCommendCount = commendCount && Number(commendCount) > 0
  const ActionCom = ({ action }: { action: CardAction }) => {
    const des: string = hasCommendCount
      ? `${getWord('comment')}（${commendCount}）`
      : getWord('comment')
    switch (action) {
      case 'feedback':
        if (!access?.authCodeList?.includes('compound.button.feedback'))
          return null
        return winWidth <= 1680 ? (
          <Popover content={des}>
            <MessageOutlined />
            {hasCommendCount ? `（${commendCount}）` : ''}
          </Popover>
        ) : (
          <>
            <MessageOutlined /> {des}
          </>
        )
      case 'collect':
        if (!access?.authCodeList?.includes('compound.button.collect'))
          return null
        return (
          <div onClick={handleCollected}>
            {isCollected ? (
              <OperateIcon
                icon={
                  <CollectedIcon
                    width={15}
                    style={{ position: 'relative', top: '2px' }}
                  />
                }
                title={getWord('unfavorite')}
              />
            ) : isTemporaryRoute ? (
              <Popover content={getWord('temporary-route-tip')}>
                <StarOutlined
                  style={{
                    color: 'rgba(85, 85, 85, 1)'
                  }}
                />
              </Popover>
            ) : (
              <OperateIcon
                icon={<StarOutlined />}
                title={getWord('favorite')}
              />
            )}
          </div>
        )
      case 'view':
        if (!access?.authCodeList?.includes('compound.button.view')) return null
        return (
          <div
            className={cs({
              complete: preViewdRoutes.includes(routeNo)
            })}
            onClick={handleView}
          >
            <OperateIcon
              icon={<EyeOutlined />}
              title={getWord('pages.projectTable.actionLabel.viewDetail')}
            />
          </div>
        )
      case 'setAsDefault':
        return (
          <div onClick={handleSetAsDefault}>
            {routeType === 'myRoutes' &&
            routeNo === targetMolecule?.default_route?.id ? (
              <OperateIcon
                icon={<ExperimentFilled />}
                title={getWord('default-route')}
              />
            ) : (
              <OperateIcon
                icon={<ExperimentOutlined />}
                title={getWord('set-default-route')}
              />
            )}
          </div>
        )
      case 'experimentDesign':
        return (
          <>
            <FundOutlined />
            {getWord('experiment-design')}
          </>
        )
      case 'materialDosage':
        return (
          <>
            <GoldOutlined />
            {getWord('material-demand')}
          </>
        )
      case 'copyRoute':
        return (
          <>
            <CopyOutlined />
            {getWord('copy-route')}
          </>
        )
      case 'edit':
        return (
          <>
            <EditOutlined />
            {getWord('edit')}
          </>
        )
      case 'apply':
        return (
          <>
            <CheckOutlined />
            {getWord('apply')}
          </>
        )
      default:
        return null
    }
  }

  const avaliableStatus = routeStatus
    ? projectStatusTransferMap[routeStatus]
    : []

  return (
    <div className={styles['card-title']}>
      <div className={styles['left-side']}>
        {!isNil(group) && curFilterInfo?.group !== 'ungrouped' && (
          <div
            className={cs(
              'enablePointer flex-justify-space-between flex-align-items-center',
              styles.groupItem
            )}
            onClick={handleFilterSimilar}
          >
            <Popover content={getWord('filter-routes-group')}>
              {getWord('new-group')} {group + 1}
              <GroupIcon width={14} fill="#336CC9" />
            </Popover>
          </div>
        )}
        <div>
          &nbsp;&nbsp;
          {getWord('route-id')}: {routeNo}
        </div>
        {routeType !== 'aiGenerated' && routeName ? (
          <>
            &nbsp;&nbsp;
            <Tag color="#2db7f5">{routeName}</Tag>
          </>
        ) : (
          ''
        )}
        {!isPlayground && item && !isNil(item.min_n_main_tree_steps) ? (
          <>
            &nbsp;&bull;&nbsp;
            <Popover content={getWord('multi-steps-tip')}>
              <HelpIcon width={18} style={{ cursor: 'pointer' }} />
            </Popover>
            <div>
              {getWord('route-length')}:{item.min_n_main_tree_steps}
            </div>
            &nbsp;&nbsp;
          </>
        ) : (
          ''
        )}
        <div>
          {getWord('longest-chain-l')}
          &nbsp;
          {stepNum}
        </div>
        &nbsp;&nbsp;
        <Tag className={styles.barnchTag}>
          {hasBranch ? getWord('has') : getWord('No')}
          {isEN() ? ' ' : ''}
          {hasBranch && hasBranches ? getWord('multiple') : ''}
          {isEN() ? ' ' : ''}
          {getWord('branched-chain')}
        </Tag>
        {updateTime && (
          <div>
            &nbsp;&bull;&nbsp;
            <FieldTimeOutlined />
            {getWord('modified-time')}: {formatYTSTime(updateTime)}
          </div>
        )}
        {!isPlayground && item && !isNil(item.score) ? (
          <>
            &nbsp;&bull;&nbsp;
            <div className={'flex-align-items-center'}>
              {getWord('algorithmic-score')}
              &nbsp;
              {Math.round(item.score * 100) / 100}
              {curHistoryInfo?.status === 'completed' && (
                <>
                  &nbsp;
                  <Popover
                    content={
                      <div key="algorithmic-tip">
                        <p>
                          {getWord('route-novelty-score')}：
                          {roundToOneDecimalPlaces(item?.novelty_score * 100)}
                          /100
                        </p>
                        <p>
                          {getWord('route-price-score')}：
                          {roundToOneDecimalPlaces(item?.price_score * 100)}
                          /100
                        </p>
                        <p>
                          {getWord('route-safety-score')}：
                          {roundToOneDecimalPlaces(item?.safety_score * 100)}
                          /100
                        </p>
                        <Divider style={{ margin: '8px 0' }} />
                        <p>
                          {getWord('route-base-score')}：
                          {roundToOneDecimalPlaces(item?.originScore)}
                          /100
                        </p>
                      </div>
                    }
                  >
                    <TipIcon
                      className="enablePointer"
                      fill="#929292"
                      width={14}
                    />
                  </Popover>
                </>
              )}
            </div>
          </>
        ) : (
          ''
        )}
        {!isPlayground && item && !isNil(item.known_reaction_rate) ? (
          <>
            &nbsp;&nbsp;
            <div>
              {getWord('known-reaction-proportion')}
              &nbsp;
              {item.known_reaction_rate}%
            </div>
          </>
        ) : (
          ''
        )}
        {isPlayground && hasCommendCount && updatedAt ? (
          <>
            &nbsp;&bull;&nbsp;
            <div>
              {getWord('last-comment-date')}: {formatYMDHMTime(updatedAt)}
            </div>
          </>
        ) : (
          ''
        )}
      </div>
      <div className={styles['right-side']}>
        {routeType === 'myRoutes' && routeStatus && (
          <>
            {avaliableStatus.length ? (
              <EnumSwitcher
                currentValue={routeStatus}
                avalibleValues={avaliableStatus}
                onSelect={handleChangeStatus}
                valueRender={(s) => (
                  <StatusRender
                    labelPrefix="pages.route.statusLabel"
                    status={s}
                  />
                )}
              />
            ) : (
              <StatusRender
                labelPrefix="pages.route.statusLabel"
                status={routeStatus}
              />
            )}
          </>
        )}
        {actions?.map((action: CardAction) => (
          <Button
            type="link"
            key={action}
            onClick={() => {
              onAction?.(action)
            }}
            style={{
              cursor:
                action === 'collect' && isTemporaryRoute
                  ? 'not-allowed'
                  : 'pointer'
            }}
          >
            <ActionCom action={action} />
          </Button>
        ))}
      </div>
    </div>
  )
}

export default CardTitle
