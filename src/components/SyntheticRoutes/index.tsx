import GeneratingImg from '@/assets/svgs/lottie/generating.gif'
import { ReactComponent as GeneratingRouteTipIcon } from '@/assets/svgs/systems/generatingRouteTip.svg'
import { ReactComponent as QueuingIcon } from '@/assets/svgs/systems/queuing.svg'
import { ReactComponent as SearchFailedIcon } from '@/assets/svgs/systems/searchFailed.svg'
import { SearchStatus, query, type RetroProcess } from '@/services/brain'
import { RouteType, routeTypes } from '@/types/common'
import { getEnvConfig, getWord, isValidArray } from '@/utils'
import { useUpdateEffect } from 'ahooks'
import { Pagination } from 'antd'
import { isEmpty } from 'lodash'
import React, { memo, useEffect, useRef } from 'react'
import { io } from 'socket.io-client'
import { history, useAccess, useModel, useParams, useSearchParams } from 'umi'
import { toInt } from '../../utils/common'
import StatusTip from '../StatusTip'
import GeneratedResultCard from './GeneratedResultCard'
import ReactionItem from './ReactionItem'
import SyntheticRouteCard from './SyntheticRouteCard'
import type { SyntheticRoutesProps } from './index.d'
import styles from './index.less'
const SyntheticRoutes: React.FC<SyntheticRoutesProps> = ({
  isPlayground,
  moleculeId,
  openAiGenerateModel
}) => {
  const { getCommonExpression, reload, finishedReload, startReload } =
    useModel('commend')
  const ref = useRef<HTMLDivElement | null>(null)
  const [searchParams, setSearchParams] = useSearchParams()
  const { id: projectId, compoundId } = useParams<{
    id: string
    compoundId: string
  }>()

  const {
    loading,
    totalCount,
    myRoutesTotal,
    routes,
    myRoutesData,
    getAIListData,
    cacheFilterInfo,
    getMyRoutesData,
    getPlaygroundData,
    updateRouteType,
    pagenate,
    updatePagenate,
    curFilterInfo,
    retroParamsConfig,
    myRoutesLoading,
    curHistoryInfo,
    filteredSearchHistory,
    targetMolecule,
    getSearchHistory,
    updateBacoboneLengthRange,
    updateMainTreeStepsRange,
    cacheRetroProcessUpdates,
    routeType
  } = useModel('compound')
  const isMyRoutesTab: boolean = routeType === 'myRoutes'

  const access = useAccess()
  const getList = async () => {
    if (isPlayground)
      return getPlaygroundData({ pagenate, routeType: routeType })
    if (isMyRoutesTab) {
      if (myRoutesLoading) return
      if (!access?.authCodeList?.includes('compound.tab.myRoutes')) return []
      await getMyRoutesData(toInt(compoundId) as number)
    } else {
      if (loading) return
      if (!access?.authCodeList?.includes('compound.tab.aiGenerated')) return []
      await getAIListData()
    }
    if (ref.current?.parentElement) {
      ref.current?.parentElement.scrollTo({ top: 0 })
    }
  }

  useEffect(() => {
    const curType = searchParams.get('tab') as RouteType
    if (routeTypes.includes(curType)) {
      updateRouteType(curType)
    } else {
      updateRouteType('aiGenerated')
    }
    return () => {
      updateBacoboneLengthRange([])
      updateMainTreeStepsRange([])
      updateRouteType(null)
    }
  }, [])

  const getBacoboneLengthRange = async () => {
    const { data } = await query(
      `retro-backbones/length-range/${curHistoryInfo?.id}`
    ).get()
    updateBacoboneLengthRange(data as number[])
  }

  const getMainTreeStepsRange = async () => {
    const { data } = await query(
      `retro-backbones/main-tree-steps-range/${curHistoryInfo?.id}`
    ).get()
    updateMainTreeStepsRange(data as number[])
  }

  useEffect(() => {
    if (curHistoryInfo?.id) {
      getBacoboneLengthRange()
      getMainTreeStepsRange()
    }
  }, [curHistoryInfo?.id])

  const { searchHistoryData } = useModel('compound')
  useEffect(() => {
    if (!compoundId) return
    const socket = io(`${getEnvConfig().apiBase}`, {
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000
    })

    socket.on('connect', () => {
      socket.emit('retro-process:join', compoundId)
    })

    socket.on('retro-process:update', (updates: RetroProcess[]) => {
      console.log('---updates01---', updates)
      if (updates[0].compound_id === moleculeId) {
        cacheRetroProcessUpdates(updates)
      }
      let hasUpdate = false,
        hasCanceled = false
      const existSearchHistory = updates.some((u) =>
        searchHistoryData.some((s) => s.retro_id === u.retro_id)
      )
      for (const iterator of updates) {
        if (iterator.status === 'canceled') hasCanceled = true
        else if (iterator.status !== 'running') {
          hasUpdate = true
          if (iterator.status === 'completed') {
            cacheRetroProcessUpdates([
              {
                retro_id: iterator?.retro_id,
                status: 'completed'
              }
            ])
          }
        }
      }
      console.log(
        '---hasUpdate---',
        hasUpdate,
        existSearchHistory,
        hasCanceled,
        moleculeId
      )
      if ((hasUpdate && existSearchHistory) || hasCanceled) {
        /* TODO 状态描述待产品确认
        if (hasUpdate) {
          message.success({
            description: getWord('search-done')
          })
        } */
        getSearchHistory(moleculeId as string)
        startReload()
      }
    })
  }, [compoundId])

  useEffect(() => {
    if (isMyRoutesTab) {
      cacheFilterInfo({
        pagenate,
        moleculeId: moleculeId,
        routeType
      })
    } else {
      cacheFilterInfo({
        pagenate,
        ...curFilterInfo
      })
    }
  }, [routeType])

  useEffect(() => {
    getList()
  }, [pagenate, curFilterInfo, retroParamsConfig, isPlayground])

  useUpdateEffect(() => {
    if (reload) {
      getList()
      finishedReload()
    }
  }, [reload])

  useEffect(() => {
    searchParams.set('page', pagenate.page.toString())
    searchParams.set('pageSize', pagenate.pageSize.toString())
    setSearchParams(searchParams, { replace: true })
  }, [pagenate])

  useUpdateEffect(() => {
    if (curHistoryInfo?.id) {
      updatePagenate({ page: 1, pageSize: pagenate?.pageSize })
    }
  }, [curHistoryInfo?.id])

  useEffect(() => {
    if (routeType) {
      searchParams.set('tab', routeType)
      setSearchParams(searchParams, { replace: true })
    }
    getCommonExpression(
      routeType === 'aiGenerated' ? 'retro-backbone' : 'project-route'
    )
  }, [routeType])

  const isInconformity =
    curHistoryInfo?.id || isValidArray(filteredSearchHistory)
  const isQueuing: boolean = ['pending', 'limited'].includes(
    curHistoryInfo.status
  )
  const isFailed: boolean = curHistoryInfo.status === 'failed'
  const isGenerating = curHistoryInfo?.status === 'running'

  const getImage = () => {
    if (isQueuing) {
      return <QueuingIcon />
    } else if (isFailed) {
      return <SearchFailedIcon />
    } else if (isGenerating) {
      return <img className={styles.generatingGif} src={GeneratingImg} />
    } else if (isInconformity) {
      return null
    } else return <GeneratingRouteTipIcon />
  }

  const EmptyTip = () => {
    const curIsEmpty: boolean =
      isMyRoutesTab && !isPlayground ? isEmpty(myRoutesData) : isEmpty(routes)
    if (isMyRoutesTab) {
      return (
        <span>
          {getWord('no-routes-create-tip')}
          <a
            onClick={() =>
              history.push(
                `/projects/${projectId}/compound/${moleculeId}/create`
              )
            }
          >
            【{getWord('new-route')}】
          </a>
          {getWord('add-routes')}
        </span>
      )
    } else {
      if (isQueuing) return <>{getWord('wait-tip')}</>
      else if (isFailed) return <>{getWord('search-failed')}</>
      else if (isGenerating || loading || myRoutesLoading)
        return <>{getWord('generating-tip')}</>
      else if (isInconformity || curIsEmpty)
        return <>{getWord('no-routes-returned')}</>
      else {
        return (
          <span>
            {targetMolecule?.status === 'finished' ? (
              getWord('no-AI-returned')
            ) : (
              <>
                {getWord('no-AI-returned-create-I')}
                <a onClick={openAiGenerateModel}>
                  【{getWord('gnerate-routes')}】
                </a>
                {getWord('no-AI-returned-create-II')}
              </>
            )}
          </span>
        )
      }
    }
  }

  const MoleculeList = memo(({ curRoutes }: any) => {
    console.log('---curRoutes---', curRoutes)
    return (
      <>
        {curRoutes.map((route) => {
          if ('backbone' in route && routeType === 'aiGenerated') {
            return (
              <GeneratedResultCard
                key={route.id}
                result={route}
                routeType={routeType}
                isPlayground={isPlayground}
              />
            )
          } else if (isPlayground && routeType === 'reaction') {
            return <ReactionItem item={route} key={`reaction-${route.id}`} />
          } else {
            return (
              <SyntheticRouteCard
                key={route.id}
                route={route}
                isPlayground={isPlayground}
                targetMolecule={targetMolecule}
              />
            )
          }
        })}
        {totalCount > 10 ? (
          <Pagination
            className="pagination"
            total={isMyRoutesTab ? myRoutesTotal : totalCount}
            current={pagenate.page}
            pageSize={pagenate.pageSize}
            showSizeChanger={false}
            onChange={(page, pageSize) => updatePagenate({ page, pageSize })}
          />
        ) : (
          ''
        )}
      </>
    )
  })

  const CurStatus = memo(
    ({ status, id }: { status: SearchStatus; id: number }) => {
      return (
        <div key={`${id}-${status}`}>
          <StatusTip
            image={getImage()}
            wrapperClassName={styles.routesContent}
            des={<EmptyTip />}
          />
        </div>
      )
    }
  )

  const RouteList = () => {
    if (isMyRoutesTab && !isPlayground && !isEmpty(myRoutesData)) {
      return <MoleculeList curRoutes={myRoutesData} />
    } else if (!isMyRoutesTab && !isEmpty(routes)) {
      return <MoleculeList curRoutes={routes} />
    } else {
      return (
        <CurStatus status={curHistoryInfo?.status} id={curHistoryInfo?.id} />
      )
    }
  }

  return (
    <div ref={ref}>
      <RouteList />
    </div>
  )
}

export default SyntheticRoutes
