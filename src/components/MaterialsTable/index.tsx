import SmilesInput from '@/components/SmilesInput'
import { reactionUnitOptions } from '@/constants'
import useOptions from '@/hooks/useOptions'
import { isReadonlyMaterialRole } from '@/pages/route/util'
import type { MaterialTable } from '@/services/brain'
import type { IOption } from '@/types/common'
import { getWord } from '@/utils'
import {
  ProForm,
  ProFormDigit,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormText
} from '@ant-design/pro-components'
import { Form } from 'antd'
import { useEffect, useState } from 'react'
import type { MaterialTableProps } from './index.d'
import styles from './index.less'

export default function MaterialsTable(props: MaterialTableProps) {
  const [form] = Form.useForm<MaterialTable>()
  const { reactionRoleOptions } = useOptions()
  const [dataSource, setDataSource] = useState<MaterialTable[]>([])
  useEffect(() => {
    const finalMaterialData = props?.material_table?.filter(
      (e) => e?.role !== 'product'
    ) as MaterialTable[]
    setDataSource(finalMaterialData)
  }, [props?.material_table])

  useEffect(() => {
    if (dataSource) {
      form.setFieldsValue({ material_table: dataSource })
    }
  }, [dataSource])

  const isConclusion: boolean = location.pathname.includes(
    'experimental-procedure/conclusion'
  )

  const handleOption = (options: IOption[]) =>
    options.map((e) => ({
      ...e,
      disabled: isReadonlyMaterialRole(e?.value as string)
    }))

  return (
    <ProForm
      submitter={
        props?.enableAdd
          ? {
              onSubmit: async () => {
                const values = await form.validateFields()
                props?.updateMaterial(values?.material_table)
              },
              resetButtonProps: {
                style: {
                  display: 'none'
                }
              }
            }
          : false
      }
      form={form}
    >
      <ProFormList
        name="material_table"
        label={getWord('material-sheet')}
        deleteIconProps={props?.enableAdd as boolean}
        creatorButtonProps={
          props?.enableAdd
            ? {
                creatorButtonText: getWord('add-raw-materials')
              }
            : false
        }
        copyIconProps={false}
        actionRender={(field, _, defaultActionDom) => {
          const item = form.getFieldValue('material_table')[
            field.name
          ] as MaterialTable
          return isReadonlyMaterialRole(item.role as string)
            ? []
            : defaultActionDom
        }}
      >
        {(field) => {
          const item = form.getFieldValue('material_table')[
            field.name
          ] as MaterialTable
          const disabledEditReactant = isReadonlyMaterialRole(item.role)
          const disabledAdd = !props?.enableAdd
          return (
            <ProFormGroup key="group">
              <ProFormSelect
                disabled={disabledAdd || disabledEditReactant}
                name="role"
                label={getWord('role')}
                width={130}
                options={handleOption(
                  reactionRoleOptions.filter((r) => r.value !== 'product')
                )}
                required
                rules={[{ required: true }]}
              />
              <ProFormText
                name="no"
                width={90}
                label={getWord('material-ID')}
                disabled={disabledAdd || disabledEditReactant}
              />
              <ProFormText
                name="name"
                label={getWord('substance-name')}
                width={140}
                disabled={disabledAdd}
              />
              <ProForm.Item
                className={styles['filter-form-root']}
                name="smiles"
                label={getWord('structural')}
                required
                rules={[{ required: true }]}
              >
                <SmilesInput
                  disabled={disabledAdd || disabledEditReactant}
                  multiple={false}
                />
              </ProForm.Item>
              <ProFormDigit
                name="equivalent"
                width={185}
                label={getWord('EWR')}
                disabled={disabledAdd}
                required
                rules={[
                  { required: true },
                  {
                    pattern: /^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,
                    message: getWord('enter-two-decimal')
                  }
                ]}
              />
              {/*  NOTE 1、实验结论 需要 实际投料量 2、实验设计 不需要 实际投料量 */}
              {isConclusion ? (
                <>
                  <ProFormDigit
                    name="value"
                    label={getWord('expected-mass')}
                    required
                    disabled={true}
                    rules={[
                      { required: true },
                      {
                        pattern: /^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,
                        message: getWord('enter-two-decimal')
                      }
                    ]}
                  />
                  <ProFormDigit
                    name="real_value"
                    label={getWord('actual-mass')}
                    required
                    disabled={true}
                    rules={[
                      { required: true },
                      {
                        pattern: /^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,
                        message: getWord('enter-two-decimal')
                      }
                    ]}
                  />
                </>
              ) : (
                ''
              )}
              <ProFormSelect
                name="unit"
                label={getWord('unit')}
                width={130}
                disabled={isConclusion || disabledAdd}
                options={reactionUnitOptions}
                required
                rules={[{ required: true }]}
              />
            </ProFormGroup>
          )
        }}
      </ProFormList>
    </ProForm>
  )
}
