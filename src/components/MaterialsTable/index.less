.filter-form-root {
  .expand-btn-wrapper {
    margin-bottom: 12px;
  }
  .confirm-col {
    :global {
      .ant-form-item {
        margin-bottom: 4px;
      }
      .ant-row.ant-form-item-row {
        flex-direction: row;
        .ant-col.ant-form-item-label {
          display: flex;
          padding: 0;
          padding-right: 16px;
        }
        .ant-col.ant-form-item-control {
          width: auto;
        }
      }
    }
  }

  :global {
    .smiles-list {
      .ant-upload-list-picture-card-container,
      .ant-upload-select-picture-card {
        width: 60px;
        height: 60px;
      }
      &.reaction-list .ant-upload-list-picture-card-container {
        width: fit-content;
      }
      .ant-upload-list-picture-card
        .ant-upload-list-item-file
        + .ant-upload-list-item-name {
        display: none !important;
      }
      .add-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }
      &.hide-upload-btn {
        .ant-upload {
          display: none;
        }
      }
    }
  }

  .re-retro-btn {
    margin-left: auto;
  }
}
