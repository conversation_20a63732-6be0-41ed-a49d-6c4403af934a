import { Button } from 'antd'
import cs from 'classnames'

import styles from './index.less'

interface CustomButtonProps {
  clickEvent?: () => void
  buttonDes?: string
  wrapClassName?: any
}
export default function CustomButton(props: CustomButtonProps) {
  return (
    <Button
      className={cs(styles.retryButton, props?.wrapClassName)}
      shape="round"
      size="large"
      onClick={props?.clickEvent}
    >
      {props?.buttonDes}
    </Button>
  )
}
