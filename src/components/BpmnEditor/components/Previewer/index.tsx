import { message, Modal } from 'antd'
import { useState } from 'react'
// 引入代码高亮插件和样式
import { xml2json } from '@/components/BpmnEditor/bpmn/util/xmlUtil'
import SyntaxHighlighter from 'react-syntax-highlighter'
import { obsidian } from 'react-syntax-highlighter/dist/esm/styles/hljs'
import type { IProps } from './index.d'
import styles from './index.less'

/**
 * 流程预览
 */
export default function Previewer(props: IProps) {
  const { modeler, type } = props
  const [xml, setXml] = useState<string>('')
  const [open, setOpen] = useState(false)

  const showModal = async () => {
    setOpen(true)
    let result = await modeler.saveXML({ format: true })
    const { xml } = result
    if (type == 'xml') {
      /* TODO xml格式化-方案二，在源头修改xml数据返回给服务端进行zb部署
      let _xml: string = cloneDeep(xml)
      _xml = _xml.replace(/<csutom:xxx/g, '<callActivity')
      _xml = _xml.replace(/csutom:xxx>/g, 'callActivity>')
      console.log('---new xml---', _xml) */
      setXml(xml)
    } else {
      const jsonStr: string = xml2json(xml)
      setXml(jsonStr)
    }
  }

  const handleCancel = () => setOpen(false)

  const handleCopy = () => {
    if (navigator) {
      navigator.clipboard
        .writeText(xml)
        .then(() => message.info('已复制到剪贴板'))
    }
  }

  return (
    <>
      <a type="primary" onClick={showModal}>
        {'预览' + type.toUpperCase()}
      </a>
      <Modal
        width={1200}
        bodyStyle={{ maxHeight: '50%' }}
        title="预览"
        open={open}
        okText={'复制'}
        cancelText={'关闭'}
        onOk={handleCopy}
        onCancel={handleCancel}
      >
        <div className={styles.codePreWrap}>
          <SyntaxHighlighter language={type} style={obsidian}>
            {xml}
          </SyntaxHighlighter>
        </div>
      </Modal>
    </>
  )
}
