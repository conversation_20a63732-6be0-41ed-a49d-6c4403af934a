import type { ExtraToolsProps } from '@/components/BpmnEditor/index.d'
import type { MenuProps } from 'antd'
import { Dropdown, Popover } from 'antd'
import cs from 'classnames'
import { isEmpty } from 'lodash'
import { memo, useMemo, useState } from 'react'
import { ReactComponent as DownloadIcon } from '../../assets/icons/download.svg'
import { ReactComponent as PreviewPictureIcon } from '../../assets/icons/previewPicture.svg'
import Previewer from '../Previewer'
import type { ButtonItemProps, EditingToolsProps } from './index.d'
import styles from './index.less'
export default function EditingTools(props: EditingToolsProps) {
  const [file, setFile] = useState<HTMLInputElement | null>(null)
  const {
    bpmnModeler,
    scale,
    onZoomIn,
    onZoomOut,
    onZoomReset,
    onRestart,
    onOpenFIle,
    onUndo,
    onRedo,
    onDownloadXml,
    onDownloadBpmn,
    onDownloadSvg,
    onPreview
  } = props

  const ButtonItem = (params: ButtonItemProps) => {
    return (
      <Popover content={params?.des}>
        <button type="button" onClick={params?.cb}>
          {params?.iconType ? (
            <i className={styles[params?.iconType]} />
          ) : (
            <img src={params?.img} className={styles.imgSvg} />
          )}
        </button>
      </Popover>
    )
  }

  // 预览菜单
  const previewItems: MenuProps['items'] = [
    {
      label: <a onClick={onPreview}>{'预览图片'}</a>,
      key: 'previewImage'
    },
    {
      label: <Previewer modeler={bpmnModeler} type="xml" />,
      key: 'previewXML'
    },
    {
      label: <Previewer modeler={bpmnModeler} type={'json'} />,
      key: 'previewJSON'
    }
  ]

  const downloadItems: MenuProps['items'] = [
    {
      label: <a onClick={onDownloadXml}>{'XML文件'}</a>,
      key: 'xml-file'
    },
    {
      label: <a onClick={onDownloadSvg}>{'SVG图像'}</a>,
      key: 'svg-img'
    },
    {
      label: <a onClick={onDownloadBpmn}>{'BPMN文件'}</a>,
      key: 'bpmn-file'
    }
  ]

  const curExtraTools = useMemo(() => {
    return props?.extraTools
  }, [props?.extraTools])

  const ExtraTools = memo(function ExtraItem({
    extraTools
  }: {
    extraTools: ExtraToolsProps[]
  }) {
    return extraTools.map((e: ExtraToolsProps) => {
      const { event } = e
      return (
        <li className={styles.control} key={e.title}>
          <ButtonItem des={e.title} cb={() => event()} img={e?.img} />
        </li>
      )
    })
  })
  const handleOpen = () => file.click()

  return (
    <div className={styles.editingTools}>
      <ul className={styles.controlList}>
        {!props?.readOnly && (
          <>
            <li className={cs(styles.control, styles.line)}>
              <input
                ref={(file: HTMLInputElement) => setFile(file)}
                className={styles.openFile}
                type="file"
                onChange={onOpenFIle}
              />
              <ButtonItem des="打开BPMN文件" cb={handleOpen} iconType="open" />
            </li>
            <li className={styles.control}>
              <ButtonItem des="撤销" cb={onUndo} iconType="undo" />
            </li>
            <li className={styles.control}>
              <ButtonItem des="恢复" cb={onRedo} iconType="redo" />
            </li>
            <li className={cs(styles.control, styles.line)}>
              <ButtonItem des="重新绘制" cb={onRestart} iconType="restart" />
            </li>
          </>
        )}
        <li className={styles.control}>
          <ButtonItem des="重置大小" cb={onZoomReset} iconType="zoom" />
        </li>
        <li className={cs(styles.control, styles.line)}>
          <ButtonItem des="缩小" cb={onZoomOut} iconType="zoomOut" />
        </li>
        <li className={styles.percent}>{`${Math.floor(scale * 10 * 10)}%`}</li>
        <li className={styles.control}>
          <ButtonItem des="放大" cb={onZoomIn} iconType="zoomIn" />
        </li>
        <Dropdown menu={{ items: previewItems }} trigger={['click']}>
          <li className={cs(styles.control, styles.download)}>
            <button type="button">
              <PreviewPictureIcon />
            </button>
          </li>
        </Dropdown>
        <Dropdown menu={{ items: downloadItems }} trigger={['click']}>
          <li className={cs(styles.control, styles.download)}>
            <button type="button">
              <DownloadIcon />
            </button>
          </li>
        </Dropdown>
        {!isEmpty(props?.extraTools) && (
          <ExtraTools extraTools={curExtraTools} />
        )}
      </ul>
    </div>
  )
}
