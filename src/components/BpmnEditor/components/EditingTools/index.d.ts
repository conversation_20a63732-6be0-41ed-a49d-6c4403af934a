import type { ExtraToolsProps } from '../../../../index.d'
export interface ButtonItemProps {
  des: string
  cb: () => void
  iconType?: string
  img: HTMLImageElement
}

export interface EditingToolsProps {
  bpmnModeler: any
  scale: number
  onOpenFIle: (e: any) => void
  onZoomIn: () => void
  onZoomOut: () => void
  onZoomReset: () => void
  onRestart: () => void
  onUndo: () => void
  onRedo: () => void
  onSave: () => void
  onDownloadXml: () => void
  onDownloadBpmn: () => void
  generateCPL: () => void
  handlePublish: () => void
  onDownloadSvg: () => void
  onPreview: () => void
  onPreviewXml: () => void
  readOnly: boolean
  extraTools?: ExtraToolsProps[]
}
