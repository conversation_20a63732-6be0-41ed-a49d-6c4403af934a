@import '@/style/variables.less';
.editingTools {
  position: absolute;
  top: 19px;
  right: calc(@editorPanelWidth + 27px);
  z-index: 999;

  .controlList {
    display: inline-block;
    margin-left: 10px;
    padding: 0;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0px 2px 12px 0px rgba(71, 80, 117, 0.1);
  }

  .controlList li:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }
  .controlList li:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .controlList .control:hover {
    background: #f3f3f3;
    cursor: pointer;
  }

  .percent {
    position: relative;
    bottom: 2px;
    width: 45px;
  }

  li {
    position: relative;
    display: inline-block;
    padding: 6px 8px;
    list-style-type: none;
  }

  .control button {
    padding: 0;
    color: #555;
    font-size: 22px;
    line-height: 26px;
    background: none;
    border: none;
    outline: none;
    cursor: pointer;
  }

  .control .openFile {
    display: none;
  }

  .control.line::after {
    position: absolute;
    top: 50%;
    right: 0;
    height: 16px;
    border-right: 1px solid #ddd;
    transform: translateY(-50%);
    content: '';
  }

  .control button i {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
  }

  .download {
    svg {
      position: relative;
      top: 2px;
    }
  }

  .zoom {
    background: url('../../assets/icons/zoom.svg') 0 0 no-repeat;
  }

  .zoomIn {
    background: url('../../assets/icons/zoomIn.svg') 0 0 no-repeat;
  }

  .zoomOut {
    background: url('../../assets/icons/zoomOut.svg') 0 0 no-repeat;
  }

  .undo {
    background: url('../../assets/icons/undo.svg') 0 0 no-repeat;
  }

  .redo {
    background: url('../../assets/icons/redo.svg') 0 0 no-repeat;
  }

  .image {
    background: url('../../assets/icons/image.svg') 0 0 no-repeat;
  }

  .open {
    background: url('../../assets/icons/open.svg') 0 0 no-repeat;
  }

  .preview {
    background: url('../../assets/icons/preview.svg') 0 0 no-repeat;
  }

  .previewPicture {
    top: 1px !important;
    background: url('../../assets/icons/previewPicture.svg') 0 0 no-repeat;
  }

  .restart {
    background: url('../../assets/icons/restart.svg') 0 0 no-repeat;
  }
}

.imgSvg {
  position: relative;
  width: 20px;
  height: 18px;
  line-height: 26px;
}
