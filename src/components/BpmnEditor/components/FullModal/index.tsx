import { Modal } from 'antd'
import type { FullModalProps } from './index.d'
import styles from './index.less'
export default function FullModal(props: FullModalProps) {
  const { visible, title = '', onCancel, children } = props
  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      width={'calc(100% - 20px)'}
      height={'calc(100% - 10px)'}
      footer={null}
      className={styles.fullModal}
    >
      {children}
    </Modal>
  )
}
