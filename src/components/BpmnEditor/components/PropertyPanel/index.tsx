import ElementBaseInfo from '@/components/BpmnEditor/bpmn/panel/ElementBaseInfo'
import ExtensionProperties from '@/components/BpmnEditor/bpmn/panel/ExtensionProperties'
import { initBpmnInstance } from '@/components/BpmnEditor/bpmn/util/windowUtil'
import {
  InfoCircleOutlined,
  NodeIndexOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { Button, Collapse, Form, Input, Space, Typography, message } from 'antd'
import { isEmpty, isNil } from 'lodash'
import { useEffect, useState } from 'react'
import { useModel, useSelector } from 'umi'
import { getTaskId } from '../../utils'

import { query } from '@/services/brain'
import { TaskInfo } from '@/types/models/task-info'
import {
  decodeUrl,
  formatYMDHMTime,
  isValidArray,
  toRobotDetail
} from '@/utils'
import ExceptionInfo from '../../bpmn/panel/ExceptionInfo'
import { contentTitleDes } from './enum'
import type { IOperation, PropertyPanelProps } from './index.d'

/**
 * 属性面板
 */
export default function PropertyPanel(props: PropertyPanelProps) {
  const { updateTaskInfo, taskInfo, getTaskInfo } = useModel('task')
  const { fetchExceptionData, exceptionData, updateExceptionData } =
    useModel('exception')
  const { modeler, experimentalNo } = props
  const [taskSatusInfo, setTaskSatusInfo] = useState<TaskInfo>()
  const [element, setElement] = useState<any>()
  const [businessObject, setBusinessObject] = useState<any>()
  const [modeling, setModeling] = useState<any>()
  const [bpmnFactory, setBpmnFactory] = useState<any>()
  const [moddle, setModdel] = useState<any>()
  const [rootElements, setRootElements] = useState([])

  const bpmnState = useSelector((state) => state?.bpmn)
  const processId = bpmnState?.processId

  const themeState = useSelector((state) => state?.theme)
  const colorPrimary = themeState?.colorPrimary

  /**
   * 获取task明细用于 Panel 展示
   */
  const fetchTaskDetail = async (taskId: string) => {
    updateTaskInfo([])
    if (experimentalNo) {
      await fetchExceptionData(
        taskId,
        JSON.parse(decodeUrl(experimentalNo as string))
      )
    }
    getTaskInfo(taskId)
  }

  /**
   * 和taskNo匹配后 用 operator: "robot-1" 获取robotId进行页面跳转
   */
  const getAllRobotList = async (robotName: string) => {
    const { data, error } = await query(`robots`)
      .filterDeep('name', 'eq', robotName)
      .get()
    if (error) return message.error(error?.message)
    if (!isValidArray(data)) return
    const robotId = data[0]?.id
    toRobotDetail(robotId)
  }

  useEffect(() => {
    let curTaskSatusInfo = props?.operationProcess?.find(
      (e) => e.taskNo === businessObject?.id
    ) as TaskInfo
    if (curTaskSatusInfo) setTaskSatusInfo(curTaskSatusInfo)
    if (businessObject?.taskType) {
      fetchTaskDetail(
        `${businessObject?.taskType}_${getTaskId(businessObject)}`
      ) // 创建时 值为 task_ 拼接 uuid
    } else if (businessObject?.id) {
      const targetKey = businessObject?.id?.split('_')[0]
      if (
        !isEmpty(props?.taskList) &&
        !isNil(targetKey) &&
        props.taskList.findIndex((e) => e?.key === targetKey) !== -1
      ) {
        fetchTaskDetail(businessObject?.id)
      }
    }
  }, [businessObject?.id])

  const panelWrapper = (contentType: IOperation) => {
    const renderContent = () => {
      /* 渲染 常规信息 组件 (所有节点都有) */
      return (
        <>
          <ElementBaseInfo
            businessObject={businessObject}
            panelDatas={props?.panelDatas}
          />
          <Form
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            key={`task-time`}
          >
            {taskSatusInfo?.startTime && (
              <Form.Item label="任务执行开始时间">
                <Input
                  disabled
                  value={formatYMDHMTime(taskSatusInfo?.startTime)}
                />
              </Form.Item>
            )}
            {taskSatusInfo?.endTime && (
              <Form.Item label="任务执行结束时间">
                <Input
                  disabled
                  value={formatYMDHMTime(taskSatusInfo?.endTime)}
                />
              </Form.Item>
            )}
          </Form>
          {taskSatusInfo?.operator ? (
            <div className="flex-justfy-content-end">
              <Button
                onClick={() => {
                  getAllRobotList(taskSatusInfo?.operator)
                }}
              >
                查看操作详情
              </Button>
            </div>
          ) : (
            ''
          )}
        </>
      )
    }
    return (
      <Collapse.Panel
        header={
          <Typography style={{ color: colorPrimary, fontWeight: 'bold' }}>
            <InfoCircleOutlined twoToneColor={colorPrimary} />
            &nbsp; {contentTitleDes[contentType]}
          </Typography>
        }
        key="baseInfo"
        showArrow={true}
        forceRender={false}
      >
        {renderContent()}
      </Collapse.Panel>
    )
  }

  /**
   * 渲染 扩展属性 组件 (所有节点都有)
   */
  function renderElementBaseInfo() {
    return (
      <Collapse.Panel
        header={
          <Typography style={{ color: colorPrimary, fontWeight: 'bold' }}>
            <WarningOutlined />
            &nbsp; {contentTitleDes['exceptionInfo']}
          </Typography>
        }
        key="exceptionInfo"
        showArrow={true}
        forceRender={false}
      >
        <ExceptionInfo />
      </Collapse.Panel>
    )
  }

  /**
   * 确认当前选中节点
   */
  function confirmCurrentElement(element: any) {
    // 如果element为空，则设置流程节点为当前节点，否则设置选中节点为当前节点 (点击canvas空白处默认指流程节点)
    if (!element) {
      // 查询流程节点的id,并通过id获取流程节点
      // console.log('--- modeler?.getDefinitions()---', modeler?.getDefinitions())
      const newId = modeler?.getDefinitions()?.rootElements
        ? modeler?.getDefinitions()?.rootElements[0]?.id
        : undefined
      let processElement: any = modeler.get('elementRegistry').get(newId)
      setElement(processElement)
      window.bpmnInstance.element = processElement
      setBusinessObject(
        JSON.parse(JSON.stringify(processElement?.businessObject || null))
      )
      return
    }
    /* dva 存储选中节点的类型 */
    window.bpmnInstance.element = element
    setBusinessObject(
      JSON.parse(
        JSON.stringify({
          ...element.businessObject,
          taskType: element?.type.split('custom:')[1]
        })
      )
    )
    setElement(element)
  }

  function init() {
    console.log('【初始化bpmn实例】2、初始化,设置实际值')
    // 设置window的bpmnInstance对象属性
    window.bpmnInstance.modeler = modeler
    window.bpmnInstance.elementRegistry = modeler.get('elementRegistry')
    window.bpmnInstance.modeling = modeler.get('modeling', true)
    window.bpmnInstance.bpmnFactory = modeler.get('bpmnFactory', true)
    window.bpmnInstance.moddle = modeler.get('moddle', true)
    console.log('【初始化bpmn实例】3、初始化完成')
    setModeling(modeler.get('modeling', true)) // 获取modeling
    setBpmnFactory(modeler.get('bpmnFactory', true)) // 获取bpmnFactory
    setModdel(modeler.get('moddle', true)) // 获取moddle
    confirmCurrentElement(null) //设置默认选中流程process节点
  }

  useEffect(() => {
    initBpmnInstance()
    // 避免初始化，流程图未加载完导致出错
    if (modeler) {
      init()
    }
    return () => {
      updateExceptionData([])
    }
  }, [processId])
  /**
   * 初始化时,设置监听器
   * 1.这部分不直接放到 init() 方法里,是为了防止在用户主动修改processId时,产生多个监听器
   * 2.特别注意: 监听器只能设置一次，如果执行多次，会设置多个监听器,浪费资源
   */
  useEffect(() => {
    // 避免初始化，流程图未加载完导致出错
    if (modeler) {
      // 设置监听器，监听所有工作就绪后，默认选中process节点 (TODO 2022/12/4 注意:没有找到关于 import.done 事件,目前这段代码是没有执行到的,先放这里吧)
      modeler?.on('import.done', () => {
        confirmCurrentElement(null)
        // 获取rootElements
        setRootElements(modeler.getDefinitions().rootElements)
        window.bpmnInstance.rootElements = modeler.getDefinitions().rootElements
      })
      // 设置监听器，监听选中节点变化 (监听器只能设置一次，如果执行多次，会设置多个监听器)
      modeler?.on('selection.changed', (e: any) => {
        confirmCurrentElement(e.newSelection[0] || null)
      })
      // 设置监听器，监听当前节点属性变化
      modeler?.on('element.changed', ({ element }: any) => {
        if (
          element &&
          element.id === window.bpmnInstance.element.businessObject.id
        ) {
          console.log(
            '---window.bpmnInstance.element---',
            window.bpmnInstance.element
          )
          confirmCurrentElement(element)
        }
      })
    }
  }, [modeler])

  /**
   * 渲染 扩展属性 组件 (所有节点都有)
   */
  function renderExtensionProperties() {
    return (
      <Collapse.Panel
        key="extended-attributes"
        header={
          <Typography style={{ color: colorPrimary, fontWeight: 'bold' }}>
            <NodeIndexOutlined twoToneColor={colorPrimary} />
            &nbsp;扩展属性
          </Typography>
        }
        showArrow={true}
        forceRender={false}
      >
        {taskInfo ? (
          <ExtensionProperties
            businessObject={businessObject}
            panelDatas={props?.panelDatas}
          />
        ) : (
          ''
        )}
      </Collapse.Panel>
    )
  }

  return (
    <>
      <Space direction="vertical" size={0} style={{ display: 'flex' }}>
        {/* TODO 加入最外层的Form表单校验 */}
        <Collapse
          bordered={false}
          expandIconPosition={'end'}
          /* accordion为true时只展示一个面板 */
          accordion={false}
          defaultActiveKey={[
            'baseInfo',
            'extended-attributes',
            'exceptionInfo'
          ]}
          destroyInactivePanel={true}
        >
          {panelWrapper('baseInfo')}
          {isValidArray(exceptionData) ? renderElementBaseInfo() : ''}
          {renderExtensionProperties()}
        </Collapse>
      </Space>
    </>
  )
}
