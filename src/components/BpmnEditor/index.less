@import '@/style/variables.less';
body {
  // CSS 变量定义
  --primary-color: #1777ff;
  --djs-palette-bg-color: #ffffff;
  --canvas-bg-color: #dbdbdb;
}

body {
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 0;
  margin-left: 0;
}
.editorCanvas {
  position: relative;
  display: flex;
}

.leftContent {
  flex: 1;
  height: 100vh;
  .canvas {
    position: absolute;
    width: 100%;
    height: 100%;
    // 加背景线条框
    background: #fff;
    background-image: linear-gradient(
        90deg,
        rgba(220, 220, 220, 0.5) 6%,
        transparent 0
      ),
      linear-gradient(rgba(192, 192, 192, 0.5) 6%, transparent 0);
    background-size: 15px 15px;

    .taskCompleted .djs-visual {
      tspan {
        fill: #52c41a !important;
      }
    }

    .taskRunning .djs-visual {
      tspan {
        fill: #ff792d !important;
      }
    }

    .taskFaile .djs-visual {
      tspan {
        fill: #ff0000 !important;
      }
    }

    .bjs-container {
      position: relative;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      border: 0px !important;
      .djs-palette {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 12;
        flex-shrink: 0;
        background: #fff;
        /* TODO 侧边栏样式修改，支持折叠收缩 */
        // width: 186px;
        // height: 100%;
        // border: 0;
        // border-radius: 0;
        // transition: width 200ms;

        // 改变悬浮颜色
        // .entry:hover {
        //   color: #40a9ff !important;
        // }

        // &.retract {
        //   width: 66px;
        //   .djs-collapse-header {
        //     display: none;
        //   }

        //   .djs-collapse-content {
        //     display: block;
        //   }

        //   .entry {
        //     .content-icon::before {
        //       font-size: 24px;
        //     }
        //   }

        //   .djs-palette-toggle {
        //     &::before {
        //       background: url(../../assets/icons/fold3.svg) 0 0 no-repeat;
        //     }
        //   }
        // }
      }

      // .djs-palette-entries {
      //   height: 100%;
      //   padding-top: 10px;
      //   overflow-y: auto;
      //   background: @fill-bg-light;
      //   border-right: 1px solid #d8d8d8;
      //   .djs-collapse-header {
      //     position: relative;
      //     height: 26px;
      //     padding-left: 25px;
      //     overflow: hidden;
      //     color: #000;
      //     font-size: 14px;
      //     line-height: 26px;
      //     white-space: nowrap;
      //     text-overflow: ellipsis;
      //     cursor: pointer;
      //     &:hover {
      //       hover {
      //         color: #070707;
      //         -webkit-transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      //           border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      //           background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      //           padding 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
      //         transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      //           border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      //           background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
      //           padding 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
      //       }
      //       &::before {
      //         content: '▲';
      //         position: absolute;
      //         top: -1px;
      //         left: 5px;
      //         background-size: 100%;
      //         background-size: 100%;
      //         transform: rotate(-180deg);
      //         transition: all 200ms eano-repeat scroll rightright center
      //           transparentse-in-out;
      //       }
      //       &.open {
      //         &::before {
      //           transform: rotate(0);
      //         }
      //       }
      //     }
      //     .djs-collapse-content {
      //       display: none;
      //       &.open {
      //         display: block;
      //       }
      //     }
      //     .entry {
      //       display: flex;
      //       align-items: center;
      //       float: none;
      //       width: 100%;
      //       height: 38px;
      //       padding: 0 22px;
      //       line-height: unset;
      //       text-align: left;
      //       cursor: pointer;
      //       .content-icon {
      //         position: relative;
      //         flex-shrink: 0;
      //         width: 24px;
      //         height: 20px;
      //         top: 1px;
      //       }
      //       .content-icon::before {
      //         position: absolute;
      //         top: 0;
      //         left: 0;
      //         margin: 0;
      //         font-size: 20px;
      //       }
      //       .content-label {
      //         display: inline-block;
      //         height: 18px;
      //         overflow: hidden;
      //         font-size: 12px;
      //         white-space: nowrap;
      //         text-overflow: ellipsis;
      //       }
      //     }
      //   }
      //   .djs-palette-toggle {
      //     position: absolute;
      //     top: calc(50vh - 15px);
      //     right: -20px;
      //     display: block;
      //     width: 20px;
      //     height: 30px;
      //     background: transparent;
      //     border-right: 0;
      //     cursor: pointer;
      //     &::before {
      //       position: absolute;
      //       right: 13px;
      //       width: 100%;
      //       height: 100%;
      //       content: '';
      //       background: url('../../assets/icons/unfold.svg') 0 0 no-repeat;
      //     }
      //   }
      // }
    }
  }
}
.hiddenPaltte {
  .djs-palette-entries {
    display: none !important;
  }
}

.rightContent {
  flex: 0 0 @editorPanelWidth;
  height: 100vh;
  // 折叠按钮
  .properties-panel-fold {
    position: absolute;
    top: calc(50vh - 15px);
    right: calc(@editorPanelWidth - 16px);
    z-index: 11;
    width: 30px;
    height: 30px;
    background-color: transparent !important;
    border-right: 0;
    cursor: pointer;
    transition: all 200ms ease-in-out;
  }

  .properties-panel-fold::before {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('./assets/icons/fold3.svg') 0 0 no-repeat;
    transform: rotate(-90deg);
    transition: transform 0.5s;
    content: '';
  }

  .fold {
    right: calc(@editorPanelWidth - 26px);
    transition: transform 0.5s;
  }

  .fold::before {
    background: url('./assets/icons/fold3.svg') 0 0 no-repeat;
    transform: translateX(calc(@editorPanelWidth - 16px)) rotate(90deg);
    transition: transform 0.5s;
  }

  /* line 117, styles/app.less */
  .properties-panel-parent {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    width: @editorPanelWidth;
    overflow: auto;
    background-color: @fill-bg-light;
    border-left: 1px solid @fill-disabled;
  }

  // 右侧菜单栏显隐
  .hidePanel {
    transform: translateX(calc(@editorPanelWidth - 6px));
    transition: transform 0.5s;
  }

  .showPanel {
    transform: translateX(0);
    transition: transform 0.5s;
  }

  /* line 126, styles/app.less */
  .properties-panel-parent:empty {
    display: none;
  }

  /* line 129, styles/app.less */
  .properties-panel-parent > .djs-properties-panel {
    min-height: 100%;
    padding-bottom: 70px;
  }
}

.ant-collapse-item {
  background-color: #fff !important;
}

.customPanel {
  height: 100vh;
  overflow-y: auto;
  border-left: 1px solid #eee;
}

// 隐藏模拟流转的toggle按钮
.bts-toggle-mode {
  display: none;
}

//侧边栏配置
.djs-palette.open {
  .djs-palette-entries {
    div[class^='bpmn-icon-']:before,
    div[class*='bpmn-icon-']:before {
      line-height: unset;
    }

    div.entry {
      position: relative;
    }

    div.entry:hover {
      &::after {
        position: absolute;
        top: 0;
        right: -10px;
        bottom: 0;
        z-index: 100;
        display: inline-block;
        box-sizing: border-box;
        width: max-content;
        padding: 0 16px;
        overflow: hidden;
        font-size: 0.5em;
        font-variant: normal;
        text-transform: none;
        text-decoration: inherit;
        vertical-align: text-bottom;
        background: #fafafa;
        // background-color: red;
        border: 1px solid #cccccc;
        border-radius: 4px;
        box-shadow: 0 0 6px #eeeeee;
        transform: translateX(100%);
        content: attr(title);
      }
    }
    // div.entry {
    //   position: relative;
    // }

    // div.entry:hover {
    //   &::after {
    //     position: absolute;
    //     top: 0;
    //     right: -10px;
    //     bottom: 0;
    //     z-index: 100;
    //     display: inline-block;
    //     box-sizing: border-box;
    //     width: max-content;
    //     padding: 0 16px;
    //     overflow: hidden;
    //     font-size: 0.5em;
    //     font-variant: normal;
    //     text-transform: none;
    //     text-decoration: inherit;
    //     vertical-align: text-bottom;
    //     background: #fafafa;
    //     // background-color: red;
    //     border: 1px solid #cccccc;
    //     border-radius: 4px;
    //     box-shadow: 0 0 6px #eeeeee;
    //     transform: translateX(100%);
    //     content: attr(title);
    //   }
    // }
  }
}

// palette框: 背景和边框样式
.djs-palette {
  // todo 这里的背景色需要跟随明暗主题而变化
  background: var(--djs-palette-bg-color);
  border: solid 1px var(--primary-color);
  border-radius: 2px;
}

// palette框内的分割线: 颜色
.djs-palette .separator {
  border-bottom: solid 1px var(--primary-color);
}

// palette内的元素: hover效果
.djs-palette .entry:hover {
  color: var(--primary-color);
}

// palette内的元素(抓手工具、套索工具、空间工具、全局连接工具): 选中效果
.djs-palette .highlighted-entry {
  color: var(--primary-color) !important;
}

// 元素外的虚线框: 选中效果
.djs-element.selected .djs-outline {
  stroke: var(--primary-color);
  stroke-width: 1px;
}

// 元素外的虚线框: hover效果
.djs-element.hover .djs-outline {
  stroke: var(--primary-color);
  stroke-width: 1px;
}

// palette内的元素(创建分组工具): 选中效果
svg.new-parent {
  background: var(--shape-drop-allowed-fill-color) !important;
}

// 页面元素：拖取样式
.djs-dragger * {
  fill: none !important;
  stroke: var(--primary-color) !important;
}

// 对齐线：拖动元素对齐时的对齐线样式
.djs-snap-line {
  pointer-events: none;
  stroke: var(--primary-color);
  stroke-linecap: round;
  stroke-width: 2px;
}

// 选取palette元素时：准备放置时的背景
svg.new-parent {
  background: transparent !important;
}

// 元素文字: 颜色
.djs-label {
  fill: var(--primary-color) !important;
}

// canvas右下角的bpmn.io文字样式: 颜色
.bjs-container > .bjs-powered-by {
  color: var(--primary-color) !important;
}

/* bpmn style start */
.custom-common-task {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 1em 1em;
}

.custom-task-PH {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/pH.svg');
}

.custom-task-Crystal {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Crystal.svg');
}

.custom-task-Dilute {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Dilute.svg');
}

.custom-task-Dissolve {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Dissolve.svg');
}

.custom-task-Dry {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Dry.svg');
}

.custom-task-DrySolution {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/DrySolution.svg');
}

.custom-task-Exsolution {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Exsolution.svg');
}

.custom-task-Extract {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Extract.svg');
}

.custom-task-Filtration {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Filtration.svg');
}

.custom-task-GetMaterial {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/GetMaterial.svg');
}

.custom-task-ParallelReaction {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Reaction.svg');
}

.custom-task-AutoMix {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Mix.svg');
}

.custom-task-MakeIngredients {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/MakeIngredients.svg');
}

.custom-task-MicroWaveReaction {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/MicroWaveReaction.svg');
}

.custom-task-Mix {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Mix.svg');
}

.custom-task-PhaseSeparate {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/PhaseSeparate.svg');
}

.custom-task-Purify {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Purify.svg');
}

.custom-task-Quench {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Quench.svg');
}

.custom-task-Reaction {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Reaction.svg');
}

.custom-task-Recrystal {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Recrystal.svg');
}

.custom-task-ReturnMaterial {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/ReturnMaterial.svg');
}

.custom-task-SetupReaction {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/SetupReaction.svg');
}

.custom-task-TLC {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/TLC.svg');
}

.custom-task-Triturate {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Triturate.svg');
}

.custom-task-WeighProduct {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/WeighProduct.svg');
}

.custom-task-RobotScaleMix {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Mix.svg');
}

.custom-task-RobotReaction {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Reaction.svg');
}

.custom-task-RobotSetupReaction {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/SetupReaction.svg');
}

.custom-task-RobotFilter {
  .custom-common-task;
  background-image: url('./assets/chemistry-icons/Filtration.svg');
}

/* Custom bpmn palette icon */
.icon-custom {
  background-repeat: no-repeat;
  background-position: center;
  background-size: 65%;
  border-radius: 50%;
}

/* 将工具选项完全换一张图片 */
.iconCommonStyle {
  position: absolute;
  top: 17px;
  left: 4px;
  font-size: 12px;
  content: '';
}

.icon-custom.start-task {
  position: relative;
  background-image: url('./assets/icons/start-task.svg');
}

.icon-custom.start-task::after {
  .iconCommonStyle;
}

.icon-custom.end-task {
  position: relative;
  background-image: url('./assets/icons/end-task.svg');
}

.icon-custom.end-task::after {
  .iconCommonStyle;
  // content: 'c12';
}

/* 自定义 contextPad 的样式 */
.djs-context-pad .start-task.entry:hover {
  background: url('./assets/images/integrate.png') center no-repeat !important;
  background-size: cover !important;
}

/* 隐藏bpmn logo */
.bjs-powered-by {
  display: none !important;
}

/* TOOD 需求评审后，再展示部分工具 */
.bpmn-icon-screw-wrench,
.bpmn-icon-group,
.bpmn-icon-data-store,
.bpmn-icon-data-object,
.bpmn-icon-participant,
.bpmn-icon-subprocess-expanded,
.bpmn-icon-gateway-none,
.bpmn-icon-intermediate-event-none,
.bpmn-icon-task {
  display: none !important;
}
