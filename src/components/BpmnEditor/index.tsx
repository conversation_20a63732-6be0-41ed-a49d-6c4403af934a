import { ConfigProvider, message, notification } from 'antd'
import cs from 'classnames'
// 引入属性面板(properties-panel)样式
import 'bpmn-js-properties-panel/dist/assets/element-templates.css'
import 'bpmn-js-properties-panel/dist/assets/properties-panel.css'
// 模拟流转流程
import 'bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css'
// 引入bpmn工作流绘图工具(bpmn-js)样式
import 'bpmn-js/dist/assets/bpmn-js.css'
import 'bpmn-js/dist/assets/diagram-js.css'
import type BpmnModeler from 'bpmn-js/lib/Modeler'
import './index.less'
// 引入bpmn建模器
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import { useDispatch, useSelector } from 'umi'
import type { ProcessDesignerProps } from './index.d'
// 引入流程图文件
import DefaultEmptyXML from '@/components/BpmnEditor/bpmn/constant/emptyXml'
import ConfigServer from '@/components/BpmnEditor/components/ConfigServer'
import EditingTools from '@/components/BpmnEditor/components/EditingTools'
import FullModal from '@/components/BpmnEditor/components/FullModal'
import PropertyPanel from '@/components/BpmnEditor/components/PropertyPanel'
import {
  darkThemeData,
  defaultThemeData
} from '@/components/BpmnEditor/globalTheme'
import { initBpmnModeler } from '@/components/BpmnEditor/initModeler'
import { TaskInfo } from '@/types/models/task-info'
import Logger from '@/utils/Logger'
import { isArray, isEmpty } from 'lodash'
import {
  innerSVG,
  append as svgAppend,
  attr as svgAttr,
  create as svgCreate
} from 'tiny-svg'
import { downloadProcess } from './utils'
function ProcessDesigner(props: ProcessDesignerProps, ref: any) {
  const [bpmnModeler, setBpmnModeler] = useState<BpmnModeler>()
  const [scale, setScale] = useState(1)
  const [hidePanel, setHidePanel] = useState<boolean>(
    props?.panelDatas?.readOnly
  )
  const [svgSrc, setSvgSrc] = useState('') // 图片地址
  const [svgVisible, setSvgVisible] = useState(false)
  const bpmnState = useSelector((state) => state?.bpmn)
  const bpmnPrefix = bpmnState?.prefix
  const processId = bpmnState?.processId
  const processName = bpmnState?.processName

  const themeState = useSelector((state) => state?.theme)
  const colorPrimary = themeState?.colorPrimary
  const borderRadius = themeState?.borderRadius
  const darkMode = themeState?.darkMode
  const dispatch = useDispatch()

  const handlePanelFold = () => setHidePanel(!hidePanel)

  /**
   * 初始化建模器
   * 1、这一步在绘制流程图之前进行，且随流程前缀改变而改变；
   * 2、因为解析器和解析文件与流程引擎类型(也就是前缀)有关，因此这里依赖的变量是放在redux里的流程前缀名
   */
  useEffect(() => {
    // 重新加载前需要销毁之前的modeler，否则页面上会加载出多个建模器
    if (!bpmnPrefix) return
    if (bpmnModeler) {
      // Logger.prettyError('---bpmnModeler---', bpmnModeler)
      bpmnModeler?.destroy()
      setBpmnModeler(undefined)
    }
    ;(async () => {
      // 每次重新加载前需要先消除之前的流程信息
      dispatch({
        type: 'bpmn/handleProcessId',
        payload: undefined
      })
      await dispatch({
        type: 'bpmn/handleProcessName',
        payload: undefined
      })
      Logger.prettyError('---bpmnPrefix---', bpmnPrefix)
      let newBpmnModeler = await initBpmnModeler(bpmnPrefix)
      setBpmnModeler(newBpmnModeler)
    })()
  }, [bpmnPrefix])

  /**
   * NOTE 绘制流程图:调用 modeler 的 importXML 方法，将 xml 字符串转为图像；
   */
  function createBpmnDiagram(xml?: string) {
    console.log('【绘制流程图】1、开始绘制流程图')
    let newId = processId || 'Process_' + new Date().getTime(),
      newName = processName || '业务流程_' + new Date().getTime(),
      newXML = xml ? xml : DefaultEmptyXML(newId, newName, bpmnPrefix)
    // 执行importXML方法
    try {
      bpmnModeler?.importXML(
        newXML
        // , (err: string) => {if (!err) detectTaskStatus()}
      )
    } catch (e) {
      Logger.prettyError('【流程图绘制出错】错误日志如下: ↓↓↓', e)
      notification.error({
        message: '导入失败',
        description: '流程图绘制出错'
      })
    }
    // NOTE 更新流程信息，初始化建模器后，有了modeler，通过modeler获取到canvas，就能拿到rootElement，从而获取到流程的初始信息
    console.log('【绘制流程图】2、更新流程节点信息')
    setTimeout(() => {
      if (!bpmnModeler) return
      const canvas = bpmnModeler.get('canvas')
      console.log('---canvas---', canvas)
      if (canvas && canvas?.length && canvas?.getRootElement()) {
        const rootElement = canvas?.getRootElement()
        // console.log('---rootElement---', rootElement)
        // 获取流程id和name
        const id: string = rootElement?.id
        const name: string = rootElement?.businessObject?.name
        dispatch({
          type: 'bpmn/handleProcessId',
          payload: id
        })
        dispatch({
          type: 'bpmn/handleProcessName',
          payload: name
        })
      }
    }, 10)
    console.log('【绘制流程图】3、流程图绘制完成')
  }

  /**
   * 属性面板监听器
   * 1、属性面板监听器，当监听到属性面板的属性发生变化，会同步更新到xml字符串中；
   * 2、监听器要等到流程图绘制结束后才能添加；
   */
  function bindPropertiesListener() {
    console.log('【绑定属性面板监听器】1、开始绑定')
    bpmnModeler?.on('commandStack.changed', async () => {
      // 监听当前是否可撤销与恢复
      //  其它操作
    })
    console.log('【绑定属性面板监听器】2、绑定成功')
  }

  /**
   * 绘制流程图，并设置属性面板的监听器
   * 1、建模器初始化完成后，开始绘制流程图，如果需要创建空白的流程图可以使用bpmnModeler.createDiagram()方法，但是这个流程的id是固定的，是bpmn内部默认的xml字符串；
   */
  useEffect(() => {
    if (!bpmnModeler)
      return // Logger.prettyError('---bpmnModeler change---', bpmnModeler)
    ;(async () => {
      if (props?.panelDatas?.workflow) {
        await createBpmnDiagram(props?.panelDatas?.workflow)
      } else {
        await createBpmnDiagram() // 绘制流程图
      }
      bindPropertiesListener() // 之后绑定属性面板监听器
    })()
  }, [bpmnModeler, props?.panelDatas?.workflow])

  const setExecuteIcon = (curId: string, newBpmn: any, colorClass: string) => {
    const elementRegistry = newBpmn.get('elementRegistry')
    if (elementRegistry._elements[curId]) {
      const element = elementRegistry._elements[curId].gfx
      let icon = svgCreate('image')
      svgAttr(icon, {
        href: require(`./assets/images/${colorClass}.png`),
        x: 80,
        y: 0,
        width: 18,
        height: 18
      })
      svgAppend(element, icon)
      element.classList.add(colorClass)
    }
  }

  const setErrorIcon = (curId: string, newBpmn: any, colorClass: string) => {
    const elementRegistry = newBpmn.get('elementRegistry')
    if (elementRegistry._elements[curId]) {
      const element = elementRegistry._elements[curId].gfx
      let icon = svgCreate('image')
      svgAttr(icon, {
        href: require(`./assets/images/${colorClass}.png`),
        x: 80,
        y: 66,
        width: 18,
        height: 18
      })
      svgAppend(element, icon)
      element.classList.add(colorClass)
    }
  }

  const setErrorCount = (
    curId: string,
    newBpmn: any,
    fillUp?: boolean,
    errorCount: number
  ) => {
    const elementRegistry = newBpmn.get('elementRegistry')
    if (elementRegistry._elements[curId]) {
      const element = elementRegistry._elements[curId].gfx
      let countLength: number = (errorCount + '').length
      let iconWidth: number
      switch (countLength) {
        case 1:
        case 2:
          iconWidth = 18
          break
        case 3:
          iconWidth = 20
          break
        default:
          iconWidth = 6 * countLength
          break
      }
      let svgIcon = `<svg width="${iconWidth}" height="18">
      <rect width="${iconWidth}" height="18" rx="10"  stroke="black" stroke-width="0" fill='red'/>
          <text
            x="50%"
            y="54%"
            dominant-baseline="middle"
            font-size="10"
            text-anchor="middle"
            fill="#fff"
          >
          ${errorCount}
          </text>
          </svg>`
      let icon = svgCreate('svg')
      svgAttr(icon, {
        x: 80,
        y: 66,
        width: iconWidth,
        height: 18
      })
      innerSVG(icon, svgIcon)
      svgAppend(element, icon)
    }
  }

  const setRobotIcon = (curId: string, newBpmn: any, colorClass: string) => {
    const elementRegistry = newBpmn.get('elementRegistry')
    if (elementRegistry._elements[curId]) {
      const element = elementRegistry._elements[curId].gfx
      let icon = svgCreate('image')
      svgAttr(icon, {
        href: require(`./assets/images/${colorClass}.png`),
        x: 80,
        y: 30,
        width: 18,
        height: 18
      })
      svgAppend(element, icon)
      element.classList.add(colorClass)
    }
  }

  const detectTaskStatus = () => {
    if (!isEmpty(props?.operationProcess) && isArray(props?.operationProcess)) {
      props?.operationProcess.map((e: TaskInfo) => {
        if (e?.operator && e?.operator.startsWith('robot-')) {
          setRobotIcon(e?.taskNo, bpmnModeler, 'robot')
        }
        switch (e?.status) {
          case 'completed':
            setExecuteIcon(e?.taskNo, bpmnModeler, 'taskCompleted')
            break
          case 'running':
            setExecuteIcon(e?.taskNo, bpmnModeler, 'taskRunning')
            break
          case 'failed':
            setExecuteIcon(e?.taskNo, bpmnModeler, 'taskFaile')
            break
          case 'canceled':
            setExecuteIcon(e?.taskNo, bpmnModeler, 'taskCanceled')
            break
          case 'inserted':
            setErrorIcon(e?.taskNo, bpmnModeler, 'taskInsert')
            break
          case 'hold':
            setExecuteIcon(e?.taskNo, bpmnModeler, 'taskHold')
            break
          default:
            break
        }
        if (e?.errorCount) {
          setErrorCount(e?.taskNo, bpmnModeler, e?.fillUp, e?.errorCount)
        }
        return null
      })
    }
  }

  useEffect(() => {
    // Logger.prettyError('---props?.operationProcess---', props?.operationProcess)
    detectTaskStatus()
  }, [props?.operationProcess])

  const handleUndo = () => bpmnModeler.get('commandStack').undo()
  const handleRedo = () => bpmnModeler.get('commandStack').redo()

  // 导入 xml 文件
  const handleOpenFile = (e) => {
    /* TODO 限定上传类型只能为 .xml, .bpmn； */
    if (e.target.files.length > 0) {
      const file = e.target.files[0]
      const reader = new FileReader()
      let xmlStr: any = ''
      reader.readAsText(file)
      reader.onload = (event) => {
        xmlStr = event.target.result
        console.log('【正在打开本地文件】文件内容如下: ↓↓↓', xmlStr)
        createBpmnDiagram(xmlStr)
      }
    }
  }

  function downloadFile(type: 'xml' | 'bpmn' | 'svg') {
    downloadProcess(type, bpmnModeler).then(() =>
      message.info(`成功另存为${type}文件`)
    )
  }

  const handlePreview = () => {
    bpmnModeler.saveSVG({ format: true }, (_err: any, data) => {
      setSvgSrc(data)
      setSvgVisible(true)
    })
  }

  const handleCancel = () => {
    setSvgSrc('')
    setSvgVisible(false)
  }

  const handleRestart = () => {
    createBpmnDiagram()
  }

  // 流程图放大缩小
  const handleZoom = (radio?: number) => {
    let newScale: number
    if (!radio) {
      newScale = 1.0 // 不输入radio则还原
      bpmnModeler.get('canvas').zoom('fit-viewport', 'auto')
    } else if (scale + radio <= 0.2) {
      newScale = 0.2 // 最小缩小倍数
      message.warning('已达到最小倍数 20%, 不能继续缩小')
      bpmnModeler.get('canvas').zoom(newScale)
    } else {
      newScale = scale + radio
      if (newScale > 4) {
        newScale = 4
        message.warning('已达到最大倍数 400%, 不能继续放大')
      }
      bpmnModeler.get('canvas').zoom(newScale)
    }
    setScale(newScale)
  }

  const handleSave = (cb) => {
    if (!bpmnModeler) return
    let bpmnXml: string = ''
    // svgXml: string = ''
    bpmnModeler.saveXML({ format: true }, (_err: any, xml) => {
      bpmnXml = xml
      // console.log('---bpmnXml---', bpmnXml)
      cb(bpmnXml)
    })
    /* bpmnModeler.saveSVG({ format: true }, (_err: any, data) => {
      svgXml = data
      Logger.prettySuccess('---svgXml---', svgXml)
    }) */
  }

  const queryRef = useRef<HTMLDivElement>()
  useImperativeHandle(ref, () => {
    return {
      handleSave
    }
  })

  return (
    <div ref={queryRef}>
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: colorPrimary,
            borderRadius: borderRadius,
            ...(darkMode && darkThemeData) // 暗夜主题
          }
        }}
      >
        <div
          className="editorCanvas"
          style={{
            backgroundColor: darkMode
              ? defaultThemeData.darkBgColor
              : defaultThemeData.lightBgColor
          }}
        >
          <EditingTools
            bpmnModeler={bpmnModeler}
            handlePublish={() => alert('开发中～')}
            onOpenFIle={handleOpenFile}
            onRestart={handleRestart}
            onUndo={handleUndo}
            onRedo={handleRedo}
            onDownloadSvg={() => downloadFile('svg')}
            onDownloadXml={() => downloadFile('xml')}
            onDownloadBpmn={() => downloadFile('bpmn')}
            onZoomIn={() => handleZoom(0.1)}
            onZoomOut={() => handleZoom(-0.1)}
            onZoomReset={() => handleZoom()}
            onPreview={handlePreview}
            extraTools={props?.extraTools}
            readOnly={props?.panelDatas?.readOnly}
            scale={scale}
          />
          <div
            className={cs('leftContent', {
              hiddenPaltte: props?.panelDatas?.readOnly
            })}
            style={{
              backgroundColor: darkMode
                ? defaultThemeData.darkCanvasBgColor
                : defaultThemeData.lightCanvasBgColor
            }}
          >
            <div id="canvas" className="canvas" />
          </div>
          <div className="rightContent">
            <div
              className={cs('properties-panel-fold', {
                fold: hidePanel,
                hide: !hidePanel
              })}
              style={{
                backgroundColor: darkMode
                  ? defaultThemeData.darkBgColor
                  : defaultThemeData.lightBgColor
              }}
              id="js-panel-fold"
              onClick={handlePanelFold}
            />
            <div
              className={cs('properties-panel-parent', {
                hidePanel: hidePanel,
                showPanel: !hidePanel
              })}
              id="properties-panel"
              style={{ height: '100%' }}
            >
              <PropertyPanel
                operationProcess={props?.operationProcess}
                modeler={bpmnModeler}
                panelDatas={props?.panelDatas}
                taskList={props?.taskList}
                experimentalNo={props?.experimentalNo}
              />
            </div>
          </div>
        </div>
      </ConfigProvider>
      {svgVisible && (
        <FullModal visible={svgVisible} onCancel={handleCancel}>
          <div
            dangerouslySetInnerHTML={{
              __html: svgSrc
            }}
          />
        </FullModal>
      )}
      <ConfigServer />
    </div>
  )
}

export default forwardRef(ProcessDesigner)
