<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    
    <g id="brain" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon" transform="translate(-58.000000, -1063.000000)">
            <g id="编组-4" transform="translate(57.000000, 763.000000)">
                <g id="icon/化学/称量并加料" transform="translate(1.000000, 300.000000)">
                    <polygon id="矩形备份-7" fill="#E0EFFF" points="3 6.5 8 6.5 7.5 8 7.5 36.5 3.5 36.5 3.5 8"></polygon>
                    <polyline id="路径" stroke="#333333" stroke-linecap="round" stroke-linejoin="round" points="8 6.5 7.5 8 7.5 36.5 3.5 36.5 3.5 8 3 6.5"></polyline>
                    <rect id="矩形" fill="#333333" x="6" y="11" width="2" height="1" rx="0.5"></rect>
                    <rect id="矩形备份" fill="#333333" x="6" y="15" width="2" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-2" fill="#333333" x="6" y="19" width="2" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-3" fill="#333333" x="6" y="23" width="2" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-4" fill="#333333" x="6" y="27" width="2" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-5" fill="#333333" x="6" y="31" width="2" height="1" rx="0.5"></rect>
                    <rect id="矩形备份-6" fill="#333333" x="6" y="35" width="2" height="1" rx="0.5"></rect>
                    <rect id="矩形" fill="#333333" x="2" y="36" width="7" height="1" rx="0.5"></rect>
                    <path d="M15.3806897,13.9338929 L19.2741203,14.3511895 C19.82326,14.4100461 20.3161383,14.0125932 20.3749949,13.4634536 C20.3787887,13.4280572 20.3806897,13.3924834 20.3806897,13.3568843 L20.3806897,7.46979128 L20.3806897,7.46979128 L21.1797278,6.88966426 L20.1306897,5.70665156 L15.3806897,13.9338929 Z" id="路径-3备份" fill="#027AFF" transform="translate(18.280209, 10.088221) rotate(60.000000) translate(-18.280209, -10.088221) "></path>
                    <path d="M13.5,5.5 L13.5,12.5 C13.5,13.0522847 13.9477153,13.5 14.5,13.5 L19.5,13.5 C20.0522847,13.5 20.5,13.0522847 20.5,12.5 L20.5,6.5 L20.5,6.5 L21.5,5.5" id="路径-3" stroke="#333333" stroke-linecap="round" stroke-linejoin="round" transform="translate(17.500000, 9.500000) rotate(60.000000) translate(-17.500000, -9.500000) "></path>
                    <path d="M30.5,11.5 L30.0000536,12.084201 L30.0009859,22.2521854 C32.9489824,23.5863902 35,26.5536747 35,30 C35,32.6082209 33.8252496,34.9420641 31.9759961,36.5012825 L21.0240039,36.5012825 C19.1747504,34.9420641 18,32.6082209 18,30 C18,26.5532867 20.0514795,23.5857222 23,22.2517349 L23,12 L22.5,11.5" id="路径" fill="#E0EFFF"></path>
                    <path d="M31.9759961,36.5012825 L21.0240039,36.5012825 C19.6633092,35.3539978 18.6677991,33.7873214 18.2367599,32.0005394 L34.7632401,32.0005394 C34.3322009,33.7873214 33.3366908,35.3539978 31.9759961,36.5012825 Z" id="形状结合" fill="#027AFF"></path>
                    <circle id="椭圆形" fill="#027AFF" opacity="0.5" cx="28.0004022" cy="28.9981497" r="1"></circle>
                    <circle id="椭圆形备份-3" fill="#027AFF" cx="23.0004022" cy="31.9981497" r="1"></circle>
                    <circle id="椭圆形备份-2" fill="#027AFF" cx="20.5004022" cy="29.4981497" r="1"></circle>
                    <circle id="椭圆形备份" fill="#027AFF" cx="30.4994369" cy="31.4978279" r="1.5"></circle>
                    <path d="M30.5,11.5 L30.0000536,12.084201 L30.0009859,22.2521854 C32.9489824,23.5863902 35,26.5536747 35,30 C35,32.6082209 33.8252496,34.9420641 31.9759961,36.5012825 L21.0240039,36.5012825 C19.1747504,34.9420641 18,32.6082209 18,30 C18,26.5532867 20.0514795,23.5857222 23,22.2517349 L23,12 L22.5,11.5" id="路径备份" stroke="#333333" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M26.5,33 C26.5,26 26.5,20.8333333 26.5,17.5 C26.5,12.5 25,10 23,10" id="路径-23" stroke="#027AFF" stroke-linecap="round" stroke-linejoin="round"></path>
                </g>
            </g>
        </g>
    </g>
</svg>