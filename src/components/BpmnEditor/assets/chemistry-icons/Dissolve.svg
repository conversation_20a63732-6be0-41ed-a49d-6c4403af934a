<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    
    <g id="run" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon" transform="translate(-288.000000, -1076.000000)">
            <g id="编组-3" transform="translate(53.000000, 776.000000)">
                <g id="编组-4" transform="translate(11.000000, 0.000000)">
                    <g id="icon/化学/静置等待备份-2" transform="translate(224.000000, 300.000000)">
                        <path d="M34,19 L34,35 C34,36.6568542 32.6568542,38 31,38 L11,38 C9.34314575,38 8,36.6568542 8,35 L8,19 L34,19 Z" id="路径" fill="#E0EFFF"></path>
                        <path d="M34,9 L34,13 L34,35 C34,36.6568542 32.6568542,38 31,38 L11,38 C9.34314575,38 8,36.6568542 8,35 C8,25.2222222 8,17.8888889 8,13 C8,12.1111111 7.33333333,10.7777778 6,9" id="路径" stroke="#333333" stroke-linecap="round" stroke-linejoin="round"></path>
                        <polygon id="多边形备份-5" fill="#027AFF" opacity="0.200000003" points="18.2908965 32 19.387862 33.9 18.2908965 35.8 16.0969655 35.8 15 33.9 16.0969655 32"></polygon>
                        <polygon id="多边形备份-9" fill="#027AFF" opacity="0.5" points="32.2908965 32 33.387862 33.9 32.2908965 35.8 30.0969655 35.8 29 33.9 30.0969655 32"></polygon>
                        <polygon id="多边形备份-12" fill="#027AFF" opacity="0.400000006" points="25.2908965 33 26.387862 34.9 25.2908965 36.8 23.0969655 36.8 22 34.9 23.0969655 33"></polygon>
                        <polygon id="多边形备份-11" fill="#027AFF" opacity="0.100000001" points="22.2908965 27 23.387862 28.9 22.2908965 30.8 20.0969655 30.8 19 28.9 20.0969655 27"></polygon>
                        <polygon id="多边形备份-13" fill="#027AFF" opacity="0.300000012" points="27.2908965 28 28.387862 29.9 27.2908965 31.8 25.0969655 31.8 24 29.9 25.0969655 28"></polygon>
                        <line x1="8.5" y1="36" x2="39" y2="2" id="路径-30" stroke="#333333" stroke-linecap="round" stroke-linejoin="round"></line>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>