<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#027AFF" stop-opacity="0.144862462" offset="0%"></stop>
            <stop stop-color="#027AFF" stop-opacity="0.0610232053" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="54.9333136%" y1="14.8465031%" x2="22.3945321%" y2="87.8828861%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" stop-opacity="0.575432624" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.0718849022" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="52.2760622%" y1="14.8465031%" x2="37.2637811%" y2="87.8828861%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" stop-opacity="0.575432624" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.0718849022" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#8A8A8A" offset="0%"></stop>
            <stop stop-color="#555555" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="linearGradient-5">
            <stop stop-color="#C8C8C8" offset="0%"></stop>
            <stop stop-color="#CCCCCC" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="run" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon" transform="translate(-736.000000, -976.000000)">
            <g id="编组-3" transform="translate(53.000000, 776.000000)">
                <g id="编组-4" transform="translate(11.000000, 0.000000)">
                    <g id="icon/化学/称重" transform="translate(672.000000, 200.000000)">
                        <g id="编组-2" transform="translate(15.500000, 16.500000)">
                            <path d="M0,0 L0,11 C6.76353751e-17,11.5522847 0.44771525,12 1,12 L9,12 C9.55228475,12 10,11.5522847 10,11 L10,1 L10,1 L11,0" id="路径-3" stroke="#333333" stroke-linecap="round" stroke-linejoin="round"></path>
                            <rect id="矩形" fill="#027AFF" x="1.5" y="9.5" width="2" height="2"></rect>
                            <rect id="矩形备份" fill="#027AFF" x="4.5" y="9.5" width="2" height="2"></rect>
                            <rect id="矩形备份-3" fill="#027AFF" x="7.5" y="9.5" width="2" height="2"></rect>
                            <rect id="矩形备份-2" fill="#027AFF" transform="translate(3.800000, 7.000000) rotate(31.000000) translate(-3.800000, -7.000000) " x="2.30000006" y="5.5" width="3" height="3"></rect>
                            <rect id="矩形备份-9" fill="#027AFF" transform="translate(7.450000, 7.650000) rotate(-9.000000) translate(-7.450000, -7.650000) " x="6.30000006" y="6.5" width="2.3" height="2.3"></rect>
                        </g>
                        <rect id="矩形" fill="url(#linearGradient-1)" x="6" y="2" width="28" height="31"></rect>
                        <polygon id="矩形备份-7" fill="url(#linearGradient-2)" points="26 2 34 2 18 33 9 33"></polygon>
                        <polygon id="矩形备份-8" fill="url(#linearGradient-3)" points="21 2 24 2 8 33 7.0190216 29.0881456"></polygon>
                        <rect id="矩形备份-6" stroke="#333333" stroke-linejoin="round" x="6" y="2" width="28" height="31"></rect>
                        <path d="M27,29 L28.3322157,31 L34.1643297,31 C34.6205456,31 35.0189256,31.3087701 35.1327215,31.7505658 L36.678176,37.7505658 C36.8159348,38.2853938 36.4940465,38.830633 35.9592185,38.9683917 C35.8777347,38.98938 35.7939277,39 35.7097843,39 L4.29021571,39 C3.73793096,39 3.29021571,38.5522847 3.29021571,38 C3.29021571,37.9158566 3.30083572,37.8320496 3.32182397,37.7505658 L4.86727852,31.7505658 C4.98107436,31.3087701 5.37945442,31 5.83567026,31 L12.6662157,31 L14,29 L27,29 Z" id="形状结合" fill="url(#linearGradient-4)" fill-rule="nonzero"></path>
                        <path d="M25.9913422,37 L15.0086578,37 C14.495822,37 14.0731507,36.6139598 14.0153856,36.1166211 L14.008695,35.9913796 L14.0259364,33.9913796 C14.0306683,33.4424788 14.476978,33 15.0258992,33 L25.9741008,33 C26.523022,33 26.9693317,33.4424788 26.9740636,33.9913796 L26.9913429,35.9913796 C26.9957258,36.5041964 26.6133436,36.9301798 26.1165213,36.99223 L25.9913422,37 Z" id="路径备份" fill="url(#linearGradient-5)" fill-rule="nonzero"></path>
                        <circle id="椭圆形" fill="#FFFFFF" cx="31" cy="35" r="1"></circle>
                        <circle id="椭圆形备份" fill="#FFFFFF" cx="10" cy="35" r="1"></circle>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>