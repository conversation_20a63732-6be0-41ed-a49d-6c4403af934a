<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    
    <g id="run" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon" transform="translate(-176.000000, -976.000000)">
            <g id="编组-3" transform="translate(53.000000, 776.000000)">
                <g id="编组-4" transform="translate(11.000000, 0.000000)">
                    <g id="icon/化学/水洗" transform="translate(112.000000, 200.000000)">
                        <g id="编组-3" transform="translate(28.012260, 9.517949) scale(-1, 1) translate(-28.012260, -9.517949) translate(20.500000, 2.035898)">
                            <path d="M3.34479128,9.89799451 L7.23822196,10.3152912 C7.7873616,10.3741478 8.28023995,9.97669484 8.33909656,9.4275552 C8.34289033,9.39215878 8.34479128,9.35658503 8.34479128,9.32098588 L8.34479128,3.4338929 L8.34479128,3.4338929 L9.14382939,2.85376588 L8.09479128,1.67075318 L3.34479128,9.89799451 Z" id="路径-3备份" fill="#50A3FF" transform="translate(6.244310, 6.052323) rotate(60.000000) translate(-6.244310, -6.052323) "></path>
                            <path d="M1.46410162,1.46410162 L1.46410162,8.46410162 C1.46410162,9.01638636 1.91181687,9.46410162 2.46410162,9.46410162 L7.46410162,9.46410162 C8.01638636,9.46410162 8.46410162,9.01638636 8.46410162,8.46410162 L8.46410162,2.46410162 L8.46410162,2.46410162 L9.46410162,1.46410162" id="路径-3" stroke="#333333" stroke-linecap="round" stroke-linejoin="round" transform="translate(5.464102, 5.464102) rotate(60.000000) translate(-5.464102, -5.464102) "></path>
                            <path d="M11.0245191,5.96410162 C14.5245191,5.96410162 15.0245191,8.96410162 15.0245191,14.9641016" id="路径-2" stroke="#50A3FF" stroke-linecap="round" stroke-linejoin="round"></path>
                        </g>
                        <g id="编组-4" transform="translate(3.500000, 10.000000)">
                            <path d="M13,20 L13,14 L19,14 L19,20 L23.827575,24.5436 C24.6319225,25.300633 24.6702785,26.5663819 23.9132456,27.3707294 C23.5352944,27.7723025 23.0083056,28 22.4568456,28 L9.54315438,28 C8.43858488,28 7.54315438,27.1045695 7.54315438,26 C7.54315438,25.44854 7.77085184,24.9215512 8.17242498,24.5436 L13,20 Z" id="矩形" fill="#E0EFFF"></path>
                            <path d="M22,18.5 L19,18.5 L19,20 L24.5875013,24.9400021 C25.172922,25.8766752 24.8881757,27.1105759 23.9515026,27.6959966 C23.6336394,27.8946611 23.2663442,28 22.8915047,28 L9.10849528,28 C8.00392578,28 7.10849528,27.1045695 7.10849528,26 C7.10849528,25.6251606 7.21383417,25.2578653 7.41249868,24.9400021 L13,20 L13,14 L19,14 L19,16.5 L22,16.5" id="路径" stroke="#333333" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M22.5,5 L17,9 L17,18 L15,16 L15,9 L9.5,5 L22.5,5 Z" id="形状结合" fill="#027AFF"></path>
                            <path d="M22.5,0 L22.5,5 L17,9 L17,18 M15,16 L15,9 L9.5,5 L9.5,0" id="形状" stroke="#333333" stroke-linecap="round" stroke-linejoin="round"></path>
                            <polyline id="路径-27" stroke="#027AFF" stroke-linecap="round" stroke-linejoin="round" points="0 17 9 10.5 11.5 10.5 12.5 12 20 12"></polyline>
                            <polyline id="路径-28" stroke="#027AFF" stroke-linecap="round" stroke-linejoin="round" points="3.5 20.5 9.5 12 11 12 13 10.5 20 10.5"></polyline>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>