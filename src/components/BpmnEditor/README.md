# labwise editor

## About

This example is a node-style web application that builds a user interface around the bpmn-js BPMN 2.0 modeler. ![](./assets/images/screenshot.png 'Screenshot of the modeler + properties panel example') ![](./assets/images/Schedulor.jpg 'Custom Schedulor')

## bpmn layout

![](./assets/images/layout-guide.png 'bpmn布局')

### JSON schema

- [rjsf-team playground](https://rjsf-team.github.io/react-jsonschema-form/)
- [x-render playground](https://xrender.fun/playground)
