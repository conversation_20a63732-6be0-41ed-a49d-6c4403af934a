import TokenSimulationModule from 'bpmn-js-token-simulation'
import BpmnModeler from 'bpmn-js/lib/Modeler'
import type { ViewerOptions } from 'diagram-js/lib/model'

import {
  ACTIVITI_PREFIX,
  CAMUNDA_PREFIX,
  FLOWABLE_PREFIX
} from '@/components/BpmnEditor/bpmn/constant/bpmn'
import activitiDescriptor from '@/components/BpmnEditor/bpmn/descriptor/activiti.json'
import camundaDescriptor from '@/components/BpmnEditor/bpmn/descriptor/camunda.json'
import MiyueModdleDescriptors from '@/components/BpmnEditor/bpmn/descriptor/custom.json'
import EnhancementRenderer from '@/components/BpmnEditor/bpmn/Renderer/EnhancementRenderer'
// 引入属性解析文件和对应的解析器
import flowableDescriptor from '@/components/BpmnEditor/bpmn/descriptor/flowable.json'
import { activitiExtension } from '@/components/BpmnEditor/bpmn/moddle/activiti'
import EnhancementPalette from '@/components/BpmnEditor/bpmn/moddle/EnhancementPalette'
import { flowableExtension } from '@/components/BpmnEditor/bpmn/moddle/flowable'
import customTranslate from '@/components/BpmnEditor/bpmn/translate/customTranslate.js'
import translationsCN from '@/components/BpmnEditor/bpmn/translate/zh.js'
import Logger from '@/utils/Logger'

export function initBpmnModeler(bpmnPrefix) {
  function getAdditionalModules() {
    console.log('【初始化建模器】2、添加解析器')
    const modules: Array<any> = []
    if (bpmnPrefix === FLOWABLE_PREFIX) {
      modules.push(flowableExtension)
    }
    if (bpmnPrefix === CAMUNDA_PREFIX) {
      modules.push(EnhancementPalette)
      modules.push(EnhancementRenderer)
      // modules.push(camundaExtension)  // TODO add ORIGIN_CAMUNDA_PREFIX
    }
    if (bpmnPrefix === ACTIVITI_PREFIX) modules.push(activitiExtension)
    const TranslateModule = {
      // translate: ["value", customTranslate(translations || translationsCN)] translations是自定义的翻译文件
      translate: ['value', customTranslate(translationsCN)]
    }
    modules.push(TranslateModule) // 添加模拟流转模块
    modules.push(TokenSimulationModule)
    return modules
  }

  function getModdleExtensions() {
    console.log('【初始化建模器】3、添加解析文件')
    const extensions: any = {}
    if (bpmnPrefix === FLOWABLE_PREFIX) extensions.flowable = flowableDescriptor
    if (bpmnPrefix === CAMUNDA_PREFIX) {
      extensions.camunda = camundaDescriptor
      extensions['custom'] = MiyueModdleDescriptors
    }
    if (bpmnPrefix === ACTIVITI_PREFIX) extensions.activiti = activitiDescriptor
    // Logger.prettyError('---extensions---', extensions)
    return extensions
  }
  Logger.prettyInfo('【初始化建模器】4、初始化建模器结束')

  console.log('【初始化建模器】1、初始化建模器开始')
  const options: ViewerOptions<Element> = {
    container: '#canvas',
    height: '100vh',
    additionalModules: getAdditionalModules(),
    moddleExtensions: getModdleExtensions()
  }

  try {
    const modeler = new BpmnModeler(options)
    return modeler
  } catch (error) {
    console.error('render error', error)
  }
}
