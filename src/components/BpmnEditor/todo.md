```tsx
/**
 * TODO 渲染 对齐按钮组
 */
function renderAlignControlButtons() {
  const [open, setOpen] = useState(false)
  const [align, setAlign] = useState<
    'left' | 'right' | 'top' | 'bottom' | 'center' | 'middle'
  >('left')

  function handleOpen(
    align: 'left' | 'right' | 'top' | 'bottom' | 'center' | 'middle'
  ) {
    const Selection = bpmnModeler.get('selection')
    const SelectedElements = Selection.get()
    if (!SelectedElements || SelectedElements.length <= 1) {
      message.warning('请按住 Shift 键选择多个元素对齐').then(() => {})
      return
    }
    setAlign(align)
    setOpen(true)
  }

  function handleElAlign() {
    const Align = bpmnModeler.get('alignElements')
    const Selection = bpmnModeler.get('selection')
    const SelectedElements = Selection.get()
    if (!SelectedElements || SelectedElements.length <= 1) {
      message.warning('请按住 Ctrl 键选择多个元素对齐').then(() => {})
      return
    }
    Align.trigger(SelectedElements, align)
    setOpen(false)
  }

  return (
    <>
      <Modal
        title="确认对齐"
        okText={'确认'}
        cancelText={'取消'}
        open={open}
        onOk={() => handleElAlign()}
        onCancel={() => setOpen(false)}
      >
        <p>{'自动对齐可能造成图形变形,是否继续?'}</p>
      </Modal>
      <Tooltip title="向左对齐">
        <Button
          type={'default'}
          size="small"
          style={{ width: '45px' }}
          icon={<AlignLeftOutlined />}
          onClick={() => handleOpen('left')}
        />
      </Tooltip>
      <Tooltip title="向右对齐">
        <Button
          type={'default'}
          size="small"
          style={{ width: '45px' }}
          icon={<AlignRightOutlined />}
          onClick={() => handleOpen('right')}
        />
      </Tooltip>
      <Tooltip title="向上对齐">
        <Button
          type={'default'}
          size="small"
          style={{ width: '45px' }}
          icon={<VerticalAlignTopOutlined />}
          onClick={() => handleOpen('top')}
        />
      </Tooltip>
      <Tooltip title="向下对齐">
        <Button
          type={'default'}
          size="small"
          style={{ width: '45px' }}
          icon={<VerticalAlignBottomOutlined />}
          onClick={() => handleOpen('bottom')}
        />
      </Tooltip>
      <Tooltip title="水平居中">
        <Button
          type={'default'}
          size="small"
          style={{ width: '45px' }}
          icon={<AlignCenterOutlined />}
          onClick={() => handleOpen('center')}
        />
      </Tooltip>
      <Tooltip title="垂直居中">
        <Button
          type={'default'}
          size="small"
          style={{ width: '45px' }}
          icon={<VerticalAlignMiddleOutlined />}
          onClick={() => handleOpen('middle')}
        />
      </Tooltip>
    </>
  )
}

/**
 * TODO 渲染模拟流转按钮
 */
function renderSimulationButton() {
  function handleSimulation() {
    bpmnModeler.get('toggleMode').toggleMode()
    setSimulationStatus(!simulationStatus)
  }

  return (
    <>
      <Button type="primary" size="small" onClick={() => handleSimulation()}>
        <Space>
          <BugOutlined />
          {simulationStatus ? '退出' : '模拟'}
        </Space>
      </Button>
    </>
  )
}
```
