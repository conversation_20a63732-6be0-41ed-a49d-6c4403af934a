import type { ITask } from './../index.d'

let langType: 'zh-CN' | 'en-US' =
  localStorage.getItem('umi_locale') || window.navigator.language
export const isEN = () => langType === 'en-US'
export function getLocaleTag() {
  return isEN() ? 'en' : 'zh-CN'
}

export function getBpmnTaskText(key: string) {
  let bpmnTaskList = JSON.parse(
    sessionStorage.getItem('bpmnTaskList') as string
  ) as ITask[]
  let title = bpmnTaskList.find((e) => e.key === key)?.iconName
  return title
}
