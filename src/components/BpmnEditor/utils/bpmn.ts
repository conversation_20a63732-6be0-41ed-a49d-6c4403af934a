import { v4 as uuidv4 } from 'uuid'

export function getTaskId(businessObject: string) {
  let taskID = businessObject?.id ? businessObject.id.split('_')[1] : uuidv4()
  return taskID
}

/**
 * 根据所需类型进行转码并返回下载地址
 * @param type
 * @param filename
 * @param data
 */
export function setEncoded(
  type: string,
  filename = 'diagram', // processId ||
  data: XMLDocument
) {
  const encodedData = encodeURIComponent(data)
  return {
    filename: `${filename}.${type}`,
    href: `data:application/${
      type === 'svg' ? 'text/xml' : 'bpmn20-xml'
    };charset=UTF-8,${encodedData}`,
    data: data
  }
}

/**
 * 文件下载方法
 * @param href
 * @param filename
 */
export function downloadFunc(href: string, filename: string) {
  if (href && filename) {
    const a = document.createElement('a')
    document.body.appendChild(a) // 将 <a> 元素添加到页面上
    a.download = filename //指定下载的文件名
    a.href = href //  URL对象
    a.click() // 模拟点击
    URL.revokeObjectURL(a.href) // 释放URL 对象
    document.body.removeChild(a) // 下载完成后移除 <a> 元素
  }
}

/**
 * 下载流程图
 * @param type
 * @param name
 */
export async function downloadProcess(
  type: string,
  bpmnModeler: any,
  name?: string
) {
  try {
    // 按需要类型创建文件并下载
    if (type === 'xml' || type === 'bpmn') {
      const { err, xml } = await bpmnModeler.saveXML()
      // 读取异常时抛出异常
      if (err) {
        console.error(`【下载流程图出错】: ${err.message || err}`)
      }
      let { href, filename } = setEncoded(type.toUpperCase(), name, xml)
      downloadFunc(href, filename)
    } else {
      const { err, svg } = await bpmnModeler.saveSVG()
      // 读取异常时抛出异常
      if (err) {
        return console.error(err)
      }
      let { href, filename } = setEncoded('SVG', name, svg)
      downloadFunc(href, filename)
    }
  } catch (e: any) {
    console.error(`【下载流程图出错】: ${e.message || e}`)
  }
}

// export function setEncoded(link: Document, name: string, data: XMLDocument) {
//   // 把xml转换为URI，下载要用到的
//   var encodedData = encodeURIComponent(data)
//   if (data) {
//     link.className = 'active'
//     link.href = 'data:application/bpmn20-xml;charset=UTF-8,' + encodedData
//     link.download = name
//   } else {
//     /* TODO use react to [remove class](https://stackoverflow.com/questions/49014937/addclass-and-removeclass-react) */
//     // link.removeClass('active')
//   }
// }

// 流程校验使用
export const getUrlParam = (name: string) => {
  const url = new URL(window.location.href)
  return url.searchParams.has(name)
}

// 流程校验使用
export const setUrlParam = (name: string, value) => {
  const url = new URL(window.location.href)
  if (value) url.searchParams.set(name, 1)
  else url.searchParams.delete(name)
  window.history.replaceState({}, null, url.href)
}

/**
 * 下载xml/svg
 *  @param  type  类型  svg / xml
 *  @param  data  数据
 *  @param  name  文件名称
 */
export const download = (type, data, name) => {
  let dataTrack = ''
  const a = document.createElement('a')

  switch (type) {
    case 'xml':
      dataTrack = 'bpmn'
      break
    case 'svg':
      dataTrack = 'svg'
      break
    default:
      break
  }

  a.setAttribute(
    'href',
    `data:application/bpmn20-xml;charset=UTF-8,${encodeURIComponent(data)}`
  )
  a.setAttribute('target', '_blank')
  a.setAttribute('dataTrack', `diagram:download-${dataTrack}`)
  a.setAttribute('download', name || `diagram.${dataTrack}`)

  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}
