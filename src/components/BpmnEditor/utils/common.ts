/* 修改数组指定key */
export const changeKeyObjects = (arr, replaceKeys) => {
  let newKeys = arr
  newKeys.map((item, index) => {
    arr[index] = Object.keys(item).reduce(
      (acc, key) => ({
        ...acc,
        ...{ [replaceKeys[key] || key]: item[key] }
      }),
      {}
    )
  })
  return newKeys
}

// Form.Item基本样式配置 ->可以模块化到common文件中
let date = new Date() // date.getFullYear() + '-' + month + '-' + Day + '-' + Hour + '-' + Min
let month = date.getMonth() + 1
let Day = date.getDate()
let Hour = date.getHours()
let Min = date.getMinutes()
const CurrentMonth = date.getFullYear() + '-' + month
const YearStart = date.getFullYear() + '-1'
const YearEnd = date.getFullYear() + '-12'
const SixDayBefore = new Date(date.getTime() - 24 * 60 * 60 * 6000) // date.getFullYear() + '-' + month + '-' + LogDay + '-' + Hour + '-' + Min
const CurrentDetailTime = date

export {
  SixDayBefore,
  date,
  CurrentMonth,
  YearStart,
  YearEnd,
  CurrentDetailTime
}

// 针对整数金额，每3位用‘,’分隔
export function segmentation(parameter) {
  let num = ''
  if (Number(parameter).toString().length > 3) {
    num = Number(parameter)
      .toString()
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
    return num
  } else {
    return parameter
  }
}

// 字符串去空格操作
export function deleteSpace(str: string) {
  return str.replace(/\s/g, '')
}

export function reloadEvent() {
  window.location.reload()
}
