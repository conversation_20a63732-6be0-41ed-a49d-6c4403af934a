export interface ITask {
  iconName: string
  iconUrl: string
  key: string
}

export interface ExtraToolsProps {
  title: string
  event: () => void
  img: HTMLImageElement
  type?: 'save' | null
}

export interface IPanelDatas {
  experimentDesignNo: string
  workflow: string
  readOnly?: boolean
}

export interface ProcessDesignerProps {
  extraTools?: ExtraToolsProps[]
  experimentalNo?: string
  panelDatas: IPanelDatas
  operationProcess: TaskInfo[] | undefined
  taskList: any[]
}
