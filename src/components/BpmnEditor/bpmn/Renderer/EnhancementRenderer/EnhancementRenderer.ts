/* 左侧任务栏 */
import BpmnRenderer from 'bpmn-js/lib/draw/BpmnRenderer'
import PathMap from 'bpmn-js/lib/draw/PathMap'
import TextRenderer from 'bpmn-js/lib/draw/TextRenderer'
import Canvas from 'diagram-js/lib/core/Canvas'
import EventBus from 'diagram-js/lib/core/EventBus'
import Styles from 'diagram-js/lib/draw/Styles'
import Text from 'diagram-js/lib/util/Text'
import {
  append as svgAppend,
  attr as svgAttr,
  create as svgCreate
} from 'tiny-svg'
import { getBpmnTaskText } from '../../../utils'
import { CUSTOM_TASK_LIST } from '../../constant/bpmn'

import renderEventContent from '@/components/BpmnEditor/bpmn/Renderer/EnhancementRenderer/renderEventContent'
import { drawCircle } from '@/components/BpmnEditor/bpmn/Renderer/utils'

class EnhancementRenderer extends BpmnRenderer {
  _styles: Styles
  constructor(
    config: any,
    eventBus: EventBus,
    styles: Styles,
    pathMap: PathMap,
    canvas: Canvas,
    textRenderer: TextRenderer
  ) {
    super(config, eventBus, styles, pathMap, canvas, textRenderer, 3000)
    this._styles = styles

    // 这里执行重绘
    this.handlers['bpmn:Event'] = (parentGfx, element, attrs) => {
      if (!attrs || !attrs['fillOpacity']) {
        !attrs && (attrs = {})
        attrs['fillOpacity'] = 1
        attrs['fill'] = '#1bbc9d'
        attrs['strokeWidth'] = 0
      }
      return drawCircle(this, parentGfx, element.width, element.height, attrs)
    }

    this.handlers['bpmn:EndEvent'] = (parentGfx, element) => {
      const circle = this.handlers['bpmn:Event'](parentGfx, element, {
        fillOpacity: 1,
        strokeWidth: 2,
        fill: '#e98885',
        stroke: '#000000'
      })
      renderEventContent(this.handlers, element, parentGfx)
      return circle
    }

    var defaultStyle = {
      fontSize: 14,
      fill: '#5d87e8',
      lineHeight: 15
    }
    var defaultSize = {
      width: 100,
      height: 80
    }
    var customText = new Text({
      size: defaultSize,
      style: defaultStyle,
      align: 'center-middle'
    })

    /**
     * 导入或生成workflow时展示自定义icon
     */
    const reDrawerTaskIcon = (taskTypeList: string[]) => {
      taskTypeList.map((key) => {
        this.handlers[key] = (parentGfx, element, attr) => {
          let imgName = element?.businessObject.id.split('_')[0]
          const customIcon = svgCreate('image')
          svgAttr(customIcon, {
            ...(attr || {}),
            width: element.width,
            height: element.height,
            href: require(`@/components/BpmnEditor/assets/images/${imgName}.png`) // element?.businessObject?.name  TODO better set default icon
          })
          svgAppend(parentGfx, customIcon)
          const text = customText.createText(getBpmnTaskText(imgName), element)
          svgAppend(parentGfx, text)
          return customIcon
        }
      })
    }
    reDrawerTaskIcon(['bpmn:ServiceTask', 'bpmn:CallActivity'])

    const drawCustomPath = (key, imgName) => {
      this.handlers[key] = (parentGfx, element, attr) => {
        const customIcon = svgCreate('image')
        svgAttr(customIcon, {
          ...(attr || {}),
          width: element.width,
          height: element.height,
          href: require(`@/components/BpmnEditor/assets/images/${imgName}.png`)
        })
        svgAppend(parentGfx, customIcon)
        if (
          !['end-task', `start-task`].includes(imgName) &&
          getBpmnTaskText(imgName)
        ) {
          let text = customText.createText(getBpmnTaskText(imgName), element)
          svgAppend(parentGfx, text)
        }
        return customIcon
      }
    }

    /* TODO next $type值重构，由custom:改为 CallActivity 、ServiceTask
    主线节点：CallActivity
    子节点：ServiceTask */

    // cover start&&end task icon
    drawCustomPath('bpmn:EndEvent', `end-task`)
    drawCustomPath('bpmn:StartEvent', `start-task`)
    // 自定义节点的绘制
    CUSTOM_TASK_LIST.map((e) => {
      drawCustomPath(e, e.split(':')[1]) // e.g custom:Ctystal -> Ctystal
    })
  }
}

EnhancementRenderer.$inject = [
  'config.bpmnRenderer',
  'eventBus',
  'styles',
  'pathMap',
  'canvas',
  'textRenderer'
]

export default EnhancementRenderer
