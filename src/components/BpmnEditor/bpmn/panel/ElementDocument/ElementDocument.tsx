import { getWord } from '@/utils'
import { Form, Input } from 'antd'
import type { ChangeEventHandler } from 'react'
import { useEffect } from 'react'
interface IProps {
  businessObject: any
}

/**
 * 元素文档 组件
 */
export default function ElementDocument(props: IProps) {
  const { businessObject } = props

  const [form] = Form.useForm<{
    documentation: string
  }>()

  function initPageData() {
    form.setFieldsValue({
      documentation: businessObject?.documentation?.at(0).text
    })
  }

  useEffect(() => {
    if (businessObject) {
      initPageData()
    }
  }, [businessObject?.id])

  /**
   * 更新元素文档
   *
   * @param value
   */
  function updateDocumentation(value: string) {
    const documentation = window.bpmnInstance.bpmnFactory?.create(
      'bpmn:Documentation',
      {
        text: value
      }
    )
    window.bpmnInstance.modeling.updateProperties(window.bpmnInstance.element, {
      documentation: value ? [documentation] : undefined
    })
  }

  const handleChange: ChangeEventHandler<HTMLTextAreaElement> = (event) =>
    updateDocumentation(event.currentTarget.value)

  return (
    <>
      <Form
        form={form}
        labelCol={{ span: 5 }}
        wrapperCol={{ span: '100%' }}
        style={{ marginBottom: 120 }}
      >
        <Form.Item name="documentation">
          <Input.TextArea
            rows={6}
            placeholder={getWord('input-tip')}
            onChange={handleChange}
          />
        </Form.Item>
      </Form>
    </>
  )
}
