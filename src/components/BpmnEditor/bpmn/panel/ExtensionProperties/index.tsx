import { extractPropertiesExtension } from '@/components/BpmnEditor/bpmn/util/panelUtil'
import { apiGetMixDetail, parseResponseResult } from '@/services'
import { ExperimentDesignGetMixDetailResponse } from '@/types/models'
// import Logger from '@/utils/Logger'
import { Empty } from 'antd'
import cs from 'classnames'
import FormSlimRender, { useForm } from 'form-render'
import { useEffect, useState } from 'react'
import { useModel, useSelector } from 'umi'
import { getTaskId } from '../../../utils'
import EditMix from './EditMix'
import type { ExtensionPropertiesProps } from './index.d'
import styles from './index.less'
/**
 * 扩展属性 组件
 */
export default function ExtensionProperties(props: ExtensionPropertiesProps) {
  const { businessObject } = props
  const { taskInfo, getTaskInfo } = useModel('task')
  const { saveTaskInfo } = useModel('newBpmn')
  const [startEditorMode, setStartEditorMode] = useState<boolean>(false)
  const [mixData, setMixData] = useState<ExperimentDesignGetMixDetailResponse>()
  const [curSchema, setCurSchema] = useState(null)
  const bpmnState = useSelector((state) => state?.bpmn)
  const prefix = bpmnState?.prefix
  useEffect(() => {
    if (businessObject) {
      /* Note init Panel Data */
      let properties: any[] = extractPropertiesExtension(prefix) // 获取扩展属性
      console.log('---get init Panel properties---', properties)
    }
  }, [businessObject?.id])

  const task = businessObject?.taskType || businessObject?.name
  const taskNo = `${task}_${getTaskId(businessObject)}`

  const onFinish = (formData: string) => {
    if (task)
      saveTaskInfo({
        task_no: taskNo,
        data: formData,
        experiment_design_no: props?.panelDatas?.experimentDesignNo
      }).then(() => {
        console.log('---businessObject---',businessObject)
        getTaskInfo(businessObject?.id)
      })
  }
  const form = useForm()

  useEffect(() => {
    // const schema = formatSchemaForm(taskInfo?.task_schema)
    // Logger.prettyError('---schema---', taskInfo?.task_schema)
    setCurSchema(taskInfo?.task_schema) // schema
    const taskName =
      businessObject?.taskType || businessObject?.id?.split('_')[0]
    if (form && taskName) {
      form.setValues({
        ...taskInfo?.data,
        task_name: taskName
      })
    }
  }, [taskInfo])

  const getMixDetail = async () => {
    const res = await apiGetMixDetail({
      routeParams: businessObject?.id
    })
    if (parseResponseResult(res).ok) {
      setMixData(res?.data)
      console.log('---mix res?.data---', res?.data)
      setStartEditorMode(true)
    }
  }

  const confirmMixData = async (newformData: string) => {
    await onFinish(newformData)
    setStartEditorMode(false)
  }

  return (
    <div className={styles.extension}>
      <EditMix
        visible={startEditorMode}
        handleCancel={() => setStartEditorMode(false)}
        mixData={mixData}
        confirmMixData={confirmMixData}
      />
      {/* ===  || businessObject?.name === 'MakeIngredients'  */}
      {['Mix'].includes(task) ? (
        <span
          className={cs(styles.editorMode, 'enablePointer')}
          onClick={getMixDetail}
        >
          编辑
        </span>
      ) : (
        ''
      )}
      {curSchema ? (
        <FormSlimRender
          form={form}
          schema={curSchema}
          labelWidth={100}
          onFinish={(_formData: string) => onFinish(_formData)}
          footer={props?.panelDatas?.readOnly ? false : true}
        />
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />
      )}
    </div>
  )
}
