import MaterialsTable from '@/components/MaterialsTable'
import ProcedureText from '@/components/ProcedureText'
import { Button, Modal } from 'antd'
import FormSlimRender, { useForm } from 'form-render'
import { useEffect } from 'react'
import type { EditMixProps } from './index.d'
import styles from './index.less'
export default function EditMix({
  handleCancel,
  visible,
  mixData,
  confirmMixData
}: EditMixProps) {
  const form = useForm()
  const confirm = async () => {
    const formData = await form.getValues()
    confirmMixData(formData)
  }
  useEffect(() => {
    if (visible) {
      form.setValues({
        ...mixData?.data
      })
    }
  }, [visible])

  return (
    <>
      <Modal
        title="编辑加料参数"
        open={visible}
        centered
        footer={
          <>
            <Button onClick={handleCancel}>取消</Button>
            <Button type="primary" onClick={confirm}>
              确认
            </Button>
          </>
        }
        width="90%"
        onCancel={handleCancel}
      >
        <div className={styles.editMix}>
          <div className={styles.staticContent}>
            {mixData?.reference_text && (
              <ProcedureText
                rows={6}
                procedure={{ text: mixData?.reference_text }}
              />
            )}
            <MaterialsTable material_table={mixData?.material_table} />
          </div>
          <div className={styles.editContent}>
            <FormSlimRender
              form={form}
              schema={mixData?.data_schema}
              labelWidth={100}
            />
          </div>
        </div>
      </Modal>
    </>
  )
}
