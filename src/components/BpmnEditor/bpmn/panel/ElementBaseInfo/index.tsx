import { Form, message, Switch } from 'antd'
import { FormSlimRender, Input, Select, TextArea, useForm } from 'form-render'
import type { ChangeEvent } from 'react'
import { useEffect } from 'react'
import { useDispatch, useSelector } from 'umi'

import { taskIdGenerator } from '@/components/BpmnEditor/bpmn/util/idUtil'

import { getWord } from '@/utils'
import type { ElementBaseInfoProps } from './index.d'
import schema from './schema/basic'

const keyOptions = {
  id: 'id',
  name: 'name',
  isExecutable: 'isExecutable',
  versionTag: 'versionTag'
}

/**
 * 常规信息 组件
 */
export default function ElementBaseInfo(props: ElementBaseInfoProps) {
  const taskState = useSelector((state) => state?.task)
  const { businessObject } = props
  const dispatch = useDispatch()
  /* 每个节点的id是必不相同的，切换当前节点才重新执行初始化操作，当前节点属性变化时,不需要重新初始化操作 */
  useEffect(() => {
    // console.log('---businessObject---', businessObject)
    if (businessObject) initPageData()
  }, [businessObject?.id])
  const form = useForm()

  function initPageData() {
    // Logger.prettyError('---taskInfo---', taskState?.taskInfo)
    // Logger.prettyError('---businessObject?.id---', businessObject?.id)
    /* TODO 处理schema数据进行Form渲染 */
    /* FIXME 检查唯一id的生成逻辑、自定义id编号 */
    const getID = () => {
      if (businessObject?.id) {
        return businessObject?.id
      } else if (businessObject?.taskType) {
        return taskIdGenerator(businessObject?.taskType)
      } else {
        return undefined
      }
    }
    if (form) {
      form.setValues({
        id: getID(),
        name: businessObject?.name,
        isExecutable: businessObject?.isExecutable || false,
        versionTag: businessObject?.versionTag
      })
    }
  }

  useEffect(() => {
    if (props?.panelDatas) form.setValues({ ...props?.panelDatas })
  }, [props?.panelDatas])

  /**
   * 更新常规信息
   */
  function updateElementAttr(key: string, value: any) {
    if (key === keyOptions.id) {
      // id校验, 这里做一次校验是因为输入框监听的是change事件,输入框自带的校验无法拦截到,因此要在这里处理一下,防止将非法值更新到流程中
      const { status: validateFlag } = validateId(value)
      if (!validateFlag) {
        return
      } else {
        try {
          window.bpmnInstance.elementRegistry._validateId(value)
        } catch (e: any) {
          message
            .error(getWord('ID-exists-tip'))
            .then(() => console.log(e.message))
          return
        }
      }
      // 更新id
      window.bpmnInstance.modeling.updateProperties(
        window.bpmnInstance.element,
        {
          id: value,
          di: { id: `${businessObject[key]}_di` }
        }
      )
    } else {
      // 更新其他属性
      window.bpmnInstance.modeling.updateProperties(
        window.bpmnInstance.element,
        {
          [key]: value || undefined
        }
      )
    }
    // 如果当前是process节点,则更新redux中的processId和processName
    if (businessObject.$type === 'bpmn:Process') {
      if (key === keyOptions.id)
        dispatch({
          type: 'bpmn/handleProcessId',
          payload: value
        })
      else if (key === keyOptions.name)
        dispatch({
          type: 'bpmn/handleProcessName',
          payload: value
        })
    }
  }

  /**
   * 校验id
   * @param value
   */
  function validateId(value: string) {
    if (!value) {
      return {
        status: false,
        message: getWord('ID-empty-tip')
      }
    } else if (value.includes(' ')) {
      return {
        status: false,
        message: getWord('ID-contains-spaces')
      }
    } else {
      return {
        status: true,
        message: 'ok'
      }
    }
  }

  /**
   * TODO future 渲染Process节点独有组件 (版本标签、是否可执行)
   */
  function renderProcessExtension() {
    if (businessObject?.$type === 'bpmn:Process') {
      return (
        <>
          <Form.Item label="版本标签" name="versionTag">
            <Input
              placeholder="请输入"
              onChange={(event: ChangeEvent<HTMLInputElement>) => {
                updateElementAttr(
                  keyOptions.versionTag,
                  event.currentTarget.value
                )
              }}
            />
          </Form.Item>
          <Form.Item label="可执行" name="isExecutable" valuePropName="checked">
            <Switch
              checkedChildren="开"
              unCheckedChildren="关"
              onChange={(checked) => {
                updateElementAttr(keyOptions.isExecutable, checked)
              }}
            />
          </Form.Item>
        </>
      )
    }
  }

  return (
    <FormSlimRender
      form={form}
      schema={schema}
      widgets={{ Input, Select, TextArea }}
      labelCol={8}
      fieldCol={16}
    />
  )

  // return (
  //   <Form form={form} labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
  //     <Form.Item
  //       label="编号"
  //       name="id"
  //       required
  //       rules={[
  //         {
  //           validator: (_, value) => {
  //             const validateId$1 = validateId(value)
  //             return validateId$1.status
  //               ? Promise.resolve()
  //               : Promise.reject(new Error(validateId$1.message))
  //           }
  //         }
  //       ]}
  //     >
  //       <Input.TextArea
  //         placeholder="请输入"
  //         autoSize={{ minRows: 2, maxRows: 4 }}
  //         onChange={(event) => {
  //           console.log(
  //             '---event.currentTarget.value---',
  //             event.currentTarget.value
  //           )
  //           updateElementAttr(keyOptions.id, event.currentTarget.value)
  //         }}
  //       />
  //     </Form.Item>
  //     <Form.Item label="名称" name="name">
  //       <Input
  //         placeholder="请输入"
  //         onChange={(event) => {
  //           updateElementAttr(keyOptions.name, event.currentTarget.value)
  //         }}
  //         disabled
  //       />
  //     </Form.Item>
  //     {renderProcessExtension()}
  //   </Form>
  // )
}
