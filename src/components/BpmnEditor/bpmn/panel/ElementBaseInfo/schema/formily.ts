export const schema = {
  type: 'object',
  properties: {
    visible_destructor: {
      type: 'boolean',
      title: 'Whether to display deconstructed fields',
      default: true,
      enum: [
        { label: 'yes', value: true },
        { label: 'no', value: false }
      ],
      'x-decorator': 'FormItem',
      'x-component': 'Radio.Group'
    },
    undestructor: {
      type: 'string',
      title: 'before deconstruction',
      'x-decorator': 'FormItem',
      'x-component': 'DatePicker.RangePicker'
    },
    '[startDate,endDate]': {
      type: 'string',
      title: 'after deconstruction',
      default: ['2020-11-20', '2021-12-30'],
      'x-decorator': 'FormItem',
      'x-component': 'DatePicker.RangePicker',
      'x-reactions': {
        dependencies: ['visible_destructor'],
        fulfill: {
          state: {
            visible: '{{!!$deps[0]}}'
          }
        }
      }
    }
  }
}
