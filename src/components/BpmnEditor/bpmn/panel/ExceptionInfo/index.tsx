import { reasonTypeDes } from '@/constants'
import { formatYTSTime } from '@/utils'
import { Form, Input } from 'antd'
import cs from 'classnames'
import type { Dayjs } from 'dayjs'
import { useModel } from 'umi'
import { isValidArray } from '../../../../../utils/detect'
import styles from './index.less'
interface Task_Exception {
  exception_code: string
  exception_name: string
  exception_type: string | null
  exception_time: Dayjs | Date
  reason_type: 'op_exception' | 'optimize' | 'continue_last'
  reason: string
}
interface ExceptionData {
  exception_id: number
  task_exception: Task_Exception
}

export default function ExceptionInfo() {
  const { exceptionData } = useModel('exception')
  return (
    <div className={cs(styles.exceptionInfo)}>
      {isValidArray(exceptionData)
        ? exceptionData?.map((item: ExceptionData, index: number) => {
            return (
              <Form
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                key={`${item?.task_exception?.exception_code}-${index}`}
                className={cs(styles.exceptionInfoContent, {
                  mt20: index !== 0
                })}
              >
                <Form.Item label="异常处理方式">
                  <Input
                    disabled
                    value={item?.task_exception?.exception_name || '-'}
                  />
                </Form.Item>
                <Form.Item label="上报异常的时间">
                  <Input
                    disabled
                    value={
                      item?.task_exception?.exception_time
                        ? formatYTSTime(item?.task_exception?.exception_time)
                        : '-'
                    }
                  />
                </Form.Item>
                <Form.Item label="原因类型">
                  <Input
                    disabled
                    value={
                      item?.task_exception?.reason_type
                        ? reasonTypeDes[item?.task_exception?.reason_type]
                        : '-'
                    }
                  />
                </Form.Item>
                <Form.Item label="原因">
                  <Input.TextArea
                    value={item?.task_exception?.reason || '-'}
                    disabled
                  />
                </Form.Item>
              </Form>
            )
          })
        : ''}
    </div>
  )
}
