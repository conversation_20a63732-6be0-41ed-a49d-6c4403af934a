import type { ITask } from '@/components/BpmnEditor/index.d'
import { getBpmnTaskText } from '@/components/BpmnEditor/utils'
import { getWord } from '@/utils'
import ElementFactory from 'bpmn-js/lib/features/modeling/ElementFactory'
import PaletteProvider from 'bpmn-js/lib/features/palette/PaletteProvider'
import Create from 'diagram-js/lib/features/create/Create'
import GlobalConnect from 'diagram-js/lib/features/global-connect/GlobalConnect'
import HandTool from 'diagram-js/lib/features/hand-tool/HandTool'
import LassoTool from 'diagram-js/lib/features/lasso-tool/LassoTool'
import Palette from 'diagram-js/lib/features/palette/Palette'
import SpaceTool from 'diagram-js/lib/features/space-tool/SpaceTool'
import { assign } from 'min-dash'

class EnhancementPaletteProvider extends PaletteProvider {
  private readonly _palette: Palette
  private readonly _create: Create
  private readonly _elementFactory: ElementFactory
  private readonly _spaceTool: SpaceTool
  private readonly _lassoTool: LassoTool
  private readonly _handTool: HandTool
  private readonly _globalConnect: GlobalConnect
  private readonly _translate: any
  constructor(
    palette,
    create,
    elementFactory,
    spaceTool,
    lassoTool,
    handTool,
    globalConnect,
    translate
  ) {
    super(
      palette,
      create,
      elementFactory,
      spaceTool,
      lassoTool,
      handTool,
      globalConnect,
      translate,
      2000
    )
    this._palette = palette
    this._create = create
    this._elementFactory = elementFactory
    this._spaceTool = spaceTool
    this._lassoTool = lassoTool
    this._handTool = handTool
    this._globalConnect = globalConnect
    this._translate = translate
  }

  getPaletteEntries() {
    const actions = {},
      create = this._create,
      elementFactory = this._elementFactory,
      translate = this._translate

    function createAction(
      type: string,
      group: string,
      className: string,
      mark: string,
      options?: any
    ) {
      const createListener = (event) => {
        const shape = elementFactory.createShape(
          assign(
            {
              type: type.startsWith('bpmn:') ? type : 'bpmn:CallActivity'
            },
            options
          )
        )
        /* NOTE process节点插入name并修改id */
        shape.businessObject.name = mark
        if (shape.businessObject.id.startsWith('Activity_')) {
          shape.businessObject.id = shape.businessObject.id.replace(
            /^Activity_/,
            `${mark}_`
          )
        }
        if (options) {
          !shape.businessObject.di && (shape.businessObject.di = {})
          shape.businessObject.di.isExpanded = (
            options as { [key: string]: any }
          ).isExpanded
        }
        create.start(event, shape)
      }
      let curTitle = getBpmnTaskText(mark)
      return {
        group: group,
        className: className,
        // html: `<div class="word">${getWord(mark)}</div>`,
        title: curTitle ? curTitle : getWord(mark), // || translate('Create {type}', { type: type.replace(/^bpmn:/, '') })
        action: {
          dragstart: createListener,
          click: createListener
        }
      }
    }

    let bpmnTaskList = JSON.parse(
      sessionStorage.getItem('bpmnTaskList') as string
    ) as ITask[]
    let createSvgs = {
      'create.start-event': createAction(
        'bpmn:StartEvent',
        'event',
        'icon-custom start-task',
        'STARTEVENT'
      ),
      'create.end-event': createAction(
        'bpmn:EndEvent',
        'event',
        'icon-custom end-task',
        'ENDEVENT'
      ),
      'task-separator': {
        group: 'activity',
        separator: true
      }
    }
    for (const iterator of bpmnTaskList) {
      let value = createAction(
        `custom:${iterator.key}`,
        'activity',
        `custom-task-${iterator.key}`,
        `${iterator.key}`
      )
      createSvgs[`create.${iterator.key}`] = value
    }
    assign(actions, createSvgs)
    return actions
  }
}

EnhancementPaletteProvider.$inject = [
  'palette',
  'create',
  'elementFactory',
  'spaceTool',
  'lassoTool',
  'handTool',
  'globalConnect',
  'translate'
]

export default EnhancementPaletteProvider
