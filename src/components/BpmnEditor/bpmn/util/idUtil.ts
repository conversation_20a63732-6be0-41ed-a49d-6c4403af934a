import { v4 as uuidv4 } from 'uuid'

/**
 * 手动生成唯一id
 * @param prefix 前缀
 * @param length 长度，默认8
 */
export function UUIdGenerator(prefix: string, length: number = 8) {
  let id: string = prefix ? prefix + '_' : ''
  let chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
  for (let i = length; i > 0; --i) {
    id += chars[Math.floor(Math.random() * chars.length)]
  }
  return id
}

/**
 * 根据当前时间生成唯一id，返回格式如：process_1667396671442 (前缀为process)
 * @param prefix 前缀
 */
export function TimeIdGenerator(prefix: string) {
  return `${prefix}_${new Date().getTime()}`
}

export function taskIdGenerator(prefix: string) {
  return `${prefix}_${uuidv4()}`
}
