/**
 * 流程类型
 */
export const PROCESS_TYPE = {
  flowable: 'flowable',
  activiti: 'activiti',
  camunda: 'camunda'
}

/**
 * 流程前缀
 */
export const FLOWABLE_PREFIX: string = PROCESS_TYPE.flowable
export const ACTIVITI_PREFIX: string = PROCESS_TYPE.activiti
export const CAMUNDA_PREFIX: string = PROCESS_TYPE.camunda

/**
 * 流程类型对应命名空间
 */
export const TYPE_TARGET = {
  activiti: 'http://activiti.org/bpmn',
  camunda: 'http://bpmn.io/schema/bpmn',
  flowable: 'http://flowable.org/bpmn'
}

export const CUSTOM_TASK_LIST = [
  'custom:PH',
  'custom:Crystal',
  'custom:Dilute',
  'custom:Dissolve',
  'custom:Dry',
  'custom:DrySolution',
  'custom:Exsolution',
  'custom:Extract',
  'custom:Filtration',
  'custom:GetMaterial',
  'custom:ParallelReaction',
  'custom:AutoMix',
  'custom:MakeIngredients',
  'custom:MicroWaveReaction',
  'custom:Mix',
  'custom:PhaseSeparate',
  'custom:Purify',
  'custom:Quench',
  'custom:Reaction',
  'custom:Recrystal',
  'custom:ReturnMaterial',
  'custom:SetupReaction',
  'custom:TLC',
  'custom:Triturate',
  'custom:WeighProduct'
]
