{"name": "custom", "prefix": "custom", "uri": "http://custom.org/schema", "xml": {"tagAlias": "lowerCase"}, "associations": [], "types": [{"name": "PH", "superClass": ["bpmn:Task"], "properties": [{"name": "address", "type": "String", "isAttr": true}, {"name": "username", "type": "String", "isAttr": true}, {"name": "port", "type": "String", "isAttr": true}, {"name": "tables", "type": "Table", "isMany": true, "isBody": true}]}, {"name": "Crystal", "superClass": ["bpmn:Task"], "properties": [{"name": "address", "type": "String", "isAttr": true}, {"name": "username", "type": "String", "isAttr": true}, {"name": "port", "type": "String", "isAttr": true}, {"name": "tables", "type": "Table", "isMany": true, "isBody": true}]}, {"name": "Dilute", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Dissolve", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Dry", "superClass": ["bpmn:Task"], "properties": []}, {"name": "DrySolution", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Exsolution", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Filtration", "superClass": ["bpmn:Task"], "properties": []}, {"name": "GetMaterial", "superClass": ["bpmn:Task"], "properties": []}, {"name": "MakeIngredients", "superClass": ["bpmn:Task"], "properties": []}, {"name": "MicroWaveReaction", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Mix", "superClass": ["bpmn:Task"], "properties": []}, {"name": "PhaseSeparate", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Purify", "superClass": ["bpmn:Task"], "properties": []}, {"name": "PurifyDry", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Quench", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Reaction", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Recrystal", "superClass": ["bpmn:Task"], "properties": []}, {"name": "ReturnMaterial", "superClass": ["bpmn:Task"], "properties": []}, {"name": "SetupReaction", "superClass": ["bpmn:Task"], "properties": []}, {"name": "TLC", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Triturate", "superClass": ["bpmn:Task"], "properties": []}, {"name": "WeighProduct", "superClass": ["bpmn:Task"], "properties": []}, {"name": "Extract", "superClass": ["bpmn:Task"], "properties": []}]}