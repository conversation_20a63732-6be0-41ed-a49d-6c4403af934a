import { ReactComponent as SortSvg } from '@/assets/svgs/sort.svg'
import { getWord } from '@/utils'
import { ProFormSelect } from '@ant-design/pro-components'
import { Select } from 'antd'
import cs from 'classnames'
import { useState } from 'react'
import styles from './index.less'
interface SortFilterProps {
  isAsc: boolean
  handleSort: (order: 'asc' | 'desc') => void
  valueEnum: any
  wrapClassName?: any
  width?: number
  isForm?: boolean
  defaultSortFiled?: string
  sortFiledChange?: (value: string) => void
}
export default function SortFilter(props: SortFilterProps) {
  const [isAsc, setIsAsc] = useState<boolean>(props?.isAsc)

  return (
    <div className={cs(props?.wrapClassName, 'flex-align-items-center')}>
      {props?.isForm ? (
        <ProFormSelect
          name="sort"
          valueEnum={props?.valueEnum}
          style={{ width: `${props?.width}px` || '120px' }}
          placeholder={getWord('select-tip')}
          allowClear={false}
        />
      ) : (
        <Select
          onChange={props?.sortFiledChange}
          defaultValue={props?.defaultSortFiled}
          options={props?.valueEnum}
          style={{ width: `${props?.width}px` || '120px' }}
          placeholder={getWord('select-tip')}
          allowClear={false}
        />
      )}
      <SortSvg
        className={cs(styles.sortButton, {
          [styles['sortButton_des']]: !isAsc
        })}
        width={25}
        style={{ marginTop: props?.isForm ? '-22px' : '0px' }}
        onClick={() => {
          props?.handleSort(!isAsc ? 'asc' : 'desc')
          setIsAsc(!isAsc)
        }}
      />
    </div>
  )
}
