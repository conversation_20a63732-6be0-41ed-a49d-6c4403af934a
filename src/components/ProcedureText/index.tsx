import Paragraph from 'antd/lib/typography/Paragraph'
import Text from 'antd/lib/typography/Text'
import Title from 'antd/lib/typography/Title'
import cs from 'classnames'
import { useState } from 'react'
import type { ProcedureTextProps } from './index.d'
import styles from './index.less'

export default function ProcedureText({ procedure, rows }: ProcedureTextProps) {
  const { text } = procedure
  const [expanded, setExpanded] = useState<boolean>(false)
  const expandText = (
    <Text
      className={styles.expandedIcon}
      onClick={(e) => {
        e.stopPropagation()
        setExpanded((pre) => !pre)
      }}
    >
      {expanded ? 'less' : 'more'}
    </Text>
  )
  return (
    <div className={cs(styles.procedureText)}>
      <Title level={5}>Procedure</Title>
      <Paragraph
        ellipsis={
          expanded
            ? false
            : {
                rows: rows || 5,
                expandable: true,
                symbol: expandText
              }
        }
        copyable={{ text }}
      >
        {text || procedure?.experimentalProcedure || ''}
        {expanded && expandText}
      </Paragraph>
    </div>
  )
}
