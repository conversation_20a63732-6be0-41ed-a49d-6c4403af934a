import { Ketch<PERSON> } from 'ketcher-core'
import { ButtonsConfig, Editor } from 'ketcher-react'
import 'ketcher-react/dist/index.css'
import { ButtonName } from 'ketcher-react/dist/script/builders/ketcher/ButtonName'
import { StandaloneStructServiceProvider } from 'ketcher-standalone'
import { FC, useEffect, useRef, useState } from 'react'
import './index.less'
import { KetcherEditorProps } from './type'

const structServiceProvider = new StandaloneStructServiceProvider()
const hiddenButtonsConfig: ButtonsConfig = [
  'about',
  'fullscreen',
  'help',
  'save',
  'paste'
].reduce<ButtonsConfig>((acc, cur) => {
  acc[cur as ButtonName] = { hidden: true }
  return acc
}, {})

export const KetcherEditor: FC<KetcherEditorProps> = ({
  initMoleculeSmiles,
  onKetcherUpdate,
  clearEvent,
  updateEvent
}) => {
  const [ketcher, setKetcher] = useState<Ketcher>()
  const isMounted = useRef(false)
  const scrollPosition = useRef<{ x: number; y: number }>(0)
  const onInit = (ketcher: Ketcher) => {
    setKetcher(ketcher)
    onKetcherUpdate?.(ketcher)
  }

  if (!isMounted.current) {
    scrollPosition.current = {
      x: window.scrollX,
      y: window.scrollY
    }
  }

  useEffect(() => {
    // https://github.com/epam/ketcher/issues/3813
    setTimeout(() => {
      window.scrollTo(scrollPosition.current.x, scrollPosition.current.y)
    }, 100)
    isMounted.current = true
  }, [])

  useEffect(() => {
    if (initMoleculeSmiles !== undefined)
      ketcher?.setMolecule(initMoleculeSmiles)
  }, [initMoleculeSmiles, ketcher, clearEvent])

  useEffect(() => {
    ketcher?.setMolecule(updateEvent?.update || '')
  }, [updateEvent])

  return (
    <Editor
      staticResourcesUrl={''}
      structServiceProvider={structServiceProvider}
      errorHandler={(e) => console.error(e)}
      onInit={onInit}
      buttons={hiddenButtonsConfig}
    />
  )
}

export default KetcherEditor
