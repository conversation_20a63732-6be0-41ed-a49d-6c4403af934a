import { useState } from 'react'
import { EditorDialogProps } from './EditorDialog'

export const useSmilesEditor = (): {
  dialogProps: EditorDialogProps
  inputSmiles: (smiles?: string) => Promise<string>
} => {
  const [dialogProps, setDialogProps] = useState<EditorDialogProps>({})
  const inputSmiles = (init?: string) => {
    const promise = new Promise<string>((resolve, reject) => {
      setDialogProps({
        init: init || '',
        open: true,
        clearWhenClose: true,
        onClose: (smiles) => {
          if (smiles === null) reject()
          else resolve(smiles)
          setDialogProps({ open: false, init: '' })
        }
      })
    })
    return promise
  }

  return { dialogProps, inputSmiles }
}
