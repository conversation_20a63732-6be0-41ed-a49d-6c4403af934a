import { Ke<PERSON><PERSON> } from 'ketcher-core'
import { ReactNode } from 'react'

interface KetcherEditorProps {
  extraFormItem?: ReactNode
  initMoleculeSmiles?: string
  onKetcherUpdate?: (ketcher: <PERSON><PERSON><PERSON>) => void
  clearEvent?: Record<string, never>
  updateEvent?: Record<'update', string>
}

interface KetcherWithInputEditorProps extends KetcherEditorProps {
  getSmiles?: (config?: GetSmilesConfig) => Promise<string | false>
  setSmiles?: (smiles: string) => void
}

type FunctionButtonName =
  | 'open'
  | 'clear'
  | 'copy'
  | 'cut'
  | 'save'
  | 'paste'
  | 'undo'
  | 'redo'

export interface EditorDialogProps {
  init?: string
  open?: boolean
  onClose?: (smiles: string | null) => void
  clearWhenClose?: boolean
  width?: number | string
}
