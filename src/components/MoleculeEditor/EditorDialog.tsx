/* eslint-disable @typescript-eslint/no-unused-expressions */
import useKetcher from '@/hooks/useKetcher'
import { getWord } from '@/utils'
import { Modal } from 'antd'
import React, { useState } from 'react'
import KetcherEditor from './LazyKetcher'
import type { EditorDialogProps } from './type'
const EditorDialog: React.FC<EditorDialogProps> = ({
  init,
  open,
  width = '80%',
  onClose,
  clearWhenClose = false
}) => {
  const { getSmiles, setSmiles, props } = useKetcher(init)
  const [confirmLoading, setConfirmLoading] = useState(false)
  const close = async (withSmiles?: boolean) => {
    setConfirmLoading(true)
    if (!withSmiles) {
      onClose?.(null)
      setConfirmLoading(false)
    } else {
      const smiles = await getSmiles()
      await onClose?.(smiles || null)
      setConfirmLoading(false)
    }
    clearWhenClose && setSmiles('')
  }

  return (
    <Modal
      title={getWord('edit-molecule')}
      width={width}
      open={open}
      onOk={async () => close(true)}
      onCancel={async () => close()}
      confirmLoading={confirmLoading}
      focusTriggerAfterClose
      destroyOnClose
      centered
    >
      <KetcherEditor {...props} />
    </Modal>
  )
}

export default EditorDialog
