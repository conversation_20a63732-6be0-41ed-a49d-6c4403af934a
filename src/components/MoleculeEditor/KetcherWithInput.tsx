import { getWord } from '@/utils'
import { ProForm } from '@ant-design/pro-components'
import { Space } from 'antd'
import { FC } from 'react'
import ImageToSmiles from '../ImageToSmiles'
import TextToSmiles from '../TextToSmiles'
import Editor from './Ketcher'
import { KetcherWithInputEditorProps } from './type'

export const KetcherWithInputEditor: FC<KetcherWithInputEditorProps> = ({
  getSmiles,
  setSmiles,
  ...editorProps
}) => {
  return (
    <>
      <ProForm
        grid
        submitter={false}
        style={{ paddingLeft: '20px', marginBottom: '8px' }}
      >
        <Space>
          {editorProps?.extraFormItem}
          <ProForm.Item
            label={getWord('CAS-name-tip')}
            tooltip={getWord('max-l-50')}
          >
            <TextToSmiles getSmiles={getSmiles} onParsed={setSmiles} />
          </ProForm.Item>
          <ProForm.Item label={getWord('smiles-recognize')}>
            <ImageToSmiles getSmiles={getSmiles} onParsed={setSmiles} />
          </ProForm.Item>
        </Space>
      </ProForm>
      <Editor {...editorProps} />
    </>
  )
}

export default KetcherWithInputEditor
