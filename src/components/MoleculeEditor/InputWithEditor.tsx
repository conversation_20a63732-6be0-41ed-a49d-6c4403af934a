import EditorDialog from '@/components/MoleculeEditor/EditorDialog'
import { But<PERSON> } from 'antd'
import Input from 'rc-input'
import React, { useState } from 'react'

interface InputWithEditorProps {
  init?: string
  onChange?: (smiles?: string) => void
}

const InputWithEditor: React.FC<InputWithEditorProps> = ({
  init,
  onChange
}) => {
  const [open, setOpen] = useState<boolean>(false)
  const [smiles, setSmiles] = useState<string>(init || '')

  const updateSmiles = (smiles: string) => {
    setSmiles(smiles)
    onChange?.(smiles)
  }

  return (
    <>
      <Input value={smiles} onChange={(e) => updateSmiles(e.target.value)} />
      <Button onClick={() => setOpen(true)}>Edit</Button>
      <EditorDialog
        open={open}
        init={smiles}
        onClose={(s) => {
          s && updateSmiles(s)
          setOpen(false)
        }}
      />
    </>
  )
}

export default InputWithEditor
