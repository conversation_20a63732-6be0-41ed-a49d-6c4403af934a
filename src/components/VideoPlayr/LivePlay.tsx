import RTCPlayer from '@/components/RTCPlayer'
import StatusTip from '@/components/StatusTip'
import { Video } from '@/types/models/videos'
import { getWord, isValidArray } from '@/utils'
import { Col, Row } from 'antd'
import styles from './index.less'
interface LivePlayProps {
  liveVideos: Video[]
  labLocation: string
  robotNo?: string
  isRobot?: boolean
}

export default function LivePlay(props: LivePlayProps) {
  const { liveVideos, labLocation, robotNo, isRobot } = props
  const RobotVideoName = ({ targetIndex }: { targetIndex: number }) => {
    return (
      <div className={styles.tag}>
        <span>{`${getWord('live')}-${robotNo}-${getWord(
          liveVideos[targetIndex]?.usage
        )}`}</span>
      </div>
    )
  }

  const LabVideoName = ({ targetIndex }: { targetIndex: number }) => {
    return (
      <div className={styles.tag}>
        <span>{`${getWord('live')}-${getWord('lab')}${labLocation}-${
          liveVideos[targetIndex]?.usage
        }`}</span>
      </div>
    )
  }

  function renderName(targetIndex: number) {
    if (isRobot) return <RobotVideoName targetIndex={targetIndex} />
    else return <LabVideoName targetIndex={targetIndex} />
  }

  return isValidArray(liveVideos) ? (
    <Row>
      <Col
        span={17}
        key={liveVideos[0]?.video_live_url}
        className={styles.videoContent}
        style={{ paddingBottom: '4px' }}
      >
        <RTCPlayer rtcLink={liveVideos[0]?.video_live_url} />
        {renderName(0)}
      </Col>
      <Col span={7} className={styles.videoContent}>
        <Row key={liveVideos[1]?.video_live_url}>
          <RTCPlayer rtcLink={liveVideos[1]?.video_live_url} />
          {renderName(1)}
        </Row>
        <Row
          key={liveVideos[2]?.video_live_url}
          className={styles.videoContent}
        >
          <RTCPlayer rtcLink={liveVideos[2]?.video_live_url} />
          {renderName(2)}
        </Row>
        <Row>
          <Col
            span={12}
            key={liveVideos[3]?.video_live_url}
            className={styles.videoContent}
          >
            <RTCPlayer rtcLink={liveVideos[3]?.video_live_url} />
            {renderName(3)}
          </Col>
          <Col
            span={12}
            key={liveVideos[4]?.video_live_url}
            className={styles.videoContent}
          >
            <RTCPlayer rtcLink={liveVideos[4]?.video_live_url} />
            {renderName(4)}
          </Col>
        </Row>
      </Col>
    </Row>
  ) : (
    <StatusTip des={getWord('noticeIcon.empty')} />
  )
}
