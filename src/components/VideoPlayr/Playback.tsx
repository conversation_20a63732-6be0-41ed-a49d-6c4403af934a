import PlyrVideo from '@/components/PlyrVideo'
import StatusTip from '@/components/StatusTip'
import { Video } from '@/types/models/videos'
import { getEnvConfig, getWord, isValidArray } from '@/utils'
import { Col, Row } from 'antd'
import styles from './index.less'
interface PlaybackProps {
  experimentVideos: Video[]
  labLocation: string
  experimentNo?: string
  robotNo?: string
  isRobot?: boolean
  taskName?: string
}

export default function PlaybackCom(props: PlaybackProps) {
  const {
    experimentVideos,
    labLocation,
    robotNo,
    taskName,
    experimentNo,
    isRobot
  } = props
  const RobotVideoName = ({ targetIndex }: { targetIndex: number }) => {
    return (
      <div className={styles.tag}>
        <span>{`${getWord('playback')}-${robotNo}-${getWord(
          experimentVideos[targetIndex]?.usage
        )}-${experimentNo}-${taskName}`}</span>
      </div>
    )
  }

  const LabVideoName = ({ targetIndex }: { targetIndex: number }) => {
    return (
      <div className={styles.tag}>
        <span>{`${getWord('playback')}-${getWord('lab')}${labLocation}-${
          experimentVideos[targetIndex]?.usage
        }`}</span>
      </div>
    )
  }

  function renderName(targetIndex: number) {
    if (isRobot) return <RobotVideoName targetIndex={targetIndex} />
    else return <LabVideoName targetIndex={targetIndex} />
  }
  return isValidArray(experimentVideos) ? (
    <Row>
      <Col
        span={16}
        key={experimentVideos[0]?.video_url}
        className={styles.videoContent}
        style={{ paddingBottom: '4px' }}
      >
        <PlyrVideo
          link={`${getEnvConfig().apiBase}${experimentVideos[0]?.video_url}`}
        />
        {renderName(0)}
      </Col>
      <Col span={8} className={styles.videoContent}>
        <Row key={experimentVideos[1]?.video_url}>
          <PlyrVideo
            link={`${getEnvConfig().apiBase}${experimentVideos[1]?.video_url}`}
          />
          {renderName(1)}
        </Row>
        <Row
          key={experimentVideos[2]?.video_url}
          className={styles.videoContent}
        >
          <PlyrVideo
            link={`${getEnvConfig().apiBase}${experimentVideos[2]?.video_url}`}
          />
          {renderName(2)}
        </Row>
        <Row>
          <Col
            span={12}
            key={experimentVideos[3]?.video_url}
            className={styles.videoContent}
            // style={{ paddingRight: '5px' }}
          >
            <PlyrVideo
              link={`${getEnvConfig().apiBase}${
                experimentVideos[3]?.video_url
              }`}
            />
            {renderName(3)}
          </Col>
          <Col
            span={12}
            key={experimentVideos[4]?.video_url}
            className={styles.videoContent}
            // style={{ paddingLeft: '5px' }}
          >
            <PlyrVideo
              link={`${getEnvConfig().apiBase}${
                experimentVideos[4]?.video_url
              }`}
            />
            {renderName(4)}
          </Col>
        </Row>
      </Col>
    </Row>
  ) : (
    <StatusTip
      des={getWord('noticeIcon.empty')}
      // wrapperClassName="fullLayoutContent"
    />
  )
}
