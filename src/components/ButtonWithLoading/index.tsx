import { Button, ButtonProps } from 'antd'
import React, { useState } from 'react'

const ButtonWithLoading: React.FC<ButtonProps> = ({ onClick, ...props }) => {
  const [loading, setLoading] = useState<boolean>(false)
  const onClickHandler: ButtonProps['onClick'] = async (event) => {
    setLoading(true)
    try {
      const result = await onClick?.(
        event as React.MouseEvent<HTMLButtonElement, MouseEvent>
      )
      setLoading(false)
      return result
    } catch {
      setLoading(false)
      return ''
    }
  }

  return <Button loading={loading} {...props} onClick={onClickHandler} />
}

export default ButtonWithLoading
