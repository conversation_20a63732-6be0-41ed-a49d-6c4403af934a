import { getWord } from '@/utils'

import { Tag } from 'antd'
import { ReactElement } from 'react'

export interface StatusRenderProps<T extends string> {
  status: T
  label?: string
  labelPrefix?: string
  colorMap?: Record<string, string>
  className?: string
}

const commonStatusMap: Record<string, string> = {
  created: '#F5B544',
  editing: '#F5B544',
  started: '#4B9F47',
  holding: '#E6521F',
  confirmed: '#4B9F47',
  finished: '#1890FF',
  cancelled: '#979797',
  canceled: '#979797',

  running: '#2AD259',
  hold: '#E6521F',
  completed: '#1890FF',
  success: '#F51D2C',
  failed: '#9747FF',

  todo: '#F5B544',
  checking: '#4B9F47'
}

const StatusRender: <T extends string>(
  props: StatusRenderProps<T>
) => ReactElement<StatusRenderProps<T>> = ({
  status,
  colorMap,
  labelPrefix,
  label: propLabel,
  className
}) => {
  const color = { ...commonStatusMap, ...colorMap }[status]
  const label = propLabel || getWord(`${labelPrefix}.${status}`)
  return (
    <Tag className={className} color={color}>
      {label}
    </Tag>
  )
}

export default StatusRender
