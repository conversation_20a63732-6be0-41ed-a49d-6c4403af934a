@import '@/style/variables.less';
.tipIcon {
  svg {
    width: 22px;
    height: auto;
  }
}

.collapsedTipIcon {
  svg {
    width: 20px;
    height: auto;
  }
}

.tipIcon:hover,
.collapsedTipIcon:hover {
  cursor: pointer;
}

.notification-list-root {
  width: 400px;
  padding: 4;
  .title-wrapper {
    display: flex;
    align-items: center;
    .title {
      margin: 0;
    }
  }
  .title-actions-wrapper {
    margin-left: auto;
  }
  .ant-list-item-extra {
    > div {
      height: 100%;
      .actions-wrapper {
        display: flex;
        flex-direction: column;
        height: 100%;
        margin-left: auto;
        .top-actions-wrapper {
          margin-left: auto;
        }
        .bottom-actions-wrapper {
          margin-top: auto;
        }
      }
    }
  }
  .divider {
    margin: 4px 0;
  }
  .notification-list {
    max-height: 70vh;
    overflow-y: auto;
  }

  :global {
    .ant-pro-card-body {
      padding: 16px 0;
      .ant-pro-list-row-content {
        margin-inline: 0;
      }
      td.ant-descriptions-item {
        padding-bottom: 4px;
      }
    }
  }

  .commonTag {
    display: flex;
    align-items: center;
    width: max-content;
    height: 24px;
    padding: 0 4px;
    font-size: 12px;
  }
  .statusDes {
    &_completed,
    &_success {
      color: @color-completed;
      background-color: #ecf8ef;
      border: 1px solid @color-completed;
    }
    &_running {
      color: @color-running;
      border: 1px solid @color-running;
    }
    &_limited {
      color: @color-pending;
      border: 1px solid @color-pending;
    }
    &_pending {
      color: @color-pending;
      border: 1px solid @color-pending;
    }
    &_failed {
      color: @color-failed;
      border: 1px solid @color-failed;
    }
  }
}
