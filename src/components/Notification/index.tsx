import { ReactComponent as TipIcon } from '@/assets/svgs/navbar/tip.svg'
import useNotification from '@/hooks/useNotifications'
import { history, useModel } from '@umijs/max'
import { Badge } from 'antd'
import React from 'react'
import styles from './index.less'

export const Notification: React.FC = () => {
  const { unreadCount } = useNotification({ unreadOnly: true })
  const { initialState } = useModel('@@initialState')
  return (
    <div
      className={
        initialState?.isMenuCollapsed ? styles.collapsedTipIcon : styles.tipIcon
      }
      onClick={() => history.push('/message-notification')}
    >
      <Badge count={unreadCount} style={{ boxShadow: 'none' }} color="#faad14">
        <TipIcon fill="#626262" />
      </Badge>
    </div>
  )
}
