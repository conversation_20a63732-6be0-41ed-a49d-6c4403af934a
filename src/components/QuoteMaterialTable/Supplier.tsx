import useOptions from '@/hooks/useOptions'
import { SupplierDetail } from '@/services/brain'
import { getWord, isEN, isValidArray, roundToTwoDecimalPlaces } from '@/utils'
import type { ProColumns } from '@ant-design/pro-components'
import { EditableProTable, ModalForm } from '@ant-design/pro-components'
import { Form } from 'antd'
import { useEffect, useState } from 'react'

import type { SupplierProps } from './index.d'
import styles from './index.less'
export default function Supplier(props: SupplierProps) {
  const { supplierDatas, confirmSupplierDatas } = props
  const { editableConfig } = useOptions()
  const [form] = Form.useForm<SupplierDetail>()
  const [dataSource, setDataSource] = useState<SupplierDetail[]>([])
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>()
  const [isEditType, setIsEditType] = useState<boolean>(false)
  useEffect(() => {
    setDataSource(supplierDatas)
  }, [supplierDatas])

  const changeEditorType = (_isEditorType: boolean) => {
    if (_isEditorType && isValidArray(dataSource)) {
      setIsEditType(true)
      setEditableRowKeys(() => dataSource.map((item) => item.id))
    } else {
      setIsEditType(false)
      setEditableRowKeys([])
    }
  }
  const renderQuantity = (record) =>
    record?.quantity && record?.unit
      ? `${record?.quantity}${record?.unit}`
      : '-'

  const columns: ProColumns<SupplierDetail>[] = [
    {
      title: 'CAS',
      dataIndex: 'cas_no',
      width: 80,
      readonly: true,
      valueType: 'text'
    },
    {
      title: getWord('supplier'),
      dataIndex: 'source',
      width: 80,
      readonly: true,
      valueType: 'text',
      renderText: (text: string, record) => {
        return record?.source_link && !isEditType ? (
          <a onClick={() => window.open(record?.source_link)}>
            {text}
            {record?.material_lib?.version ? (
              <>
                &nbsp;
                <span className="red">({record?.material_lib?.version})</span>
              </>
            ) : (
              ''
            )}
          </a>
        ) : (
          text
        )
      }
    },
    {
      title: getWord('amount'),
      dataIndex: 'quantity',
      width: 80,
      readonly: true,
      renderFormItem: (info: string) => renderQuantity(info?.entity),
      render: (_: string, record) => renderQuantity(record)
    },
    {
      title: getWord('purity'),
      dataIndex: 'purity',
      width: 80,
      readonly: true
    },
    {
      title: getWord('spot-or-futures'),
      readonly: true,
      dataIndex: 'in_stock',
      width: 120,
      initialValue: ['true', 'false'],
      valueEnum: {
        true: { text: getWord('spot') },
        false: { text: getWord('futures') }
      }
    },
    {
      title: `${getWord('price')}/￥`,
      readonly: true,
      dataIndex: 'price',
      width: 120,
      render: (_, item) =>
        item?.price && !isNaN(item?.price) ? item?.price.toFixed(2) : ''
    },
    {
      title: `${getWord('unit-price')}￥/g`,
      readonly: true,
      width: 130,
      dataIndex: 'unit_price',
      render: (_, item) =>
        item?.unit_price && !isNaN(item?.unit_price)
          ? item?.unit_price.toFixed(2)
          : ''
    },
    {
      title: getWord('quantity'),
      width: 90,
      valueType: 'digit',
      readonly: false,
      dataIndex: 'count',
      formItemProps: {
        rules: [
          {
            message: getWord('cant-input-negative-number'),
            pattern: /^[0-9]\d*$/
          }
        ]
      }
    },
    {
      title: `${getWord('cost')}/￥`,
      readonly: true,
      width: 120,
      dataIndex: 'decs',
      renderText: (_: string, record) => {
        let sum =
          record?.count && record?.price ? record?.count * record?.price : '-'
        return roundToTwoDecimalPlaces(sum)
      }
    }
  ]
  return (
    <ModalForm<SupplierDetail>
      title={getWord('supplier')}
      width={1180}
      trigger={<a key="supplier">{getWord('supplier')}</a>}
      form={form}
      autoFocusFirstInput
      onOpenChange={(open: boolean) => {
        if (!open) setDataSource(supplierDatas)
      }}
      modalProps={{
        destroyOnClose: true
      }}
      submitter={{
        resetButtonProps: { style: { display: 'none' } },
        submitButtonProps: {
          style: { display: !isEditType ? undefined : 'none' }
        },
        searchConfig: {
          submitText: getWord('pages.route.edit.label.save')
        }
      }}
      onFinish={async () => {
        await confirmSupplierDatas(dataSource)
        return true
      }}
    >
      <div className={styles.editContent}>
        <span>{getWord('quotate-price-tip')}</span>
        <a
          onClick={() => changeEditorType(!isEditType)}
          style={{ width: isEN() ? '120px' : 'auto' }}
        >
          {isEditType ? getWord('supplier.edit.finished') : getWord('edit')}
        </a>
      </div>
      <EditableProTable<SupplierDetail>
        columns={columns}
        rowKey="id"
        value={dataSource}
        onChange={setDataSource}
        recordCreatorProps={false}
        scroll={{ y: 300 }}
        editable={{
          ...editableConfig,
          type: 'multiple',
          editableKeys,
          onValuesChange: (_record, recordList) => {
            setDataSource(recordList)
          },
          onChange: setEditableRowKeys
        }}
      />
    </ModalForm>
  )
}
