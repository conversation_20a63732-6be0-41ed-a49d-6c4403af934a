import type { Material, SupplierDetail } from '@/services/brain'

export interface MaterialTableProps {
  materialData: Material[]
  enableEdit?: boolean
  hiddeStep?: boolean
  enableAddMaterial?: boolean
  handleSaveQuoteData: (value: unknown, successCb?: () => void) => void
  quoteMoleculeId: string
  projectReactionId?: number
}

export interface SupplierProps {
  supplierDatas: SupplierDetail[]
  confirmSupplierDatas: (values: SupplierDetail[]) => void
}
