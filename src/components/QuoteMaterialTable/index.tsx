import { ReactComponent as HelpIcon } from '@/assets/svgs/help.svg'
import { materialUnitValueEnum } from '@/constants'
import useOptions from '@/hooks/useOptions'
import { isReadonlyMaterialRole } from '@/pages/route/util'
import type { Material, SupplierDetail } from '@/services/brain'
import {
  fetchSuppliers,
  getWord,
  isEN,
  isValidArray,
  roundToTwoDecimalPlaces
} from '@/utils'
import { EditableProTable, ProColumns } from '@ant-design/pro-components'
import { useAccess, useModel } from '@umijs/max'
import { Popover } from 'antd'
import { cloneDeep, isEmpty } from 'lodash'
import { useEffect, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { EditorDialog } from '../MoleculeEditor'
import SmilesInput from '../SmilesInput'
import Supplier from './Supplier'
import type { MaterialTableProps } from './index.d'
import styles from './index.less'

export default function QuoteMaterialTable(props: MaterialTableProps) {
  const { editableConfig } = useOptions()
  const { quoteMoleculeId, projectReactionId } = props
  const isQuotation: boolean = location.pathname.includes('quotation-records')
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([])
  const [dataSource, setDataSource] = useState<readonly Material[]>([])
  const { updateQuotes } = useModel('quotation')
  const access = useAccess()

  useEffect(() => {
    setDataSource(props?.materialData)
  }, [props?.materialData])

  const handleSaveQuoteData = async (values: any, successCb?: () => void) => {
    const res = await updateQuotes(quoteMoleculeId as string, {
      ...values,
      project_reaction_id: projectReactionId
    })
    if (res && successCb) successCb()
  }

  const saveEditItem = async (
    data: Material,
    isAdd?: boolean,
    newDataSource?: Material[]
  ) => {
    let params = {
      project_reaction_id: projectReactionId,
      role: data?.role,
      cas_no: data?.cas_no,
      detail: data?.detail,
      smi: data?.smi,
      name_zh: data?.name_zh,
      name_en: data?.name_en,
      equivalent: data?.equivalent,
      unit: data?.unit,
      required_quantity: data?.required_quantity,
      set_cost: data?.set_cost,
      material_no: data?.no,
      gPerMol: data?.gPerMol,
      action: isAdd ? 'add' : undefined
    }
    await handleSaveQuoteData(params, () => {
      if (newDataSource) setDataSource(newDataSource)
    })
  }

  const getSuppliers = async (
    _newSmiles: string,
    record: Material,
    isAdd?: boolean
  ) => {
    let newDataSource = cloneDeep(props?.materialData)
    const supplierInfo = await fetchSuppliers(_newSmiles)
    const getTargetValue = (
      key: 'gPerMol' | 'name_en' | 'name_zh' | 'cas_no'
    ) => {
      let targetItem = supplierInfo.find((e) => e[key] && e[key] !== null)
      return targetItem ? targetItem[key] : undefined
    }
    if (supplierInfo) {
      let _gPerMol = getTargetValue('gPerMol'),
        name_en = getTargetValue('name_en'),
        name_zh = getTargetValue('name_zh'),
        cas_no = getTargetValue('cas_no')
      let editedData = {
        ...record,
        smi: _newSmiles,
        detail: supplierInfo,
        gPerMol: _gPerMol,
        role: record?.role || 'reactant',
        name_en,
        cas_no,
        name_zh
      }
      newDataSource.push(editedData)
      await saveEditItem(editedData as Material, isAdd, newDataSource)
    }
  }

  const [columns, setColumns] = useState<ProColumns<Material>[]>([
    {
      title: getWord('structural'),
      dataIndex: 'smi',
      readonly: true,
      width: 120,
      valueType: 'text',
      renderFormItem: (info: string, { isEditable }) => {
        const record = info?.entity
        const smiles = info?.entity?.smi
        return info?.entity?.step_no === getWord('total') ||
          isEditable ||
          smiles === '-' ? (
          smiles
        ) : (
          <div className={styles.smi}>
            {
              <SmilesInput
                multiple={false}
                onChange={(newSmiles: string) =>
                  getSuppliers(newSmiles, record)
                }
              />
            }
          </div>
        )
      },
      render: (smiles, record) => (
        <div className={styles.smi}>
          {record?.step_no === getWord('total') ? (
            '-'
          ) : (
            <SmilesInput
              value={smiles === '-' ? [] : smiles}
              disabled={isReadonlyMaterialRole(record?.role as string)}
              multiple={false}
              onChange={(newSmiles: string) => getSuppliers(newSmiles, record)}
            />
          )}
        </div>
      )
    },
    {
      title: getWord('role'),
      dataIndex: 'role',
      valueType: 'select',
      width: 110,
      valueEnum: (row) =>
        row?.role === 'main_reactant'
          ? {
              main_reactant: getWord('main-reactant')
            }
          : {
              reactant: getWord('reactant'),
              other_reagent: getWord('other-reagent'),
              solvent: getWord('solvent')
            }
    },
    {
      title: getWord('chinese-name'),
      dataIndex: 'name_zh',
      width: 100,
      valueType: 'text',
      hideInTable: isEN()
    },
    {
      title: getWord('english-name'),
      dataIndex: 'name_en',
      width: 100,
      valueType: 'text'
    },
    {
      title: 'CAS',
      dataIndex: 'cas_no',
      width: 100,
      valueType: 'text'
    },
    {
      title: getWord('molecular-mass'),
      dataIndex: 'gPerMol',
      width: 100,
      hideInTable: !isQuotation,
      valueType: 'digit',
      renderText: (_, item) => roundToTwoDecimalPlaces(item?.gPerMol as number)
    },
    {
      title: getWord('EWR'),
      dataIndex: 'equivalent',
      valueType: 'digit',
      width: 140,
      formItemProps: {
        rules: [
          {
            message: getWord('pattern-positive-number'),
            pattern: /^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/
          }
        ]
      },
      renderText: (_, item) =>
        roundToTwoDecimalPlaces(item?.equivalent as number)
    },
    {
      title: getWord('measurement-method'),
      dataIndex: 'unit',
      width: 120,
      valueEnum: materialUnitValueEnum
    },
    {
      title: `${getWord('required-weight')}/g`,
      dataIndex: 'required_quantity',
      valueType: 'digit',
      width: 100,
      formItemProps: {
        rules: [
          {
            message: getWord('pattern-positive-number'),
            pattern: /^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/
          }
        ]
      },
      renderText: (_, item) =>
        roundToTwoDecimalPlaces(item?.required_quantity as number)
    },
    {
      title: () => (
        <div className="flex-align-items-center">
          <div>{getWord('min-cost')}/￥</div>
        </div>
      ),
      dataIndex: 'cost',
      valueType: 'text',
      width: 140,
      readonly: true,
      renderText: (_, item) => {
        if (item?.step_no === getWord('total')) return ''
        let digitDes = roundToTwoDecimalPlaces(item?.cost as number)
        return !isNaN(digitDes) && !isEmpty(digitDes)
          ? digitDes
          : getWord('please-quote')
      }
    },
    {
      title: () => (
        <div className="flex-align-items-center">
          <div>{getWord('cost')}/￥</div>
          <Popover content={getWord('cost-empty-tip')}>
            <HelpIcon width={18} style={{ cursor: 'pointer' }} />
          </Popover>
        </div>
      ),
      dataIndex: 'set_cost',
      valueType: 'text',
      width: 140,
      formItemProps: {
        rules: [
          {
            message: getWord('pattern-positive-number'),
            pattern: /^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/
          }
        ]
      },
      renderText: (_, item) => {
        if (item?.step_no === getWord('total')) return ''
        let digitDes = roundToTwoDecimalPlaces(
          item?.set_cost || (item?.cost as number)
        )
        return !isNaN(digitDes) && !isEmpty(digitDes) ? digitDes : '-'
      }
    }
  ])

  useEffect(() => {
    if (!props?.hiddeStep) {
      let _columns = cloneDeep(columns)
      _columns.unshift({
        title: getWord('steps'),
        width: 60,
        key: 'step_no',
        dataIndex: 'step_no',
        fixed: 'left',
        readonly: true,
        render: (_text, record: Material) =>
          record?.step_no === getWord('total') ? (
            record?.step_no
          ) : (
            <a href={`#${record?.step_no}`}>{record?.step_no}</a>
          )
      })
      setColumns(_columns)
    }
  }, [props?.hiddeStep])

  const operate = () => {
    return [
      {
        title: getWord('pages.experiment.label.operation'),
        valueType: 'option',
        width: 150,
        fixed: 'right',
        render: (
          _text: string,
          record: Material,
          _: any,
          action: { startEditable: (no: string) => void }
        ) => [
          <>
            {record?.step_no === getWord('total') ? (
              ''
            ) : (
              <a
                key="editable"
                onClick={() => {
                  setEditableRowKeys([...editableKeys, record.no])
                  action?.startEditable?.(record.no)
                }}
              >
                {getWord('edit')}
              </a>
            )}
            {access?.authCodeList?.includes(
              'quotation-records.button.supplier'
            ) && isValidArray(record?.detail) ? (
              <Supplier
                supplierDatas={record?.detail as SupplierDetail[]}
                confirmSupplierDatas={async (values) =>
                  await updateQuotes(quoteMoleculeId as string, {
                    material_no: record?.no,
                    detail: values
                  })
                }
              />
            ) : (
              ''
            )}
          </>
        ]
      }
    ]
  }

  const [open, setOpen] = useState<boolean>(false)

  return (
    <>
      <EditableProTable<Material>
        rowKey="no"
        maxLength={5}
        scroll={{
          x: 960
        }}
        loading={false}
        recordCreatorProps={
          props?.enableEdit && props?.enableAddMaterial
            ? {
                position: 'bottom',
                newRecordType: 'dataSource',
                creatorButtonText: getWord('add-raw-materials'),
                onClick: () => setOpen(true),
                record: () => ({
                  no: `R-${uuidv4().slice(0, 6)}`,
                  smi: '-'
                })
              }
            : false
        }
        columns={
          props?.enableEdit
            ? ([...columns, ...operate()] as ProColumns<Material>[])
            : columns
        }
        value={dataSource}
        editable={{
          ...editableConfig,
          type: 'multiple',
          editableKeys,
          actionRender: (_row, _config, defaultDom) =>
            _row?.role && _row?.role === 'main_reactant'
              ? [defaultDom?.save, defaultDom?.cancel]
              : [defaultDom?.save, defaultDom?.cancel, defaultDom?.delete],
          onSave: async (_rowKey, data) => {
            if (!data.role) return false
            await saveEditItem(data)
          },
          onDelete: async (_rowKey, data) => {
            await handleSaveQuoteData({
              project_reaction_id: projectReactionId,
              material_no: data?.no,
              action: 'delete'
            })
          },
          onChange: setEditableRowKeys
        }}
      />
      <EditorDialog
        open={open}
        init=""
        onClose={async (s: string) => {
          if (s) {
            await getSuppliers(
              s,
              {
                no: `R-${uuidv4().slice(0, 6)}`,
                smi: s
              },
              true
            )
          }
          setOpen(false)
        }}
      />
    </>
  )
}
