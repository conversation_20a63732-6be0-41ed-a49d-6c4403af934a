.editContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  span {
    margin-left: 12px;
    color: red;
  }
  a {
    margin-right: 14px;
    font-size: 16px;
  }
}
.smi {
  // display: none;
  :global {
    .smiles-list {
      .ant-upload-list-picture-card-container,
      .ant-upload-select-picture-card {
        width: 60px;
        height: 60px;
      }
      &.reaction-list .ant-upload-list-picture-card-container {
        width: fit-content;
      }
      .ant-upload-list-picture-card
        .ant-upload-list-item-file
        + .ant-upload-list-item-name {
        display: none !important;
      }
      .add-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }
      &.hide-upload-btn {
        .ant-upload {
          display: none;
        }
      }
    }
  }
}
