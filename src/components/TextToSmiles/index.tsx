import { GetSmilesConfig } from '@/hooks/useKetcher'
import { query } from '@/services/brain'
import { getWord } from '@/utils'
import { App, Input, Space } from 'antd'
import React, { useState } from 'react'

import ButtonWithLoading from '../ButtonWithLoading'

const parseTextToSmiles = async (text: string) => {
  const { data, error } = await query('compounds/search-by-text', {
    params: { query: text }
  }).get()

  if (error) throw error.message
  const smiles = (data as unknown as { smiles: string }).smiles
  if (!smiles) throw getWord('compound-not-exist')
  return smiles
}

export interface TextToSmilesProps {
  value?: string
  onChange?: (value?: string) => void
  onParsed?: (smiles: string) => void
  getSmiles?: (config?: GetSmilesConfig) => Promise<string | false>
}

const TextToSmiles: React.FC<TextToSmilesProps> = ({
  value: propValue,
  onChange,
  onParsed,
  getSmiles
}) => {
  const [text, originSetText] = useState<string | undefined>(propValue)
  const { modal } = App.useApp()
  const [parsing, setParsing] = useState<boolean>(false)
  const setText = (file?: string) => {
    originSetText(file)
    onChange?.(file)
  }

  const parseImage = async (text?: string) => {
    if (!text) return
    setParsing(true)
    try {
      const smiles = await parseTextToSmiles(text)
      const cur = await getSmiles?.({ validate: false })
      if (!cur) {
        setParsing(false)
        onParsed?.(smiles)
        return
      }
      return new Promise((res, rej) => {
        modal.confirm({
          title: getWord('confirm-to-replace'),
          content: getWord('new-molecule-input'),
          onOk: () => {
            onParsed?.(smiles)
            setParsing(false)
            res('')
          },
          onCancel: () => {
            setParsing(false)
            rej()
          }
        })
      })
    } catch (e) {
      setParsing(false)
      modal.error({
        title: getWord('compound-not-exist'),
        content: <>{getWord('try-diff-search')}</>
      })
    }
    setParsing(false)
  }

  return (
    <Space>
      <Input
        style={{ width: '130px' }}
        onChange={(e) => setText(e.target.value || '')}
        allowClear
        value={text}
        disabled={parsing}
      />
      <ButtonWithLoading
        disabled={!text}
        type="primary"
        onClick={() => parseImage(text)}
      >
        {getWord('search-molecule')}
      </ButtonWithLoading>
    </Space>
  )
}

export default TextToSmiles
