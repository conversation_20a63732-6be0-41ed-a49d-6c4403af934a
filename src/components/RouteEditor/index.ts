import { MainTreeForRoute } from '@/pages/route/util'
import { FlexEditor } from './Flex'

export interface GetRouteEvent {
  getRoute?: (route?: MainTreeForRoute) => void
  getIsDirty?: (dirty: boolean) => void
}
export interface UpdateChildrenEvent {
  parent?: string
  children?: MainTreeForRoute[]
  resetStack?: boolean
}

export * from './Flex'
export * from './G6'
export const RouteEditor = FlexEditor
export default FlexEditor
