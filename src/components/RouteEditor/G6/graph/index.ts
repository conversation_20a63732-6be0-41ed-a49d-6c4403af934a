import G6, { GraphOptions, Item, TreeGraph, TreeGraphData } from '@antv/g6'
import { appenAutoShapeListener } from '@antv/g6-react-node'
import { createContext } from 'react'
import { SyntheticTreeGraphData } from '../util'
import './customShape'
import { plugins } from './plugins'

const graghConfig = (
  editMode: boolean,
  expandable?: boolean
): Partial<GraphOptions> => ({
  modes: {
    default: [
      {
        type: 'collapse-expand',
        trigger: 'dblclick',
        onChange: (item, collapsed) => {
          item?.setState('collapsed', !!collapsed)
        }
      },
      'drag-canvas',
      'zoom-canvas'
    ]
  },
  layout: {
    type: 'compactBox',
    direction: 'LR',
    getWidth: () => 400,
    getHeight: () => 300,
    getHGap: () => 50,
    getVGap: () => 50
  },
  animateCfg: { duration: 100 },
  enabledStack: true,
  maxStep: 20,
  minZoom: 0.28,
  // maxZoom: 0.9,
  fitView: true,
  defaultNode: {
    type: 'smiles-node',
    editMode,
    expandable,
    anchorPoints: [
      [0, 0.5],
      [1, 0.5]
    ]
  },
  nodeStateStyles: {
    hover: {
      lineWidth: 4,
      stroke: '#7cc0d8',
      shadowColor: '#7cc0d8',
      back: {
        stroke: '',
        lineWidth: 2,
        shadowColor: '#777'
      }
    },
    select: {
      lineWidth: 4,
      stroke: '#027AFF',
      back: {
        stroke: '',
        lineWidth: 2,
        shadowColor: '#777'
      }
    }
  },
  edgeStateStyles: {
    hover: {
      stroke: '#7cc0d8',
      lineWidth: 4,
      back: {
        stroke: '#333',
        lineWidth: 2
      }
    },
    select: {
      stroke: '#027AFF',
      lineWidth: 4,
      back: {
        stroke: '#333',
        lineWidth: 2
      }
    }
  },
  defaultEdge: {
    type: 'hvh',
    editMode
  },
  plugins
})

export const initGraph = (
  container: HTMLElement,
  data: TreeGraphData,
  {
    onSelectEdge,
    onSelectNode,
    onNodeAddBefore,
    onNodeAddAfter,
    onNodeDelete,
    onNodeExpand
  }: {
    onSelectEdge?: (target: Item | false) => void
    onSelectNode?: (target: Item | false) => void
    onNodeAddBefore?: (target: SyntheticTreeGraphData) => void
    onNodeAddAfter?: (target: SyntheticTreeGraphData) => void
    onNodeDelete?: (target: SyntheticTreeGraphData) => void
    onNodeExpand?: (target: SyntheticTreeGraphData) => void
  } = {},
  editMode?: boolean,
  expandable?: boolean
): TreeGraph => {
  const graph = new G6.TreeGraph({
    ...graghConfig(editMode || false, expandable || false),
    container
  })
  graph.data(data)
  graph.render()
  appenAutoShapeListener(graph as (typeof appenAutoShapeListener)['arguments'])

  graph.on('node:mouseenter', ({ item }) => {
    if (item) graph.setItemState(item, 'hover', true)
  })
  graph.on('node:mouseleave', ({ item }) => {
    if (item) graph.setItemState(item, 'hover', false)
  })
  graph.on('edge:mouseenter', ({ item }) => {
    if (item) graph.setItemState(item, 'hover', true)
  })
  graph.on('edge:mouseleave', ({ item }) => {
    if (item) graph.setItemState(item, 'hover', false)
  })

  graph.on('node:click', ({ item, propagationPath }) => {
    if (item?.getStates().indexOf('select') !== -1) return
    if (propagationPath?.[0]?.cfg?.preventSelect) return

    graph
      .findAllByState('node', 'select')
      .forEach((i) => graph.setItemState(i, 'select', false))
    if (item) graph.setItemState(item, 'select', true)
    onSelectNode?.(item)
  })
  graph.on('edge:click', ({ item }) => {
    /* TODO 路线中具体反应的评论 */
    if (item?.getStates().indexOf('select') !== -1) return
    graph
      .findAllByState('edge', 'select')
      .forEach((i) => graph.setItemState(i, 'select', false))
    if (!item) return
    graph.setItemState(item, 'select', true)
    onSelectEdge?.(item)
  })

  graph.on('canvas:click', () => {
    graph
      .findAllByState('node', 'select')
      .forEach((i) => graph.setItemState(i, 'select', false))

    graph
      .findAllByState('edge', 'select')
      .forEach((i) => graph.setItemState(i, 'select', false))

    onSelectNode?.(false)
    onSelectEdge?.(false)
  })

  graph.on('node:add-before', (e) =>
    onNodeAddBefore?.(e as unknown as SyntheticTreeGraphData)
  )
  graph.on('node:add-after', (e) =>
    onNodeAddAfter?.(e as unknown as SyntheticTreeGraphData)
  )
  graph.on('node:delete', (e) =>
    onNodeDelete?.(e as unknown as SyntheticTreeGraphData)
  )
  graph.on('node:expand-material', (e) => {
    onNodeExpand?.(e as unknown as SyntheticTreeGraphData)
  })
  return graph
}

export const GraphContext = createContext<TreeGraph | null>(null)
