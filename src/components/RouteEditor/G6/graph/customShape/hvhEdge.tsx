import { IContainer } from '@antv/g-base'
import G6, { Item, ModelConfig, ShapeOptions } from '@antv/g6'

const hvhEdge: ShapeOptions = {
  draw: (cfg, group) => {
    const stroke = getStrokeColor(cfg)
    const startPoint = cfg?.startPoint
    const endPoint = cfg?.endPoint
    if (!startPoint || !endPoint || !group) {
      return {}
    }
    return group.addShape('path', {
      attrs: {
        stroke,
        lineAppendWidth: 20,
        path: [
          ['M', startPoint.x, startPoint.y],
          ['L', endPoint.x / 3 + (2 / 3) * startPoint.x, startPoint.y], // 三分之一处
          ['L', endPoint.x / 3 + (2 / 3) * startPoint.x, endPoint.y], // 三分之二处
          ['L', endPoint.x, endPoint.y]
        ]
      },
      name: 'path-shape'
    })
  },

  setState: (name, value, item) => {
    if (!name || !item || typeof value !== 'boolean') return
    switch (name) {
      case 'hover':
      case 'select':
        changeHoverAndSelectState(name, value, item)
        break
      default:
        break
    }
  }
}

G6.registerEdge('hvh', hvhEdge)

const changeHoverAndSelectState = (
  state: 'hover' | 'select',
  value: boolean,
  item: Item
) => {
  const attr = item?.getStateStyle(state)
  const group = item.getKeyShape().getParent()
  const parentId = group.cfg.id?.split(':')?.[0]

  if (!parentId || !attr) return
  if (state === 'hover' && item.getStates().indexOf('select') !== -1) return

  group
    .getParent()
    .findAll((i) => i?.cfg?.id?.startsWith(`${parentId}:`))
    .flatMap((g) => (g as IContainer).findAllByName('path-shape'))
    .forEach((e) => {
      e.attr(value ? attr : attr.back)
      e.attr('stroke', getStrokeColor(item._cfg))
    })
}

const getStrokeColor = (cfg?: ModelConfig): string => {
  return !!cfg?.sourceNode?.getModel?.()?.procedureHasApplied
    ? '#64BC7C'
    : '#333333'
}
