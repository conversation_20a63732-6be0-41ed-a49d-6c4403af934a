import ExpandIcon from '@/assets/images/expand-material.png'
import { smilesDrawer } from '@/components/MoleculeStructure/smilesDrawer'
import { IContainer, ShapeAttrs } from '@antv/g-base'
import G6, {
  G6GraphEvent,
  IGroup,
  INode,
  Item,
  ModelConfig,
  ShapeOptions,
  TreeGraph
} from '@antv/g6'
import { Group, Image, Rect, createNodeFromReact } from '@antv/g6-react-node'

const smilesImageGraghName = 'smiles-image'
const defaltAttrs: Pick<ShapeAttrs, 'height' | 'width'> = {
  height: 200,
  width: 200
}

const cardStyle = { height: 250, width: 250 }
const smilesStyle = { height: 220, width: 220 }

const changeHoverAndSelectState = (
  state: 'hover' | 'select',
  value: boolean,
  item: Item
) => {
  const attr = item?.getStateStyle(state)
  const keyShape = item.getKeyShape() as unknown as IContainer
  const wrapper = keyShape?.findAllByName('wrapper')?.[0]

  if (!wrapper || !attr) return
  if (state === 'hover' && item.getStates().indexOf('select') !== -1) return

  wrapper.attr(value ? attr : attr.back)
  if (state === 'hover') {
    const group = wrapper.getParent().getParent()
    group
      .findAll((i) => i.cfg.displayOnHover)
      .forEach((i) => i.set('visible', value))
    group.show()
  }
}

const changeCollapsedState = (item: Item) => {
  const keyShape = item.getKeyShape() as unknown as IContainer
  const numbers = keyShape.findAllByName('collasped-children-number')
  numbers.forEach((i) => {
    const childNumber = item.getModel()?.children?.length || 0
    i.attr('text', childNumber || '')
    i.set('visible', childNumber !== 0)
  })
}

const changeCollapseOnClick = (
  _: G6GraphEvent,
  item: INode,
  __: INode,
  graph: TreeGraph
) => {
  const sourceData = graph.findDataById(item.get('id'))
  if (!sourceData?.children?.length) return

  const newCollapsed = !sourceData.collapsed
  sourceData.collapsed = newCollapsed
  item.getModel().collapsed = newCollapsed
  graph.emit('itemcollapsed', { item, collapsed: newCollapsed })
  item.setState('collapsed', newCollapsed)
  graph.layout()
}

const displayCollaspedNumber = (cfg: ModelConfig, group: IGroup) => {
  const childNumber = cfg.children?.length

  group.addShape('circle', {
    attrs: {
      r: 20,
      x: cardStyle.width,
      y: cardStyle.height / 2,
      fill: '#fff',
      stroke: '#999',
      lineWidth: 1,
      cursor: 'pointer'
    },
    name: 'collasped-children-number-wrapper',
    visible: !!childNumber,
    preventSelect: true,
    onClick: changeCollapseOnClick
  })
  group.addShape('text', {
    attrs: {
      textAlign: 'center',
      textBaseline: 'middle',
      fontSize: 20,
      x: cardStyle.width,
      y: cardStyle.height / 2,
      fill: '#000',
      text: childNumber,
      cursor: 'pointer'
    },
    preventSelect: true,
    name: 'collasped-children-number',
    visible: !!childNumber,
    onClick: changeCollapseOnClick
  })
}

const displayActionButtons = (cfg: ModelConfig, group: IGroup) => {
  const childNumber = cfg.children?.length
  const y = childNumber ? cardStyle.height / 2 + 50 : cardStyle.height / 2

  group.addShape('circle', {
    attrs: {
      r: 16,
      x: cardStyle.width,
      y,
      fill: '#fff',
      stroke: '#64BC7C',
      lineWidth: 2,
      cursor: 'pointer'
    },
    name: 'add-after-btn-wrapper',
    preventSelect: true,
    // displayOnHover: true,
    visible: (cfg.editMode as boolean) || false,
    onClick: (e, n, c, graph) => graph.emit('node:add-after', cfg)
  })
  group.addShape('text', {
    attrs: {
      textAlign: 'center',
      textBaseline: 'middle',
      fontSize: 32,
      x: cardStyle.width,
      y: y - 3,
      fill: '#64BC7C',
      text: '+',
      cursor: 'pointer'
    },
    name: 'add-after-btn-text',
    preventSelect: true,
    // displayOnHover: true,
    visible: (cfg.editMode as boolean) || false,
    onClick: (e, n, c, graph) => graph.emit('node:add-after', cfg)
  })

  if (!cfg.parent) return

  group.addShape('circle', {
    attrs: {
      r: 16,
      x: 0,
      y: cardStyle.height / 2,
      fill: '#fff',
      stroke: '#64BC7C',
      lineWidth: 2,
      cursor: 'pointer'
    },
    name: 'add-before-btn-wrapper',
    preventSelect: true,
    // displayOnHover: true,
    visible: (cfg.editMode as boolean) || false,
    onClick: (e, n, c, graph) => graph.emit('node:add-before', cfg)
  })
  group.addShape('text', {
    attrs: {
      textAlign: 'center',
      textBaseline: 'middle',
      fontSize: 32,
      x: 0,
      y: cardStyle.height / 2 - 3,
      fill: '#64BC7C',
      text: '+',
      cursor: 'pointer'
    },
    name: 'add-before-btn-text',
    preventSelect: true,
    // displayOnHover: true,
    visible: (cfg.editMode as boolean) || false,
    onClick: (e, n, c, graph) => graph.emit('node:add-before', cfg)
  })

  group.addShape('circle', {
    attrs: {
      r: 16,
      x: cardStyle.width,
      y: 18,
      fill: '#fff',
      stroke: '#FF2222',
      lineWidth: 2,
      cursor: 'pointer'
    },
    name: 'delete-btn-wrapper',
    preventSelect: true,
    // displayOnHover: true,
    visible: (cfg.editMode as boolean) || false,
    onClick: (e, n, c, graph) => graph.emit('node:delete', cfg)
  })
  group.addShape('text', {
    attrs: {
      textAlign: 'center',
      textBaseline: 'middle',
      fontSize: 32,
      x: cardStyle.width,
      y: 16,
      fill: '#FF2222',
      text: '-',
      cursor: 'pointer'
    },
    name: 'delete-btn-text',
    preventSelect: true,
    // displayOnHover: true,
    visible: (cfg.editMode as boolean) || false,
    onClick: (e, n, c, graph) => graph.emit('node:delete', cfg)
  })

  if ((cfg.children as any[])?.length || !cfg.expandable) return

  group.addShape('image', {
    attrs: {
      x: cardStyle.width / 2 - 15,
      y: -20 - 15,
      width: 30,
      height: 30,
      img: ExpandIcon,
      cursor: 'pointer'
    },
    name: 'expand-material-btn-image',
    preventSelect: true,
    // displayOnHover: true,
    visible: (cfg.editMode as boolean) || false,
    onClick: (e, n, c, graph) => graph.emit('node:expand-material', cfg)
  })
}

const getStrokeColor = (cfg?: ModelConfig): string | undefined => {
  return !!cfg?.getModel?.()?.procedureHasApplied ? '#64BC7C' : undefined
}

const initSmilesNode = createNodeFromReact(({ cfg }) => {
  const img = document.createElement('img') as unknown as ImageData
  smilesDrawer.draw(cfg?.value, img)
  const strokeColor = getStrokeColor(cfg)

  return (
    <Group>
      <Rect
        style={{
          width: cardStyle.width,
          height: cardStyle.height,
          fill: cfg.parent ? '#fff' : '#FFCF72',
          stroke: strokeColor,
          shadowColor: '#777',
          shadowBlur: 4,
          shadowOffsetX: 4,
          shadowOffsetY: 4,
          lineWidth: 2,
          radius: 12,
          justifyContent: 'center',
          padding: 8
        }}
        name="wrapper"
      >
        <Rect
          style={{
            margin: 'auto',
            width: smilesStyle.width,
            height: smilesStyle.height,
            stroke: '#888',
            fill: '#fff',
            lineWidth: 1,
            radius: 7,
            justifyContent: 'center',
            padding: 10
          }}
          name="smiles"
        >
          <Image style={{ ...defaltAttrs, img }} name={smilesImageGraghName} />
        </Rect>
      </Rect>
    </Group>
  )
})

const SmilesNode: ShapeOptions = {
  ...initSmilesNode,
  getAnchorPoints: () => [
    [0, 0.5],
    [0.8, 0.5]
  ],
  afterDraw: (cfg, group) => {
    if (!cfg || !group) {
      return
    }
    displayCollaspedNumber(cfg, group)
    if (cfg.editMode) displayActionButtons(cfg, group)
  },
  update: (cfg, item, updateType) => {
    initSmilesNode.update(cfg, item, updateType)
    const group = item.getContainer()
    displayCollaspedNumber(cfg, group)
    changeCollapsedState(item, item.getStates().indexOf('collapsed') !== -1)
    if (cfg.editMode) displayActionButtons(cfg, group)
  },

  setState: (name, value, item) => {
    if (!name || !item) return
    switch (name) {
      case 'hover':
      case 'select':
        changeHoverAndSelectState(name, !!value, item)
        break
      case 'collapsed':
        changeCollapsedState(item, !!value)
        break
      default:
        break
    }
  }
}

G6.registerNode('smiles-node', SmilesNode)
