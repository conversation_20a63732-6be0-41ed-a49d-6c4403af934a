import { SyntheticTree } from '@/types/SyntheticRoute/SyntheticTree'
import { TreeGraphData } from '@antv/g6'

export interface SyntheticTreeGraphData extends TreeGraphData {
  value: string
  parent?: string
  children?: SyntheticTreeGraphData[]
  procedureHasApplied?: boolean
}

export const routeTreeNodeToTreeGraphData = (
  { value, children, parent }: SyntheticTree,
  id?: string,
  procedureAppliedTargets?: Set<string>
): SyntheticTreeGraphData => {
  return {
    id: id || `smiles-node-${Math.random()}`,
    value,
    parent,
    procedureHasApplied: procedureAppliedTargets?.has(value),
    children: children?.map((c) => routeTreeNodeToTreeGraphData(c))
  }
}

export const treeGraphDataToRouteTreeNode = (
  graphData: SyntheticTreeGraphData,
  parent?: string
): SyntheticTree => {
  return {
    value: graphData.value,
    parent: parent || graphData.parent,
    children: graphData.children?.map((c) =>
      treeGraphDataToRouteTreeNode(c, graphData.value)
    )
  }
}

export const addNodeBefore = (
  targetData: SyntheticTreeGraphData,
  addData: SyntheticTree
): SyntheticTreeGraphData => {
  return {
    ...routeTreeNodeToTreeGraphData(
      {
        ...addData,
        children: [treeGraphDataToRouteTreeNode(targetData, addData.value)],
        parent: targetData.parent
      },
      targetData.id
    )
  }
}
