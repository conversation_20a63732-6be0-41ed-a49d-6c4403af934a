import ModalBase from '@/components/ModalBase'
import { useModalBase } from '@/components/ModalBase/useModalBase'
import { EditorDialog } from '@/components/MoleculeEditor'
import { useSmilesEditor } from '@/components/MoleculeEditor/useSmilesEditor'
import { getWord } from '@/utils'
import { TreeGraph } from '@antv/g6'
import { FC, useEffect, useRef, useState } from 'react'
import { useElementSize } from 'usehooks-ts'
import { GetRouteEvent, UpdateChildrenEvent } from '..'
import { GraphContext, initGraph } from './graph'
import './index.less'
import { menuHandler } from './menuHandler'
import { SyntheticTreeGraphData } from './util'

interface G6EditorProps {
  root: SyntheticTreeGraphData
  editMode: boolean
  rightTopSlot?: JSX.Element
  getRouteEvent?: GetRouteEvent
  onSelectRxn?: (target: string | false) => void
  onSelectMolecule?: (target: string | false) => void
  onExpandMolecule?: (target: string | false) => void
  updateChildrenEvent?: UpdateChildrenEvent
  cancelSelectRxnEvent?: Record<never, never>
  cancelSelectMoleculeEvent?: Record<never, never>
}

export const G6Editor: FC<G6EditorProps> = ({
  root,
  editMode,
  rightTopSlot,
  getRouteEvent,
  onSelectRxn,
  onSelectMolecule,
  onExpandMolecule,
  updateChildrenEvent,
  cancelSelectRxnEvent,
  cancelSelectMoleculeEvent
}) => {
  const [elementSizeRef, { width, height }] = useElementSize()

  const ref = useRef<HTMLDivElement | null>(null)
  const { dialogProps, inputSmiles } = useSmilesEditor()
  const { dialogProps: confirmDialogProps, confirm } = useModalBase()
  const [graph, setGraph] = useState<TreeGraph | null>(null)
  const callbacks = useRef<(undefined | ((target: string | false) => void))[]>([
    onSelectRxn,
    onSelectMolecule,
    onExpandMolecule
  ])

  useEffect(() => {
    if (updateChildrenEvent?.children?.length && updateChildrenEvent?.parent) {
      graph?.updateChildren(
        updateChildrenEvent.children,
        updateChildrenEvent.parent
      )
    }
  }, [updateChildrenEvent])

  useEffect(() => {
    if (!ref.current) {
      return
    }
    const graph = initGraph(
      ref?.current,
      root,
      {
        onSelectEdge: (i) => callbacks.current[0]?.(i && i.getID()),
        onSelectNode: (i) => callbacks.current[1]?.(i && i.getID()),
        onNodeExpand: (i) => callbacks.current[2]?.(i && i.id),
        onNodeAddBefore: (i) => menuHandler('addBefore', i, graph, inputSmiles),
        onNodeAddAfter: (i) => menuHandler('addAfter', i, graph, inputSmiles),
        onNodeDelete: (i) => menuHandler('deleteRoute', i, graph, confirm)
      },
      editMode,
      !!onExpandMolecule
    )
    setGraph(graph)
  }, [])

  useEffect(() => {
    graph?.getNodes().forEach((node) => graph?.updateItem(node, { editMode }))
  }, [editMode])

  useEffect(() => {
    graph?.changeData(root)
  }, [root])

  useEffect(() => {
    callbacks.current[0] = onSelectRxn
    callbacks.current[1] = onSelectMolecule
  }, [onSelectRxn, onSelectMolecule])

  useEffect(() => {
    graph
      ?.findAllByState('edge', 'select')
      .forEach((i) => graph.setItemState(i, 'select', false))
  }, [cancelSelectRxnEvent])

  useEffect(() => {
    graph
      ?.findAllByState('node', 'select')
      .forEach((i) => graph.setItemState(i, 'select', false))
  }, [cancelSelectMoleculeEvent])

  useEffect(() => {
    graph?.changeSize(width, height)
    // TODO current this will only be triggered when change size of windiw, not when the page changed, like collaspe sidebar
  }, [width, height, graph])

  useEffect(() => {
    const route = graph?.save() as SyntheticTreeGraphData
    getRouteEvent?.getIsDirty?.(
      !!(graph?.getRedoStack().length || graph?.getUndoStack().length)
    )
    getRouteEvent?.getRoute?.(route)
  }, [getRouteEvent])

  return (
    <GraphContext.Provider value={graph}>
      <div
        className="route-editor-root"
        ref={(r) => {
          ref.current = r
          elementSizeRef(r)
        }}
      >
        {rightTopSlot && (
          <div className="right-top-slot-wrapper">{rightTopSlot}</div>
        )}
      </div>
      <EditorDialog {...dialogProps} />
      <ModalBase
        {...confirmDialogProps}
        title={getWord('confirm-to-del')}
        okType="danger"
      >
        {getWord('confirm-delete-molecule-route')}
      </ModalBase>
    </GraphContext.Provider>
  )
}
