import { exhaustiveGuard } from '@/utils/codeGuard'
import { INode, TreeGraph } from '@antv/g6'
import {
  addNodeBefore,
  routeTreeNodeToTreeGraphData,
  SyntheticTreeGraphData
} from './util'

const allMenuKeys = ['deleteRoute', 'addBefore', 'addAfter'] as const
type MenuKey = (typeof allMenuKeys)[number]

export const menuHandler = async (
  key: <PERSON>u<PERSON><PERSON>,
  treeData: SyntheticTreeGraphData,
  graph: TreeGraph,
  confirmInput: () => Promise<string>
): Promise<void> => {
  const node = graph.findById(treeData.id) as INode
  const parentNode = graph.getNeighbors(node, 'source')[0]
  const parentData = parentNode?.getModel() as SyntheticTreeGraphData
  switch (key) {
    case 'deleteRoute':
      await confirmInput()
      graph.removeChild(treeData.id, true)
      break
    case 'addBefore': {
      if (!parentData?.id) return
      const newSmiles = await confirmInput()
      const updatedChild = addNodeBefore(treeData, { value: newSmiles })
      graph.updateChild(updatedChild, parentData.id, true)
      graph.refreshItem(treeData.id)
      break
    }
    case 'addAfter': {
      const newSmiles = await confirmInput()
      const addedChild = routeTreeNodeToTreeGraphData({
        value: newSmiles,
        parent: treeData.id
      })
      graph.addChild(addedChild, treeData.id, true)
      break
    }
    default:
      exhaustiveGuard(key)
      break
  }
}
