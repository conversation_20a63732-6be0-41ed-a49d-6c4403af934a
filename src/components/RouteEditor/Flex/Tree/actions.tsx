import { ReactComponent as AddAfterIcon } from '@/assets/svgs/route-operation/add-after.svg'
import { ReactComponent as AddBeforeIcon } from '@/assets/svgs/route-operation/add-before.svg'
import { ReactComponent as CollapseIcon } from '@/assets/svgs/route-operation/collapse.svg'
import { ReactComponent as CopyCasIcon } from '@/assets/svgs/route-operation/copy-cas.svg'
import { ReactComponent as CopyMaterialIcon } from '@/assets/svgs/route-operation/copy-material.svg'
import { ReactComponent as CopyRouteIcon } from '@/assets/svgs/route-operation/copy-route.svg'
import { ReactComponent as DeleteIcon } from '@/assets/svgs/route-operation/delete.svg'
import { ReactComponent as ExpandIcon } from '@/assets/svgs/route-operation/expand.svg'
import { ReactComponent as SaveAsBuildingBlockIcon } from '@/assets/svgs/route-operation/save-bb.svg'
import { ReactComponent as SearchRouteIcon } from '@/assets/svgs/route-operation/search-route.svg'
import { ReactComponent as ZoomInIcon } from '@/assets/svgs/route-operation/zoom-in.svg'
import { getWord } from '@/utils'

const materialActions = [
  'copyMaterial',
  'copyRoute',
  'expandMaterial',
  'delete',
  'searchRoute',
  'saveAsBuildingBlock',
  'expandRoute',
  'collapseRoute',
  'copyCas',
  'addBefore',
  'addComp',
  'addAfter'
] as const
export type MaterialActionType = (typeof materialActions)[number]

interface MaterialAction {
  icon: React.FC
  description: string
  order: number
}

export const actionConfigs: Record<MaterialActionType, MaterialAction> = {
  expandMaterial: {
    icon: ZoomInIcon,
    order: 1,
    description: getWord('enlarge-molecule')
  },
  copyMaterial: {
    icon: CopyMaterialIcon,
    order: 2,
    description: getWord('copy-molecule')
  },
  copyRoute: {
    icon: CopyRouteIcon,
    order: 3,
    description: getWord('copy-molecule-route')
  },
  saveAsBuildingBlock: {
    order: 4,
    icon: SaveAsBuildingBlockIcon,
    description: getWord('save-build-block')
  },
  searchRoute: {
    order: 5,
    icon: SearchRouteIcon,
    description: getWord('fetch-target-molecule')
  },
  delete: {
    icon: DeleteIcon,
    order: 6,
    description: getWord('del-molecule-routes')
  },
  expandRoute: {
    icon: ExpandIcon,
    order: 7,
    description: getWord('expand-route')
  },
  collapseRoute: {
    icon: CollapseIcon,
    order: 8,
    description: getWord('collapse-route')
  },
  copyCas: {
    icon: CopyCasIcon,
    order: 9,
    description: getWord('copy-cas')
  },
  addBefore: {
    icon: AddBeforeIcon,
    order: 9,
    description: getWord('add-intermediate')
  },
  addComp: {
    icon: AddBeforeIcon,
    order: 10,
    description: getWord('add-raw-materials')
  },
  addAfter: {
    icon: AddAfterIcon,
    order: 11,
    description: getWord('add-molecule-in-route-tip')
  }
}

export const getActions = (
  authCodeList: string[],
  config: {
    hasChildren?: boolean
    hasParent?: boolean
    editMode?: boolean
  }
): MaterialActionType[] => {
  const { hasParent, editMode } = config
  const actions: MaterialActionType[] = [
    'expandMaterial',
    'copyMaterial',
    'copyCas'
  ]
  if (editMode) {
    if (hasParent && authCodeList.includes('view-by-backbone.searchRoute')) {
      actions.push('searchRoute')
    }
    if (hasParent && authCodeList.includes('view-by-backbone.delMolecule')) {
      actions.push('delete')
    }
  }
  return actions
}
