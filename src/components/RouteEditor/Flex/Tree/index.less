.tree-wrapper {
  --line-color: #c3c8d0;
  --node-padding: 18px;
  --node-width: 120px;
  --node-height: 120px;
  &.zoom-1 {
    --node-width: 80px;
    --node-height: 80px;
  }
  &.zoom-2 {
    --node-width: 100px;
    --node-height: 100px;
  }
  &.zoom-4 {
    --node-width: 190px;
    --node-height: 190px;
  }
  &.zoom-5 {
    --node-width: 280px;
    --node-height: 280px;
  }
  position: relative;
  flex-direction: row;
  align-items: center;
  margin-right: var(--node-padding);
  padding-left: var(--node-padding);
  &.root {
    min-height: 100%;
    margin-top: 20px;
  }

  .action-buttons {
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    cursor: pointer;
    > svg {
      background: white;
    }
    &.copy-cas-button {
      .fetching-tip {
        position: absolute;
      }
      &.no-cas,
      &.fetching {
        background: #f5f5f5;
        > svg {
          background: #f5f5f5;
          > path {
            fill: #c2c2c2;
          }
        }
      }
    }
  }

  .smiles-wrapper {
    flex: none;
    width: var(--node-width);
    max-width: unset;
    height: var(--node-height);
    max-height: unset;
    margin: 20px 0;
    padding: 8px;
    border: 3px #cce4ff solid;
    border-radius: 2px;
    &.molecule {
      margin-right: 800px;
      border-color: #979797;
      .add-after-button {
        right: -12px;
      }
    }
    &.target {
      border-color: #faad14;
      .add-before-button {
        display: none;
      }
    }
    &.expanding {
      background: linear-gradient(90deg, #cce4ff 50%, transparent 0) repeat-x,
        linear-gradient(90deg, #cce4ff 50%, transparent 0) repeat-x,
        linear-gradient(0deg, #cce4ff 50%, transparent 0) repeat-y,
        linear-gradient(0deg, #cce4ff 50%, transparent 0) repeat-y;
      background-position: 0 0, 0 100%, 0 0, 100% 0;
      background-size: 6px 3px, 6px 3px, 3px 6px, 3px 6px;
      border: none;
      animation: linearGradientMove 0.3s infinite linear;

      @keyframes linearGradientMove {
        100% {
          background-position: 6px 0, -6px 100%, 0 -6px, 100% 6px;
        }
      }
    }

    .compound-actions-wrapper {
      svg {
        width: 16px;
        height: 16px;
      }
      position: absolute;
      top: -30px;
      left: -4px;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: center;
      width: fit-content;
      height: 24px;
      background: white;
      box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.6);
    }
    .fold-button {
      position: absolute;
      top: calc(50% - 10px);
      right: -10px;
      z-index: 1;
    }
    .add-before-button,
    .add-after-button {
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto 0;
      > svg path {
        fill: #027aff;
      }
    }
    .add-before-button {
      left: -30px;
    }
    .add-after-button {
      right: -32px;
    }
  }
  .children-wrapper {
    display: contents;
    &.hide * {
      width: 0;
      height: 0;
      opacity: 0;
      pointer-events: none;
    }
  }
  .to-parent-line-wrapper {
    &::before {
      position: absolute;
      top: 50%;
      right: 0;
      width: var(--node-padding);
      height: 2px;
      border-top: 1px solid var(--line-color);
      content: '';
    }
  }
  .to-children-line-wrapper {
    max-width: unset;
    padding-right: var(--node-padding);
    padding-left: 36px;
    .arrow {
      position: absolute;
      top: -1px;
      left: -1px;
      display: flex;
      align-items: center;
      height: 100%;
      color: var(--line-color);
    }
    .yields-tag-wrapper {
      position: absolute;
      bottom: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: calc(100% - 36px - 18px);
      .yields-tag {
        background: #e5edf8;
      }
    }
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 2px;
      border-top: 1px solid var(--line-color);
      content: '';
    }
  }

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 100%;
    border-left: 1px solid var(--line-color);
    content: '';
  }
  &:first-child::before {
    top: 50%;
    height: 50%;
  }
  &:last-child::before {
    bottom: 50%;
    height: 50%;
  }
  &:first-child:last-child::before {
    content: none;
  }
}
