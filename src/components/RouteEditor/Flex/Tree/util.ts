import normalizeMainTree, {
  MainTreeForRoute,
  findNodeById
} from '@/pages/route/util'

export const deleteNode = (
  tree: MainTreeForRoute,
  id: string
): MainTreeForRoute => {
  const target = findNodeById(tree, id)
  const parent = findNodeById(tree, target?.parent)
  const index = parent?.children?.findIndex((c) => c.id === id)
  if (index !== -1 && index !== undefined && parent?.children?.[index]) {
    parent.children.splice(index, 1)
  }
  return tree
}

export const addBefore = (
  tree: MainTreeForRoute,
  id: string,
  smiles: string
): MainTreeForRoute => {
  const target = findNodeById(tree, id)
  const parent = findNodeById(tree, target?.parent)
  const index = parent?.children?.findIndex((c) => c.id === id)
  if (index !== -1 && index !== undefined && parent?.children?.[index]) {
    parent.children[index] = normalizeMainTree(
      { value: smiles, children: [parent.children[index]] },
      parent.id
    )
  }
  return tree
}

export const addAfter = (
  tree: MainTreeForRoute,
  id: string,
  smiles: string
): MainTreeForRoute => {
  const target = findNodeById(tree, id)
  if (target) {
    if (!target.children?.length) {
      target.children = []
    }
    target.children.splice(
      0,
      Number.MAX_SAFE_INTEGER,
      normalizeMainTree({ value: smiles, children: target.children }, id)
    )
  }
  return tree
}

export const addComp = (
  tree: MainTreeForRoute,
  id: string,
  smiles: string
): MainTreeForRoute => {
  const target = findNodeById(tree, id)
  if (target) {
    if (!target.children?.length) {
      target.children = []
    }
    target.children.splice(
      Number.MAX_SAFE_INTEGER,
      0,
      normalizeMainTree({ value: smiles, children: [] }, id)
    )
  }
  return tree
}

export const updateChildren = (
  tree: MainTreeForRoute,
  parentId: string,
  children: MainTreeForRoute[]
): MainTreeForRoute => {
  const target = findNodeById(tree, parentId)
  if (target) {
    target.children?.splice(0, Number.MAX_SAFE_INTEGER, ...children)
  }
  return tree
}
