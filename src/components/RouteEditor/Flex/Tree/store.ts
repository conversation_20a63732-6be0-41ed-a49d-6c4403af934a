import {
  MainTreeForRoute,
  findNodeById,
  getNameOfReactionMap
} from '@/pages/route/util'
import { isEqual } from 'lodash'
import { create } from 'zustand'
import { combine, subscribeWithSelector } from 'zustand/middleware'

const moleculeActions = [
  'addBefore',
  'addAfter',
  'addComp',
  'delete',
  'searchRoute',
  'saveAsBuildingBlock'
] as const
export type MoleculeActionType = (typeof moleculeActions)[number]

interface State {
  route?: MainTreeForRoute
  reactionNameMap: Map<string, string>
  foldNodeIds: Set<string>
  editMode?: boolean
  selectedNode?: string
  expandingNodeId?: string
  selectedReaction?: string
  expandingNode?: string
  actionEvent?: { action: MoleculeActionType; id: string }
  undoRedoStack: { undo: MainTreeForRoute[]; redo: MainTreeForRoute[] }
  exportFileName?: string
}

const initState: State = {
  reactionNameMap: new Map(),
  foldNodeIds: new Set(),
  undoRedoStack: { undo: [], redo: [] }
}

const update = (route: MainTreeForRoute, state: State): State => {
  const oldRoute = state.route
  const newStack = state.undoRedoStack
  if (oldRoute && !isEqual(oldRoute, route)) {
    newStack.undo.push(oldRoute)
    newStack.redo = []
  }
  return {
    ...state,
    route,
    reactionNameMap: getNameOfReactionMap(route),
    undoRedoStack: newStack
  }
}
const undo = (state: State): State => {
  const { undo, redo } = state.undoRedoStack
  if (undo.length && state.route) {
    const poped = undo.pop()
    return {
      ...state,
      route: poped,
      reactionNameMap: getNameOfReactionMap(poped),
      undoRedoStack: { undo, redo: [...redo, state.route] }
    }
  }
  return state
}
const redo = (state: State): State => {
  const { undo, redo } = state.undoRedoStack
  if (redo.length && state.route) {
    const poped = redo.pop()
    return {
      ...state,
      route: poped,
      reactionNameMap: getNameOfReactionMap(poped),
      undoRedoStack: { undo: [...undo, state.route], redo }
    }
  }
  return state
}
const reset = (route: MainTreeForRoute, editMode: boolean): State => ({
  route,
  reactionNameMap: getNameOfReactionMap(route),
  foldNodeIds: new Set(),
  editMode,
  undoRedoStack: { undo: [], redo: [] }
})

export const useRouteTreeStore = create(
  subscribeWithSelector(
    combine({ ...initState }, (set, get) => ({
      reset: (route: MainTreeForRoute, editMode?: boolean) =>
        set(reset(route, editMode || false)),
      update: (route: MainTreeForRoute) => set((state) => update(route, state)),
      undo: () => set(undo),
      redo: () => set(redo),
      getReactionName: (id: string) => get().reactionNameMap.get(id),
      getById: (id: string) => findNodeById(get().route, id),
      isFolded: (id: string) => get().foldNodeIds.has(id),
      toggleFold: (id: string, fold?: boolean) => {
        const { foldNodeIds } = get()
        const toFold = fold === undefined ? !foldNodeIds.has(id) : fold
        if (toFold) {
          foldNodeIds.add(id)
        } else {
          foldNodeIds.delete(id)
        }
        set((s) => ({ ...s, foldNodeIds }))
      },
      setExpandingNode: (id?: string) =>
        set((s) => ({ ...s, expandingNode: id })),
      selectNode: (id?: string) =>
        set((state) => ({ ...state, selectedNode: id })),
      selectReaction: (id?: string) =>
        set((state) => ({ ...state, selectedReaction: id })),
      setEditMode: (editMode: boolean) =>
        set((state) => ({ ...state, editMode })),
      setExportFileName: (exportFileName: string) =>
        set((state) => ({ ...state, exportFileName })),
      setExpandingNodeId: (expandingNodeId?: string) =>
        set((state) => ({ ...state, expandingNodeId })),
      triggerAction: (action: MoleculeActionType, id: string) =>
        set((state) => ({ ...state, actionEvent: { action, id } })),
      resetStack: () =>
        set((state) => ({ ...state, undoRedoStack: { undo: [], redo: [] } }))
    }))
  )
)
