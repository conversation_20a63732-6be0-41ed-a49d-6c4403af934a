import MoleculeStructure from '@/components/MoleculeStructure'
import { useCopyToClipboard } from '@/components/MoleculeStructure/util'
import { useSmilesToCas } from '@/hooks/useSmilesToCas'
import { MainTreeForRoute, getRxnFromTree } from '@/pages/route/util'
import { getWord } from '@/utils'
import { DownOutlined, LoadingOutlined } from '@ant-design/icons'
import { useAccess } from '@umijs/max'
import {
  Button,
  Col,
  Dropdown,
  DropdownProps,
  Popover,
  Row,
  Space,
  Spin,
  Tag,
  Tooltip
} from 'antd'
import classNames from 'classnames'
import { sortBy } from 'lodash'
import React, { useState } from 'react'
import { MaterialActionType, actionConfigs, getActions } from './actions'
import './index.less'
import { useRouteTreeStore } from './store'

export interface TwoWayAddButtonProps {
  root: MainTreeForRoute
  action: 'addBefore' | 'addAfter'
}

const TwoWayAddButton: React.FC<TwoWayAddButtonProps> = ({
  action,
  root,
  children
}) => {
  const [open, setOpen] = useState<boolean>(false)
  const [triggerAction] = useRouteTreeStore((s) => [s.triggerAction])
  const access = useAccess()
  return (
    <Popover
      overlayInnerStyle={{ padding: 0 }}
      open={open}
      onOpenChange={setOpen}
      placement="top"
      style={{ padding: 0 }}
      content={
        <>
          {((action === 'addBefore' &&
            access?.authCodeList?.includes('view-by-backbone.addBefore')) ||
            action !== 'addBefore') && (
            <Button
              type="link"
              onClick={() => {
                triggerAction(action, root.id)
                setOpen(false)
              }}
            >
              {actionConfigs.addBefore.description}
            </Button>
          )}
          {access?.authCodeList?.includes('view-by-backbone.addComp') && (
            <Button
              type="link"
              onClick={() => {
                triggerAction(
                  'addComp',
                  action === 'addAfter' ? root.id : root.parent || ''
                )
                setOpen(false)
              }}
            >
              {actionConfigs.addComp.description}
            </Button>
          )}
        </>
      }
      arrow={false}
    >
      {children}
    </Popover>
  )
}

export interface TreeProps {
  root: MainTreeForRoute
  hasParent?: boolean
  zoom?: number
  compoundRef?: React.Ref<HTMLDivElement>
  rxnYieldMap?: Record<string, number>
}

const Tree: React.FC<TreeProps> = ({
  root,
  hasParent,
  zoom = 3,
  rxnYieldMap,
  compoundRef
}) => {
  const [
    editMode,
    getReactionName,
    isFolded,
    toggleFold,
    setExpandingNode,
    selectReaction,
    selectedReaction,
    triggerAction,
    expandingNodeId
  ] = useRouteTreeStore((s) => [
    s.editMode,
    s.getReactionName,
    s.isFolded,
    s.toggleFold,
    s.setExpandingNode,
    s.selectReaction,
    s.selectedReaction,
    s.triggerAction,
    s.expandingNodeId
  ])

  const access = useAccess()
  const hasChildren = !!root.children?.length && root.children.length > 0
  const { copy } = useCopyToClipboard()
  const actions = getActions(access?.authCodeList as string[], {
    editMode,
    hasParent,
    hasChildren
  })

  const { map, add } = useSmilesToCas()
  const [fetchingCas, setFetchingCas] = useState<boolean>(false)
  const folded = isFolded(root.id)
  const foldButtonConfig =
    actionConfigs[folded ? 'expandRoute' : 'collapseRoute']

  const reactionActions: Required<DropdownProps>['menu']['items'] = [
    {
      label: getWord('view-reaction-lib'),
      key: 'select',
      onClick: () => selectReaction(root.id)
    },
    {
      label: getWord('copy-reaction'),
      key: 'copy',
      onClick: () => copy(getRxnFromTree(root))
    }
  ]

  const stepYield = rxnYieldMap?.[getRxnFromTree(root, true)]
  const showYield = rxnYieldMap !== undefined

  const childrenDom = (
    <div
      className={classNames('children-wrapper', {
        hide: !hasChildren || folded
      })}
    >
      <Col className="to-children-line-wrapper">
        {showYield && (
          <div className="yields-tag-wrapper">
            <Tag bordered={false} className="yields-tag">
              {getWord('yield')}
              {stepYield || '-'}%
            </Tag>
          </div>
        )}
        {access?.authCodeList?.includes(
          'view-by-backbone.button.reactionDropdown'
        ) ? (
          <Dropdown
            menu={{
              items: reactionActions
            }}
          >
            <Button
              type={selectedReaction === root.id ? 'primary' : 'default'}
              onClick={() => selectReaction(root.id)}
            >
              <Space>
                {getReactionName(root.id)}
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        ) : (
          <Button type={selectedReaction === root.id ? 'primary' : 'default'}>
            <Space>{getReactionName(root.id)}</Space>
          </Button>
        )}
      </Col>
      <Col>
        {root.children?.map((r) => (
          <Tree
            root={r}
            key={r.id}
            hasParent
            zoom={zoom}
            rxnYieldMap={rxnYieldMap}
          />
        ))}
      </Col>
    </div>
  )

  const addButtons = (
    <>
      <TwoWayAddButton root={root} action="addBefore">
        <div
          onClick={() => triggerAction('addBefore', root.id)}
          className="action-buttons add-before-button reaction-buttons"
        >
          {actionConfigs.addBefore.icon({})}
        </div>
      </TwoWayAddButton>
      {!folded && !!root.children?.length && root.children.length > 1 && (
        <TwoWayAddButton root={root} action="addAfter">
          <div
            onClick={() => triggerAction('addAfter', root.id)}
            className="action-buttons add-after-button reaction-buttons"
          >
            {actionConfigs.addAfter.icon({})}
          </div>
        </TwoWayAddButton>
      )}
      {!folded && !root.children?.length && (
        <Tooltip title={actionConfigs.addAfter.description}>
          <div
            onClick={() => triggerAction('addAfter', root.id)}
            className="action-buttons add-after-button reaction-buttons"
          >
            {actionConfigs.addAfter.icon({})}
          </div>
        </Tooltip>
      )}
    </>
  )

  const handlers: Partial<
    Record<MaterialActionType, (node: MainTreeForRoute) => void>
  > = {
    copyMaterial: (node) => copy(node.value),
    copyRoute: (node) => copy(JSON.stringify(node)),
    expandMaterial: (node) => setExpandingNode(node.id),
    delete: (node) => triggerAction('delete', node.id),
    searchRoute: (node) => triggerAction('searchRoute', node.id),
    saveAsBuildingBlock: (node) =>
      triggerAction('saveAsBuildingBlock', node.id),
    copyCas: async (node) => {
      const cas = map[node.value]
      if (cas) {
        copy(cas, `CAS: ${cas}, ${getWord('copy-to-clipboard')}`)
        return
      }
      if (cas === '') return
      setFetchingCas(true)
      const [fetched] = await add([node.value]).finally(() =>
        setFetchingCas(false)
      )
      if (fetched?.length) {
        copy(fetched, `CAS: ${fetched}, ${getWord('copy-to-clipboard')}`)
      }
    }
  }

  return (
    <Row
      wrap={false}
      className={classNames('tree-wrapper', `zoom-${zoom}`, {
        root: !hasParent
      })}
    >
      {hasParent && <Col className="to-parent-line-wrapper"></Col>}
      <Col
        className={classNames('smiles-wrapper', {
          target: !hasParent,
          molecule: !root.children?.length,
          expanding: expandingNodeId === root.id
        })}
        ref={compoundRef}
      >
        <Row className="compound-actions-wrapper">
          {sortBy(
            actions.map((action) => ({ action, ...actionConfigs[action] })),
            'order'
          ).map(({ action, description, icon }) => {
            if (action === 'copyCas') {
              const cas = map[root.value]
              const status =
                cas === undefined
                  ? 'to-fetch'
                  : cas.length
                  ? 'has-cas'
                  : 'no-cas'
              return (
                <Tooltip
                  key={action}
                  title={
                    status === 'has-cas'
                      ? `CAS: ${cas}, ${getWord('click-icon-to-copy')}`
                      : status === 'no-cas'
                      ? getWord('cas-not-found')
                      : getWord('click-to-find-cas')
                  }
                >
                  <div
                    onClick={() => handlers[action]?.(root)}
                    className={classNames(
                      'action-buttons',
                      'copy-cas-button',
                      status,
                      { fetching: fetchingCas }
                    )}
                  >
                    {fetchingCas && (
                      <Spin
                        className="fetching-tip"
                        indicator={
                          <LoadingOutlined style={{ fontSize: 16 }} spin />
                        }
                      />
                    )}
                    {icon({})}
                  </div>
                </Tooltip>
              )
            }
            return (
              <Tooltip key={action} title={description}>
                <div
                  onClick={() => handlers[action]?.(root)}
                  className="action-buttons"
                >
                  {icon({})}
                </div>
              </Tooltip>
            )
          })}
        </Row>
        <MoleculeStructure
          structure={root.value}
          copyBtn={false}
          expandBtn={false}
        />
        {hasChildren && (
          <Tooltip title={foldButtonConfig.description}>
            <div
              onClick={() => toggleFold(root.id)}
              className="action-buttons fold-button reaction-buttons"
            >
              {foldButtonConfig.icon({})}
            </div>
          </Tooltip>
        )}
        {editMode && addButtons}
      </Col>
      {childrenDom}
    </Row>
  )
}

export default Tree
