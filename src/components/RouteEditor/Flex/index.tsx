import ModalBase from '@/components/ModalBase'
import { useModalBase } from '@/components/ModalBase/useModalBase'
import { EditorDialog, useSmilesEditor } from '@/components/MoleculeEditor'
import MoleculeStructure from '@/components/MoleculeStructure'
import { MainTreeForRoute } from '@/pages/route/util'
import {
  MinusOutlined,
  PlusOutlined,
  RedoOutlined,
  UndoOutlined
} from '@ant-design/icons'
import { Button, Space, Tooltip } from 'antd'
import { cloneDeep } from 'lodash'
import React, { useEffect, useRef, useState } from 'react'
import useDraggableScroll from 'use-draggable-scroll'

import { getWord } from '@/utils'
import { GetRouteEvent, UpdateChildrenEvent } from '..'
import Tree from './Tree'
import { MoleculeActionType, useRouteTreeStore } from './Tree/store'
import {
  addAfter,
  addBefore,
  addComp,
  deleteNode,
  updateChildren
} from './Tree/util'
import './index.less'

export interface FlexEditorProps {
  root: MainTreeForRoute
  editMode: boolean
  onSelectRxn?: (
    target: string | false,
    tree?: MainTreeForRoute
  ) => Promise<void>
  selectRxnEvent?: { select?: string }
  onExpandMolecule?: (target: string | false) => void
  expandingNodeId?: string
  getRouteEvent?: GetRouteEvent
  updateChildrenEvent?: UpdateChildrenEvent
  rightTopSlot?: JSX.Element
  rxnYieldMap?: Record<string, number>
  onUndoLengthChange?: (l: number) => void
  onTreeChange?: (t: MainTreeForRoute) => void
}

export const FlexEditor: React.FC<FlexEditorProps> = ({
  root,
  editMode,
  onSelectRxn,
  selectRxnEvent,
  expandingNodeId,
  onExpandMolecule,
  rightTopSlot,
  updateChildrenEvent,
  getRouteEvent,
  rxnYieldMap,
  onUndoLengthChange,
  onTreeChange
}) => {
  const [
    reset,
    update,
    route,
    setEditMode,
    expandingNode,
    setExpandingNode,
    getById,
    selectReaction,
    undo,
    redo,
    stack,
    setExpandingNodeId,
    resetStack
  ] = useRouteTreeStore((s) => [
    s.reset,
    s.update,
    s.route,
    s.setEditMode,
    s.expandingNode,
    s.setExpandingNode,
    s.getById,
    s.selectReaction,
    s.undo,
    s.redo,
    s.undoRedoStack,
    s.setExpandingNodeId,
    s.resetStack
  ])

  const [zoom, setZoom] = useState<number>(3)
  const [inited, setInited] = useState<boolean>(false)
  const containerRef =
    useRef<HTMLDivElement>() as React.MutableRefObject<HTMLInputElement>
  const { onMouseDown } = useDraggableScroll(containerRef)
  const ref = useRef<HTMLDivElement>()
  const { dialogProps, confirm } = useModalBase()
  const { dialogProps: editDialogProps, inputSmiles } = useSmilesEditor()
  const { dialogProps: confirmDeleteDialogProps, confirm: confirmDelete } =
    useModalBase()

  const handleActions = async (id: string, action: MoleculeActionType) => {
    switch (action) {
      case 'addBefore':
        if (route) update(addBefore(cloneDeep(route), id, await inputSmiles()))
        break
      case 'addAfter':
        if (route) update(addAfter(cloneDeep(route), id, await inputSmiles()))
        break
      case 'addComp':
        if (route) update(addComp(cloneDeep(route), id, await inputSmiles()))
        break
      case 'delete':
        await confirmDelete()
        if (route) update(deleteNode(cloneDeep(route), id))
        break
      case 'searchRoute':
        onExpandMolecule?.(id)
        break
      default:
        break
    }
  }

  useRouteTreeStore.subscribe(
    (s) => s.expandingNode,
    (n) => n && confirm()
  )
  useEffect(() => {
    useRouteTreeStore.subscribe(
      (s) => s.route,
      (n) => n && onTreeChange?.(n)
    )
  }, [onTreeChange])
  useEffect(() => {
    useRouteTreeStore.subscribe(
      (s) => s.selectedReaction,
      (n) => onSelectRxn?.(n || false, route)
    )
  }, [onSelectRxn])
  useRouteTreeStore.subscribe(
    (s) => s.actionEvent,
    (e) => e && handleActions(e.id, e.action)
  )

  useEffect(() => {
    onUndoLengthChange?.(stack.undo.length)
  }, [stack.undo.length])
  useEffect(() => reset(root, editMode), [root])
  useEffect(() => setEditMode(editMode), [editMode])
  useEffect(() => selectReaction(selectRxnEvent?.select), [selectRxnEvent])
  useEffect(
    () => setExpandingNodeId(expandingNodeId?.split('::')?.[0]),
    [expandingNodeId]
  )
  useEffect(() => {
    getRouteEvent?.getIsDirty?.(!!(stack.undo.length || stack.redo.length))
    getRouteEvent?.getRoute?.(route)
  }, [getRouteEvent])
  useEffect(() => {
    if (route && !inited) {
      setInited(true)
      ref.current?.scrollIntoView({ block: 'center', inline: 'nearest' })
    }
  }, [route])
  useEffect(() => {
    if (route && updateChildrenEvent?.parent && updateChildrenEvent.children)
      update(
        updateChildren(
          cloneDeep(route),
          updateChildrenEvent?.parent,
          updateChildrenEvent?.children
        )
      )
    if (updateChildrenEvent?.resetStack) {
      resetStack()
    }
  }, [updateChildrenEvent])

  return (
    <div className="flex-editor-root">
      <div
        className="flex-editor-wrapper"
        onMouseDown={onMouseDown}
        ref={containerRef}
      >
        {route && (
          <Tree
            root={route}
            zoom={zoom}
            rxnYieldMap={rxnYieldMap}
            compoundRef={(r) => (ref.current = r || undefined)}
          />
        )}
      </div>

      <Space.Compact className="left-top-wrapper">
        {editMode && (
          <Tooltip title={getWord('undo')}>
            <Button
              icon={<UndoOutlined />}
              disabled={!stack.undo.length}
              onClick={undo}
            />
          </Tooltip>
        )}
        {editMode && (
          <Tooltip title={getWord('redo')}>
            <Button
              icon={<RedoOutlined />}
              disabled={!stack.redo.length}
              onClick={redo}
            />
          </Tooltip>
        )}
      </Space.Compact>

      <Space.Compact className="right-bottom-wrapper" direction="vertical">
        <Button
          icon={<PlusOutlined />}
          disabled={zoom >= 5}
          onClick={() => setZoom((o) => Math.min(o + 1, 5))}
        />
        <Button
          icon={<MinusOutlined />}
          disabled={zoom <= 1}
          onClick={() => setZoom((o) => Math.max(o - 1, 1))}
        />
      </Space.Compact>

      <div className="right-top-slot-wrapper">{rightTopSlot}</div>

      <EditorDialog {...editDialogProps} />

      <ModalBase
        {...confirmDeleteDialogProps}
        title={getWord('confirm-to-del')}
        okType="danger"
      >
        {getWord('confirm-delete-synthesis-route')}
      </ModalBase>

      <ModalBase
        {...dialogProps}
        footer={null}
        cancelButtonProps={{ hidden: true }}
        width={800}
        centered
        afterClose={async () => setExpandingNode('')}
      >
        <MoleculeStructure
          structure={getById(expandingNode || '')?.value || ''}
          copyBtn={false}
          expandBtn={false}
        />
      </ModalBase>
    </div>
  )
}
