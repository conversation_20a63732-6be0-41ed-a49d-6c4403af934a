import React from 'react'
import { DatePicker } from 'antd'
import { SwapRightOutlined } from '@ant-design/icons'
import dayjs, { Dayjs } from 'dayjs'
function Range({ value = [], onChange, placeholder = [] }) {
  function onChangeDate(e: Dayjs, type) {
    const v = [...value]
    v[type] = e
    if (v[1] < v[0]) {
      if (Object.is(type, 0)) {
        v[1] = undefined
      } else {
        v[0] = undefined
      }
    }
    onChange(v)
  }
  return (
    <>
      <DatePicker
        format="YYYY-MM-DD HH:mm"
        showTime={{
          defaultValue: dayjs(dayjs().format('YYYY/MM/DD 00:00:00')),
          format: 'HH:mm'
        }}
        placeholder={placeholder[0]}
        value={value && value[0]}
        onChange={(e) => onChangeDate(e, 0)}
      />
      <SwapRightOutlined style={{ marginLeft: 5, marginRight: 5 }} />
      <DatePicker
        format="YYYY-MM-DD HH:mm"
        showTime={{
          defaultValue: dayjs(dayjs().format('YYYY/MM/DD 23:59:59')),
          format: 'HH:mm'
        }}
        placeholder={placeholder[1]}
        value={value && value[1]}
        onChange={(e) => onChangeDate(e, 1)}
      />
    </>
  )
}

export default function DateRangePicker({ bothRequired = true, ...restProps }) {
  function range(start, end) {
    const result = []
    for (let i = start; i < end; i++) {
      result.push(i)
    }
    return result
  }

  function disabledRangeTime(date) {
    const hour = dayjs().hour()
    const minute = dayjs().minute()
    const second = dayjs().second()
    if (date && dayjs(date).date() === dayjs().date()) {
      return {
        disabledHours: () => range(hour + 1, 24),
        disabledMinutes: (selectedHour) =>
          selectedHour >= hour ? range(minute + 1, 60) : [],
        disabledSeconds: (selectedHour, selectedMinute) =>
          selectedHour >= hour && selectedMinute >= minute
            ? range(second + 1, 60)
            : []
      }
    }
    return {
      disabledHours: () => [],
      disabledMinutes: () => [],
      disabledSeconds: () => []
    }
  }
  if (bothRequired) {
    return (
      <DatePicker.RangePicker
        disabledDate={
          restProps?.forbiddenDisabled
            ? null
            : (current) => current && current > dayjs()
        }
        disabledTime={restProps?.forbiddenDisabled ? null : disabledRangeTime}
        style={{ width: '100%' }}
        separator="~"
        {...restProps}
      />
    )
  } else {
    return <Range {...restProps} />
  }
}
