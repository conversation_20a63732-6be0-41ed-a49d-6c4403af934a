import type { ProjectStatus, ProjectType } from '@/services/brain'
import { getWord } from '@/utils'

interface WordParserProps {
  word: ProjectType | ProjectStatus
}
export default function WordParser(props: WordParserProps) {
  const valueEnum = {
    fte: getWord('pages.projectTable.typeValue.fte'),
    ffs: getWord('pages.projectTable.typeValue.ffs'),
    personal: getWord('pages.projectTable.typeValue.personal'),
    designing: getWord('molecules-status.designing'),
    synthesizing: getWord('molecules-status.synthesizing'),
    created: getWord('pages.projectTable.statusLabel.created'),
    started: getWord('pages.projectTable.statusLabel.started'),
    finished: getWord('component.notification.statusValue.success'),
    holding: getWord('pages.projectTable.statusLabel.holding'),
    cancelled: getWord('pages.projectTable.statusLabel.cancelled'),
    canceled: getWord('pages.projectTable.statusLabel.cancelled')
  }
  const { word } = props
  return <>{valueEnum[`${word}`] || ''}</>
}
