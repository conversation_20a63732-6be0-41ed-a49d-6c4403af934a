import { scrollToTop } from '@/utils/dom'
import { useEffect, useState } from 'react'
import styles from './index.less'
/* TODO 美化ScrollTop */
export default function ScrollTop() {
  const [show, setShow] = useState<boolean>(false)
  useEffect(() => {
    document.addEventListener('scroll', scrollEvent)
    // Specify how to clean up after this effect:
    return document.removeEventListener('scroll', scrollEvent)
  }, [])

  /* scroll */
  const scrollEvent = () => {
    const scrollTop =
      document.body.scrollTop || document.documentElement.scrollTop
    setShow(scrollTop > 0)
  }
  return (
    <div className={styles.container}>
      {show ? (
        <div className={styles.scrollToTop} onClick={() => scrollToTop()}>
          scorllToTop icon
        </div>
      ) : null}
    </div>
  )
}
