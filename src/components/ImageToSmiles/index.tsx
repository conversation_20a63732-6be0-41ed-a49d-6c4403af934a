import { GetSmilesConfig } from '@/hooks/useKetcher'
import { query } from '@/services/brain'
import { getWord } from '@/utils'
import { App, Button, Input, Space, Typography, Upload, UploadFile } from 'antd'
import { RcFile } from 'antd/es/upload'
import React, { useState } from 'react'
import ButtonWithLoading from '../ButtonWithLoading'

const parseImgToSmiles = async (file: RcFile, fileName: string) => {
  const data = new FormData()
  data.append('image', file, fileName)
  const res = (await query('smiles/from-img', {
    method: 'post',
    data,
    normalizeData: false
  }).get()) as unknown as { reason: string; smiles: string }

  if (res.reason) {
    throw res.reason
  }
  return res.smiles
}

export interface ImageToSmilesProps {
  value?: UploadFile
  onChange?: (value?: UploadFile) => void
  onParsed?: (smiles: string) => void
  getSmiles?: (config?: GetSmilesConfig) => Promise<string | false>
}

const ImageToSmiles: React.FC<ImageToSmilesProps> = ({
  value: propValue,
  onChange,
  onParsed,
  getSmiles
}) => {
  const [file, originSetFile] = useState<UploadFile | undefined>(propValue)
  const { message, modal } = App.useApp()
  const [parsing, setParsing] = useState<boolean>(false)
  const setFile = (file?: UploadFile) => {
    originSetFile(file)
    onChange?.(file)
  }

  const parseImage = async (file: UploadFile) => {
    setParsing(true)
    try {
      const smiles = await parseImgToSmiles(file as RcFile, file.name)
      const cur = await getSmiles?.({ validate: false })
      if (!cur) {
        setParsing(false)
        onParsed?.(smiles)
        return
      }
      return new Promise((res, rej) => {
        modal.confirm({
          title: getWord('confirm-to-replace'),
          content: getWord('new-molecule-input'),
          onOk: () => {
            onParsed?.(smiles)
            setParsing(false)
            res('')
          },
          onCancel: () => {
            setParsing(false)
            rej()
          }
        })
      })
    } catch (e) {
      setParsing(false)
      modal.error({
        title: getWord('fail-convert'),
        content: (
          <>
            {getWord('try-replace-img')}
            <Typography.Paragraph code>{e}</Typography.Paragraph>
          </>
        )
      })
    }
    setParsing(false)
  }

  const onParseImg = async () => {
    if (!file) {
      message.error(getWord('select-an-img'))
      return
    }
    return await parseImage(file)
  }

  const onPaste = ({
    clipboardData
  }: React.ClipboardEvent<HTMLInputElement>) => {
    for (const item of Array.from(clipboardData?.items || [])) {
      if (item.type.indexOf('image') === 0) {
        const blob = item.getAsFile()
        setFile(blob)
      }
    }
  }

  return (
    <Space>
      <Upload
        onRemove={() => setFile(undefined)}
        beforeUpload={(file: UploadFile): false => {
          if (
            file.type === 'image/png' ||
            file.type === 'image/jpg' ||
            file.type === 'image/jpeg'
          ) {
            setFile(file)
          } else {
            message.error(getWord('upload-img'))
          }
          return false
        }}
        showUploadList={false}
        fileList={file ? [file] : []}
        disabled={parsing}
      >
        <Button disabled={parsing}>{getWord('select-a-document')}</Button>
      </Upload>
      <Space.Compact>
        <Input
          onChange={(e) => {
            if (!e.target.value?.length) {
              setFile(undefined)
            }
          }}
          allowClear
          value={file?.name}
          disabled={parsing}
          onPaste={onPaste}
          style={{ width: '130px' }}
        />
        <ButtonWithLoading
          disabled={!file}
          type="primary"
          onClick={() => onParseImg()}
        >
          {getWord('transfer-to-structure')}
        </ButtonWithLoading>
      </Space.Compact>
    </Space>
  )
}

export default ImageToSmiles
