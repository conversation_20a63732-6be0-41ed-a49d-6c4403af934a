import { useState } from 'react'
import type { ModalBaseProps } from './index.d'

export const useModalBase = (
  baseProps?: ModalBaseProps
): {
  dialogProps: ModalBaseProps
  confirm: () => Promise<string>
} => {
  const [dialogProps, setDialogProps] = useState<ModalBaseProps>({})
  const confirm = async () => {
    const promise = new Promise<string>((resolve, reject) => {
      setDialogProps({
        openEvent: { open: true },
        onConfirm: async () => resolve(''),
        onCancel: async () => reject()
      })
    })
    return promise
  }
  return { dialogProps: { ...baseProps, ...dialogProps }, confirm }
}
