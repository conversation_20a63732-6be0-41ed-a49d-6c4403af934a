import { Modal } from 'antd'
import React, { useCallback, useEffect, useState } from 'react'
import type { ModalBaseProps } from './index.d'

const ModalBase: React.FC<ModalBaseProps> = ({
  disabled,
  onConfirm,
  onCancel,
  afterClose,
  children,
  title,
  openEvent,
  ...props
}) => {
  const [open, setOpen] = useState<boolean>(false)
  const [confirming, setConfirming] = useState<boolean>(false)

  useEffect(() => setOpen(openEvent?.open || false), [openEvent])

  const close = useCallback(
    async (confirmed: boolean) => {
      const request = confirmed ? onConfirm : onCancel
      setConfirming(true)
      try {
        await request?.()
        setConfirming(false)
        setOpen(false)
        afterClose?.()
      } catch (error) {
        setConfirming(false)
        throw error
      }
    },
    [afterClose, onCancel, onConfirm]
  )

  return (
    <Modal
      {...props}
      title={title}
      open={open}
      confirmLoading={confirming}
      onCancel={disabled ? undefined : () => close(false)}
      onOk={disabled ? undefined : () => close(true)}
    >
      {children}
    </Modal>
  )
}

export default ModalBase
