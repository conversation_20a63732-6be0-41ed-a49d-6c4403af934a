@import '@/style/variables.less';
.userGuide {
  width: 100%;
  padding-right: 10px;
  cursor: pointer;
  svg {
    width: 24px;
    height: 24px;
    fill: @color-text-a;
  }
  .operateContent {
    display: flex;
    align-items: center;
    .langSwitch {
      position: relative;
      right: 4px;
    }
    .help {
      span {
        margin-left: 10px;
        font-family: 'NotoSans-Medium' !important;
      }
    }
    .langIcon {
      svg {
        position: relative;
        left: 11px;
        width: 18px;
      }
    }
    .langIcon:hover {
      svg {
        fill: @brand-primary;
      }
    }
  }
}

.help:hover {
  color: @brand-primary;
  cursor: pointer;
  svg {
    width: 24px;
    fill: @brand-primary;
  }
}
.commonIconStyle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.foldType {
  position: relative;
  bottom: 38px;
  flex-direction: column-reverse;
  font-size: 16px;
  transition: font-size 0.3s ease-in-out;
  padding-block: 0;
  padding-inline: 0px;
  svg {
    position: relative;
    left: 3px;
  }
  .langIcon {
    .commonIconStyle;
    margin-left: 0px;
    border-radius: 8px;
    svg {
      position: relative;
      left: 0px;
      width: 22px;
    }
  }
  .langIcon:hover,
  .help:hover {
    background-color: rgba(0, 0, 0, 0.06);
    svg {
      fill: @brand-primary;
    }
  }
  .help {
    margin-top: 5px;
    margin-bottom: 15px;
    margin-left: 0px;
    border-radius: 8px;
    .commonIconStyle;
    svg {
      position: relative;
      left: 0px;
      width: 22px;
    }
  }
}

:global {
  .ant-pro-sider-extra {
    position: absolute;
    bottom: 45px;
    z-index: @sider-extra-ZIndex;
    width: calc(100% - 16px) !important;
    font-family: unset !important;
    margin-inline: 8px !important;
  }
}
