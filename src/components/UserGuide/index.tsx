import { ReactComponent as HelpIcon } from '@/assets/svgs/help.svg'
import cs from 'classnames'
import { useModel } from 'umi'

import { isEN } from '@/utils'
import SwichLang from '../SwitchLang'
import styles from './index.less'
export default function UserGuide() {
  const { initialState } = useModel('@@initialState')
  const jumpHelpPage = () =>
    window.open(
      isEN()
        ? `${document.location.origin}/help/#/en/`
        : `${document.location.origin}/help`,
      '_blank'
    )

  const HelpButton = () => <HelpIcon onClick={jumpHelpPage} />
  return (
    <div
      className={cs(styles.userGuide, {
        [styles['foldType']]: initialState?.isMenuCollapsed
      })}
    >
      {initialState?.isMenuCollapsed ? (
        <>
          <div className={styles.langIcon}>
            <SwichLang />
          </div>
          <div className={styles.help}>
            <HelpButton />
          </div>
        </>
      ) : (
        <div
          className={cs(
            'flex-align-items-center',
            'flex-justify-space-between',
            styles.operateContent
          )}
        >
          <div className={cs(styles.help, 'flex-align-items-center')}>
            <HelpButton />
            <span onClick={jumpHelpPage}>Help</span>
          </div>
          <div className={styles.langSwitch}>
            <SwichLang />
          </div>
        </div>
      )}
    </div>
  )
}
