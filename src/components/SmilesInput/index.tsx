import { useSmilesEditor } from '@/components/MoleculeEditor'
import EditorDialog from '@/components/MoleculeEditor/EditorDialog'
import MoleculeStructure from '@/components/MoleculeStructure'
import { getWord } from '@/utils'
import message from '@/utils/message'
import { PlusOutlined } from '@ant-design/icons'
import { Upload } from 'antd'
import React, { useEffect, useState } from 'react'
import type { SmilesInputProps } from './index.d'

const SmilesInput: React.FC<SmilesInputProps> = ({
  value,
  onChange,
  multiple = true,
  className,
  disabled,
  type = 'molecule'
}) => {
  const [smiles, setSmiles] = useState<string[]>(
    typeof value === 'string' ? [value] : value || []
  )
  const { dialogProps, inputSmiles } = useSmilesEditor()
  const updateSmiles = (newSmiles: string[]) => {
    setSmiles(newSmiles)
    onChange?.(multiple ? newSmiles : newSmiles[0])
  }

  useEffect(() => {
    if (typeof value === 'string') {
      setSmiles([value])
    } else {
      setSmiles(value || [])
    }
  }, [value])

  const uploadButton = (
    <div
      className="add-button"
      onClick={async () => {
        if (disabled) return
        const newSmiles = await inputSmiles('')
        if (!newSmiles) return
        if (smiles.includes(newSmiles)) {
          message.warn({
            message: getWord('molecule-exist'),
            description: ''
          })
          return
        }
        updateSmiles(multiple ? [...smiles, newSmiles] : [newSmiles])
      }}
    >
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Add</div>
    </div>
  )

  return (
    <>
      <Upload<string>
        listType="picture-card"
        disabled={disabled}
        className={`smiles-list ${type === 'reaction' ? 'reaction-list' : ''} ${
          className || ''
        } ${multiple ? '' : smiles?.length ? 'hide-upload-btn' : ''}`}
        multiple={multiple}
        maxCount={multiple ? undefined : 1}
        openFileDialogOnClick={false}
        iconRender={(file) => (
          <MoleculeStructure
            structure={file.name}
            copyBtn={false}
            expandBtn={false}
          />
        )}
        onChange={({ fileList }) => {
          updateSmiles(fileList.map(({ name }) => name))
        }}
        fileList={smiles.map((s) => ({ uid: s, name: s }))}
        showUploadList={{
          showDownloadIcon: false,
          showPreviewIcon: false,
          showRemoveIcon: true
        }}
      >
        {uploadButton}
      </Upload>
      <EditorDialog {...dialogProps} />
    </>
  )
}

export default SmilesInput
