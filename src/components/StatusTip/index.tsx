import { ReactComponent as EmptyIcon } from '@/assets/svgs/empty.svg'

import { Empty } from 'antd'
import cs from 'classnames'
import type { StatusTipProps } from './index.d'
import styles from './index.less'

export default function StatusTip(props: StatusTipProps) {
  return (
    <div
      className={cs(styles.statusTip, 'flex-center', props?.wrapperClassName)}
    >
      <Empty
        image={props?.image ? props?.image : <EmptyIcon />}
        imageStyle={{ height: 200 }}
        description={
          <span>
            {props?.clickEvent ? (
              <a onClick={props?.clickEvent}>{props?.des}</a>
            ) : (
              props?.des
            )}
          </span>
        }
      />
    </div>
  )
}
