import { ReactComponent as SettingsIcon } from '@/assets/svgs/navbar/settings.svg'
import { getWord } from '@/utils'
import cs from 'classnames'
import { history, useModel } from 'umi'
import styles from './index.less'

export default function UserSettings() {
  const { initialState } = useModel('@@initialState')
  const toSettingPage = () => history.push('/settings')
  return (
    <div
      className={cs(styles.userSettings, {
        [styles['foldType']]: initialState?.isMenuCollapsed
      })}
    >
      {initialState?.isMenuCollapsed ? (
        <div className={styles.settings} onClick={toSettingPage}>
          <SettingsIcon />
        </div>
      ) : (
        <div
          className={cs(
            'flex-align-items-center',
            'flex-justify-space-between',
            styles.operateContent
          )}
        >
          <div className={cs(styles.settings, 'flex-align-items-center')}>
            <SettingsIcon />
            <span onClick={() => history.push('/settings')}>
              {getWord('menu.account.settings')}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
