@import '@/style/variables.less';
.userSettings {
  width: 100%;
  height: 30px;
  padding-right: 10px;
  cursor: pointer;
  svg {
    width: 24px;
    height: 24px;
    fill: @color-text-a;
  }
  .operateContent {
    .settings {
      span {
        margin-left: 10px;
        font-family: 'NotoSans-Medium' !important;
      }
    }
    .settings:hover {
      color: @brand-primary;
      cursor: pointer;
      svg {
        width: 24px;
        fill: @brand-primary;
      }
    }
  }
}

.commonIconStyle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.foldType {
  position: relative;
  bottom: 38px;
  flex-direction: column-reverse;
  margin-bottom: 10px;
  font-size: 16px;
  transition: font-size 0.3s ease-in-out;
  padding-block: 0;
  padding-inline: 0px;
  svg {
    position: relative;
    left: 3px;
  }
  .settings {
    margin-top: 5px;
    margin-left: 0px;
    border-radius: 8px;
    .commonIconStyle;
    svg {
      position: relative;
      left: 0px;
      width: 24px;
    }
  }
  .settings:hover {
    background-color: rgba(0, 0, 0, 0.06);
    svg {
      fill: @brand-primary;
    }
  }
}
