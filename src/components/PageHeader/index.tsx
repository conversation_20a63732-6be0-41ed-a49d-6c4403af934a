import { Breadcrumb } from 'antd'
import { ItemType } from 'antd/lib/breadcrumb/Breadcrumb'
import React, { ReactNode } from 'react'
import styles from './index.less'

export interface PageHeaderProps {
  leftSlot?: ReactNode
  rightSlot?: ReactNode
  breadcrumbs?: ItemType[]
  minRightWidth?: number
}

const PageHeader: React.FC<PageHeaderProps> = ({
  leftSlot,
  rightSlot,
  breadcrumbs,
  minRightWidth
}) => {
  if (!leftSlot && breadcrumbs) {
    leftSlot = <Breadcrumb items={breadcrumbs} />
  }
  return (
    <div className={styles['page-header-root']}>
      <div className={styles.leftSide}>{leftSlot}</div>
      <div
        className={styles.rightSide}
        style={{
          minWidth: minRightWidth ? `${minRightWidth}px` : 'unset'
        }}
      >
        {rightSlot}
      </div>
    </div>
  )
}

export default PageHeader
