import cs from 'classnames'
import type { SectionTitleProps } from './index.d'
import styles from './index.less'
export default function SectionTitle(props: SectionTitleProps) {
  return (
    <div
      className={cs(styles.sectionTitle, props?.wrapClassName)}
      id={props?.anchorId}
    >
      <h2>{props?.word}</h2>
      {props?.extra ? (
        <div className={styles.extraCom}>{props?.extra}</div>
      ) : null}
    </div>
  )
}
