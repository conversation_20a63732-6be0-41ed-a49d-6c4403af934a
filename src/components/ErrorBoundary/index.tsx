import LoadingTip from '@/components/LoadingTip'
import { getWord } from '@/utils'
import { App, Result } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { ErrorBoundary as DefaultErrorBoundary } from 'react-error-boundary'

const lastReloadTimeForChunkLoadErrorKey = 'reloadedForChunkLoadError'

interface FallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

const Fallback: React.FC<FallbackProps> = ({ error }) => {
  const { message } = App.useApp()
  const lastReloadTime = dayjs(
    sessionStorage.getItem(lastReloadTimeForChunkLoadErrorKey)
  )
  if (
    error.name === 'ChunkLoadError' &&
    (!lastReloadTime.isValid() || dayjs().diff(lastReloadTime, 's') > 10)
  ) {
    sessionStorage.setItem(
      lastReloadTimeForChunkLoadErrorKey,
      dayjs().toISOString()
    )
    message.loading(getWord('system-update'))
    window.location.reload()
    return (
      <div className="loadingPage">
        <LoadingTip />
      </div>
    )
  }
  return (
    <Result
      status="error"
      title="Something went wrong."
      extra={error.message}
    />
  )
}

const ErrorBoundary: React.FC = ({ children }) => {
  return (
    <DefaultErrorBoundary FallbackComponent={Fallback}>
      {children}
    </DefaultErrorBoundary>
  )
}

export default ErrorBoundary
