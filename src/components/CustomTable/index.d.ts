// TODO complete ts interface
export interface CustomTableProps {
  columns: any
  rowKey: any
  dataSource: any
  rowSelection?: any
  enExpandable?: any
  onRow?: (record: any, index: number) => void
  pagination?: any
  total?: number
  scrollY?: number
  loading?: boolean
}

export interface IScroll {
  /**
   *
   * 指定Table滚动区域的宽
   */
  x: number
  /**
   *
   * 指定Table滚动区域的高
   */
  y: number
}
