import { Table } from 'antd'
import cs from 'classnames'
import ResizeObserver from 'rc-resize-observer'
import { useEffect, useRef, useState } from 'react'

import { getTableScroll } from '@/utils/dom'
import { DownOutlined } from '@ant-design/icons'

import type { CustomTableProps, IScroll } from './index.d'
import styles from './index.less'

export default function CustomTable(props: CustomTableProps) {
  const {
    dataSource = [],
    rowSelection,
    rowKey,
    onRow,
    pagination,
    total,
    scrollY,
    loading
  } = props
  const { current, pageSize } = pagination || {}
  const countRef = useRef(null)
  const [columns, setColumns] = useState(props.columns || [])
  const [wrapperHeight, setWrapperHeight] = useState<number>(0)
  const [bodyHeight, setBodyHeight] = useState<number>(0)
  const [width, setWidth] = useState<number>(0)
  const [currentExpandeRowKeys, setCurrentExpandeRowKeys] = useState<string[]>(
    []
  )
  useEffect(() => {
    if (!props?.enExpandable) setCurrentExpandeRowKeys([])
  }, [props?.enExpandable])

  useEffect(() => {
    if (loading) setCurrentExpandeRowKeys([]) // 查询时折叠列表内容
  }, [loading])

  const tableHeaderHeight: number = 40
  useEffect(() => {
    if (countRef.current) {
      const offsetHeight = countRef.current?.offsetHeight - tableHeaderHeight
      setWrapperHeight(offsetHeight)
      const newBodyHeight =
        countRef.current?.querySelector(`.ant-table-tbody`)?.offsetHeight
      setBodyHeight(newBodyHeight)
    }
    let width = 0
    columns.forEach((item) => {
      if (item.width) width += item.width
    })
    if (width > countRef.current.offsetWidth) setWidth(width)

    window.onresize = () => {
      if (countRef.current) {
        const offsetHeight = countRef.current?.offsetHeight - tableHeaderHeight
        setWrapperHeight(offsetHeight)
        const newBodyHeight =
          countRef.current?.querySelector(`.ant-table-tbody`)?.offsetHeight
        if (newBodyHeight !== bodyHeight) setBodyHeight(newBodyHeight)
      }
    }
    return () => {
      window.onresize = () => {}
    }
  }, [])

  useEffect(() => {
    if (props.columns instanceof Array) {
      const flag = true
      if (flag) {
        if (countRef.current) {
          const offsetHeight =
            countRef.current?.offsetHeight - tableHeaderHeight
          setWrapperHeight(offsetHeight)
          const newBodayHeight =
            countRef.current.querySelector(`.ant-able-tbody`)?.offsetHeight
          setBodyHeight(newBodayHeight)
        }
        let width = 0
        props.columns.forEach((item) => {
          if (item.width) width += item.width
        })
        if (width > countRef.current.offsetWidth) {
          setWidth(width)
        } else if (width < countRef.current.offsetWidth) {
          setWidth(null)
        }
        setColumns(props.columns)
      }
    }
  }, [props.columns])

  useEffect(() => {
    if (countRef.current) {
      const offsetHeight = countRef.current?.offsetHeight - tableHeaderHeight
      setWrapperHeight(offsetHeight)
      const newBodyHeight =
        countRef.current.querySelector(`.ant-table-tbody`)?.offsetHeight
      if (newBodyHeight !== bodyHeight) setBodyHeight(newBodyHeight)
    }
  }, [pageSize, dataSource, current])

  const scroll: IScroll = { x: 0, y: 0 }
  if (bodyHeight > wrapperHeight) scroll.y = wrapperHeight
  if (width) scroll.x = width

  function expandedRowFunction(record) {
    if (currentExpandeRowKeys.includes(record?.id)) {
      setCurrentExpandeRowKeys(
        currentExpandeRowKeys.filter((e) => e !== record?.id)
      )
    } else {
      props?.expandEvent(record)
      currentExpandeRowKeys.push(record?.id)
      setCurrentExpandeRowKeys(currentExpandeRowKeys)
    }
  }
  return (
    <ResizeObserver
      onResize={() => {
        if (countRef.current) {
          const offsetHeight =
            countRef.current?.offsetHeight - tableHeaderHeight
          setWrapperHeight(offsetHeight)
          const newBodyHeight =
            countRef.current.querySelector(`.ant-table-tbody`)?.offsetHeight
          setBodyHeight(newBodyHeight)
        }
      }}
    >
      <div className={cs(styles.adminTable, 'enableUserSelect')}>
        <div
          className={cs(styles.tableWrapper, props?.wrapperClassName)}
          ref={countRef}
          style={{ height: pagination ? '' : '100%' }}
        >
          <Table
            rowKey={rowKey || 'id'}
            columns={columns}
            pagination={
              props?.onChange && pagination
                ? {
                    size: props?.paginationSize || 'small',
                    total: total || pagination?.total,
                    showQuickJumper: true,
                    showSizeChanger:
                      dataSource?.length > 10 || pagination?.total > 10,
                    showTotal: (total: number) => `共${total}条记录`,
                    onChange: (pageNo: number, pageSize: number) => {
                      props?.onChange(pageNo, pageSize)
                    },
                    current: current || 1,
                    pageSize: pageSize || 20,
                    pageSizeOptions: ['10', '20', '30', '50', '100']
                  }
                : false
            }
            dataSource={dataSource}
            rowSelection={rowSelection || null}
            loading={loading && { size: 'large', tip: '加载中，请耐心等待～' }}
            expandable={
              props?.enExpandable
                ? {
                    expandedRowRender: (record) => (
                      <RecommendedProgress record={record} />
                    ),
                    rowExpandable: () => true,
                    expandRowByClick: true,
                    expandedRowKeys: currentExpandedRowKeys,
                    expandIcon: ({ expanded, onExpand, record }) => {
                      return expanded ? (
                        <DownOutlined
                          style={{
                            cursor: 'pointer',
                            fill: 'hsla 204, 1000, 63%, 1',
                            transform: 'rotatex (180deg)'
                          }}
                          width={16}
                          height={9}
                          onClick={(e) => {
                            expandedRowFunction(record)
                            onExpand(record, e)
                          }}
                        />
                      ) : (
                        <DownOutlined
                          style={{
                            cursor: 'pointer',
                            fill: 'hsla(0, 0t, tableHeaderHeightt, 1)',
                            transform: 'rotatex(180deg)'
                          }}
                          width={16}
                          height={9}
                          onClick={(e) => {
                            expandedRowFunction(record)
                            onExpand(record, e)
                          }}
                        />
                      )
                    }
                  }
                : null
            }
            scroll={{
              x: scroll.x,
              y: scrollY || getTableScroll({ ref: countRef })
            }}
            onRow={
              props?.enExpandable
                ? (record) => {
                    return {
                      onClick: () => expandedRowFunction(record)
                    }
                  }
                : onRow
            }
          />
        </div>
      </div>
    </ResizeObserver>
  )
}
