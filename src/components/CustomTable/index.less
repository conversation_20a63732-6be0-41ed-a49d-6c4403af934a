.adminTable {
  flex: 1;
  width: 100%;
  overflow: hidden;
  .tableWrapper {
    width: 100%;
    height: auto;
    min-height: calc(100% - 56px);
    // padding: 0 18px 0 24px;
    overflow: hidden;
    background: #ffff;
  }

  :global {
    .ant-table-pagination.ant-pagination {
      margin: 16px 10px;
    }
    /* TODO custom table */
    .ant-table {
      /* NOTE https://github.com/ant-design/ant-design/issue/13825 */
      .ant-table-fixed {
        table-layout: fixed;
      }

      /* NOTE fix scroll-x/y共存时，表头最后一列和表格体最后一列错位的问题 */
      .ant-table-header {
        .ant-table-thead {
          // .ant-table-cell-fix-right-first {
          //   right: 10px !important; // 和滚动条 -webkit-scrollbar 宽度一致
          // }
          // .ant-table-row-expand-icon-cell {
          //   padding-right: 30px;
          // }
          /* custom:header圆角配置 */
          /*   th:first-child {
            padding-left: 30px;
            border-radius: 15px 0 0 15px !important;
          }
          th:last-child {
            border-radius: 0 15px 15px 0 !important;
          } */
          tr {
            th {
              height: 48px !important;
              min-height: 40px;
              // padding: 0px 0 15px 16px !important;
              padding-left: 16px !important;
              color: #1d2129;
              font-weight: bold;
              font-size: 14px;
              line-height: 14px;
              // background: #fff !important; //  #fafafa #fff #f2f3f5
              background: #fafafa !important; //   #fff #f2f3f5
              .ant-table-column-sorters {
                height: 48px !important;
              }
            }
            th::before {
              width: 0px !important; // 去掉标题之间的竖线
            }
            .ant-table-cell-scrollbar {
              // 最右侧的竖线处理
              background: #fff;
              box-shadow: 0 1px 0 1px #fff;
            }
          }
        }

        .ant-table-thead > tr > th {
          padding: 0px; //表格头部
          // padding: 7px; //表格头部
          .ant-table-column-title {
            // 设置头部标题不换行
            white-space: nowrap; // 文本不会换行，文本会在在同一行上继续，直到遇到 <br> 标签为止
          }
        }
      }

      .ant-table-body {
        .ant-table-tbody > tr > td {
          padding: 5px 0px 5px 16px;
          vertical-align: middle;
        }
        width: 100%;
        height: auto;
        font-size: 14px; //表格字体
        background: #ffff;
        border-radius: 0 0 6px 6px;
        colgroup {
          min-width: auto !important;
        }
        col {
          min-width: auto !important;
        }
        .ant-pagination-options-size-changer {
          min-width: 112px;
        }
        tr {
          td {
            height: auto;
            min-height: 36px;
            color: #666666;
            font-size: 12px;
          }
        }
        // /* NOTE:Fix ant table bug https://github.com/ant-design/ant-design/issue/22333 */
        tr.ant-table-measure-row {
          visibility: collapse;
        }
      }

      .ant-table-body > tr {
        word-wrap: break-word;
        word-break: break-all;
      }

      /* Custom table scrollbar style start */
      .ant-table-body::-webkit-scrollbar {
        /* width of the entire scrollbar */
        width: 6px;
        height: 8px;
        border-radius: 6px;
      }

      .ant-table-body::-webkit-scrollbar-track {
        background: transparent;
      }

      .ant-table-body:hover {
        color: #fff;
        opacity: 0.95;
        transition: 0.3s ease-in-out;
        &::-webkit-scrollbar-track {
          /* color of the tracking area */
          // background: #f6f9fc;
          border-radius: 6px;
        }

        &::-webkit-scrollbar-thumb {
          /* color of the scroll thumb */
          background: #66ccff;
          border: 3px solid transparent;
          border-radius: 6px;
        }
      }
    }

    .ant-table:not(.ant-table-bordered) {
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
  }

  // /* Custom table scrollbar style end */

  // /* 展开内容设置padding为0 */
  // .ant-table-expanded-row-level-1 {
  //   td {
  //     padding: 0 O !important;
  //   }
  // }

  .ant-spin-container {
    transition: opacity 0.3s cubic-bezier(0.645, 0.045.355, 1);
  }
}
