import Plyr from 'plyr'
import 'plyr/dist/plyr.css'
import { useEffect, useRef } from 'react'
import styles from './index.less'
interface PlyrVideoProps {
  link: string | null
  currentTime?: number
}
export default function PlyrVideo(props: PlyrVideoProps) {
  // const [curPlayer, setCurPlayer] = useState()
  // const { updateTimeValue, timeValue, updatePlayer } = useModel('video')
  const { link, currentTime } = props
  const videoRef = useRef<HTMLVideoElement>(null)

  // useEffect(() => {
  //   // console.log('---liveLink?---', liveLink)
  //   // http://43.192.94.14:2022/rtc/v1/whep/?app=live&stream=livestream
  //   // let hlsLink: string = 'http://ec2-43-192-94-14.cn-northwest-1.compute.amazonaws.com.cn:2022/live/r1.m3u8'
  //   if (!liveLink) return
  //   if (Hls.isSupported()) {
  //     const hls = new Hls()
  //     hls.loadSource(liveLink) //hlsLink 替换为实际的 HLS 流地址
  //     hls.attachMedia(videoRef.current)
  //     hls.on(Hls.Events.MANIFEST_PARSED, function () {
  //       videoRef.current.play()
  //     })
  //     // if (!videoRef?.current) return
  //     // videoRef.current.src = link
  //     const _player = new Plyr(videoRef.current)
  //     updatePlayer(_player)
  //     setCurPlayer(_player)
  //     return () => {
  //       _player.destroy()
  //       hls.destroy()
  //     }
  //   } else if (videoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
  //     videoRef.current.src = liveLink // hlsLink // 替换为实际的 HLS 流地址
  //     videoRef.current.addEventListener('canplay', function () {
  //       videoRef.current.play()
  //     })
  //     const _player = new Plyr(videoRef.current)
  //     updatePlayer(_player)
  //     setCurPlayer(_player)
  //     return () => {
  //       _player.destroy()
  //     }
  //   }
  // }, [liveLink])
  /* 通过后缀格式判断是直播流 还是mp4回放 */

  const initVideo = async () => {
    let player,
      playbackLink: string = link
    if (videoRef.current) {
      console.log('---已录制的视频---', link)
      if (playbackLink) videoRef.current.src = playbackLink
      player = new Plyr(videoRef.current)
      player.play()
    }
  }

  useEffect(() => {
    initVideo()
  }, [])
  // useEffect(() => {
  //   if (videoRef.current) {
  //     console.log('---已录制的视频---', link)
  //     if (link) videoRef.current.src = link
  //     const _player = new Plyr(videoRef.current)
  //     // 设置视频流的地址
  //     // _player.source = {
  //     //   type: 'video',
  //     //   sources: [
  //     //     {
  //     //       src: 'http://43.192.94.14:2022/live/robot-1-left.m3u8', // 直播流地址 webrtc://195974.push.tlivecloud.com/live/labwise?txSecret=fba75764c59350b94c1612543b845a6e&txTime=65D5C39D
  //     //       type: 'application/x-mpegURL' // 根据直播流的格式设置类型 HTTP-FLV流
  //     //     }
  //     //   ]
  //     // }
  //     // updatePlayer(_player)
  //     // setCurPlayer(_player)
  //   }
  // }, [videoRef])

  /*   useEffect(() => {
    if (curPlayer && currentTime) {
      let previousSecond = timeValue
      curPlayer.currentTime = currentTime
      curPlayer.on('timeupdate', (event) => {
        const currentTime = Math.floor(event.detail.plyr.currentTime)
        // 检查秒数是否发生变化
        if (currentTime !== previousSecond) {
          //   console.log('当前播放时间:', currentTime)
          previousSecond = currentTime
          updateTimeValue(currentTime)
        }
      })
      curPlayer.play()
    }
  }, [currentTime]) */

  return (
    <div className={styles.plyrVideo}>
      <video ref={videoRef} controls={false} playsInline />
    </div>
  )
}
