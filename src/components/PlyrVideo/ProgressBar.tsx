import { useEffect, useRef, useState } from 'react'

function ProgressBar({ player }) {
  const progressBarRef = useRef(null)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    if (!player) return
    const progressBar = progressBarRef.current

    const updateProgress = () => {
      const currentTime = player.currentTime
      const duration = player.duration
      const progressValue = (currentTime / duration) * 100
      setProgress(progressValue)
    }
    player.on('timeupdate', updateProgress)

    return () => {
      player.off('timeupdate', updateProgress)
    }
  }, [player])

  const handleProgressBarClick = (e) => {
    const progressBar = progressBarRef.current
    const rect = progressBar.getBoundingClientRect()
    const offsetX = e.clientX - rect.left
    const progressPercentage = (offsetX / rect.width) * 100
    const newTime = (progressPercentage / 100) * player.duration
    player.currentTime = newTime
  }

  return (
    <div ref={progressBarRef} onClick={handleProgressBarClick}>
      <div
        style={{
          width: `${progress}%`,
          backgroundColor: 'blue',
          height: '10px'
        }}
      ></div>
    </div>
  )
}

export default ProgressBar
