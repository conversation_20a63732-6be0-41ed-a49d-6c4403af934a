import 'plyr/dist/plyr.css'
import { useEffect, useState } from 'react'

const CustomControls = ({ player }) => {
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  useEffect(() => {
    const handleTimeUpdate = () => {
      setCurrentTime(player.currentTime)
      setDuration(player.duration)
    }
    player.on('timeupdate', handleTimeUpdate)
    return () => {
      player.off('timeupdate', handleTimeUpdate)
    }
  }, [player])

  const [isDragging, setIsDragging] = useState(false)

  const handleProgressBarChange = (e) => {
    const seekTime = (e.target.value / 100) * player.duration
    player.currentTime = seekTime
  }

  const handleProgressBarMouseDown = () => {
    setIsDragging(true)
    player.pause()
  }

  const handleProgressBarMouseUp = () => {
    if (isDragging) {
      player.play()
      setIsDragging(false)
    }
  }

  const [isPlaying, setIsPlaying] = useState(false)

  const togglePlay = () => {
    if (isPlaying) {
      player.pause()
    } else {
      player.play()
    }
    setIsPlaying(!isPlaying)
  }

  return (
    <div className="plyr__controls">
      <button
        onClick={togglePlay}
        type="button"
        className="plyr__control"
        data-plyr="play"
      >
        {isPlaying ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect x="5" y="3" width="14" height="18" rx="2" ry="2"></rect>
          </svg>
        ) : (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
        )}
      </button>
      <input
        type="range"
        className="plyr__control"
        data-plyr="seek"
        value={(currentTime / duration) * 100 || 0}
        onChange={handleProgressBarChange}
        onMouseDown={handleProgressBarMouseDown}
        onMouseUp={handleProgressBarMouseUp}
      />
    </div>
  )
}

export default CustomControls
