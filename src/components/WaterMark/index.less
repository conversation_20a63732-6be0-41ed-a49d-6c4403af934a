.commonWatermark {
  position: fixed;
  top: 0; // 60px if has nav header
  left: 0;
  width: 100%;
  height: inherit;
  overflow: hidden;
  pointer-events: none;
}

.commonWatermarkBox {
  margin-top: -25%;
  transform: skew(-4deg);
}

.commonStylesDiv {
  display: flex;
  justify-content: space-between;
  padding: 40px 20px;
  transform: rotate(-30deg);
}

.commonFont {
  font-size: 16px;
  user-select: none;
}

.two {
  width: 100%;
  margin-left: 11%;
}

/* 盲水印比明水印z-index大1 */
.blindWatermark {
  &:extend(.commonWatermark);
  .blindWatermarkBox:extend(.commonWatermarkBox) {
    margin-left: -800px;
  }

  .stylesDiv:extend(.commonStylesDiv) {
    span:extend(.commonFont) {
      color: #000;
      opacity: 0.01;
    }
  }
}

/* 明水印(比Antd弹窗大2，与顶部Header保持一致) */
.watermark {
  &:extend(.commonWatermark);
  .watermarkBox:extend(.commonWatermarkBox) {
    margin-left: -400px;
  }
  .stylesDiv:extend(.commonStylesDiv) {
    span:extend(.commonFont) {
      color: #999999;
      opacity: 0.2;
    }
  }
}

.mbileWatermark {
  top: 0px;
  left: -25%;
  width: 150%;
}

.mobileWatermarkBox {
  margin-left: -5%;
}

.mobileBlindWatermarkBox {
  margin-left: -8%;
}

.mobileDiv {
  padding: 20px 0px !important;
  span:extend(.commonFont) {
    margin: 20px 20px !important;
    color: #aaaaaa !important;
    opacity: 0.1 !important;
  }
}
