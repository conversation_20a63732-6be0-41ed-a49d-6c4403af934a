import cs from 'classnames'
import dayjs, { Dayjs } from 'dayjs'
import { isEmpty } from 'lodash'
import type { WatermarkProps } from './index.d'
import styles from './index.less'

export default function Watermark({
  isBlind,
  domain,
  blindzIndex,
  zIndex,
  isMobile,
  wrapclassName
}: WatermarkProps) {
  const YMD = dayjs(dayjs() as Dayjs).format('YY-MM-DD HH:mm:ss')
  const domains: string[][] = []
  for (let i = 0; i < 100; i++) {
    const obj: string[] = [
      `${domain}_${YMD}`,
      `${domain}_${YMD}`,
      `${domain}_${YMD}`,
      `${domain}_${YMD}`,
      `${domain}_${YMD}`
    ]
    domains.push(obj)
  }

  function getZIndex(): number {
    if (isBlind) return blindzIndex || 1033
    return zIndex || 1032
  }

  return (
    <div
      className={cs(
        {
          [styles['watermark']]: !isBlind,
          [styles['blindWatermark']]: isBlind,
          [styles['mobileWatermark']]: isMobile
        },
        wrapclassName
      )}
      style={{ zIndex: getZIndex() }}
    >
      <div
        className={cs({
          [styles['watermarkBox']]: !isBlind && !isMobile,
          [styles['blindWatermarkBox']]: isBlind && !isMobile,
          [styles['mobileWatermarkBox']]: isMobile && !isBlind,
          [styles['mobileBlindWatermarkBox']]: isMobile && isBlind
        })}
      >
        {!isEmpty(domains) &&
          domains.map((item, index: number) => (
            <div
              key={`${isBlind ? 'blind-watermark' : 'watermark'}-${index}`}
              className={cs(styles.stylesDiv, {
                [styles['two']]: index % 2 === 1,
                [styles['mobileDiv']]: isMobile && !isBlind
              })}
            >
              {!isEmpty(item) &&
                item.map((child: string, i: number) => (
                  <span
                    key={`${i}-${isBlind ? 'blind-watermark' : 'watermark'}`}
                  >
                    {child}
                  </span>
                ))}
            </div>
          ))}
      </div>
    </div>
  )
}
