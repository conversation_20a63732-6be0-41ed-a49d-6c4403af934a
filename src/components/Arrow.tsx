import React from 'react'

export interface ArrowProps {
  color?: string
}

const Arrow: React.FC<ArrowProps> = ({ color = '#222' }) => {
  return (
    <svg viewBox="0 -3 128 6" width="60" height="12" transform="rotate(180)">
      <defs>
        <marker
          id="reaction-arrowhead"
          viewBox="0 0 8 6"
          markerUnits="userSpaceOnUse"
          markerWidth="18"
          markerHeight="12"
          refX="2"
          refY="2"
          orient="auto"
          fill={color}
        >
          <path d="m 0 0 l 7 2.25 l -7 2.25 c 0 0 0.735 -1.084 0.735 -2.28 c 0 -1.196 -0.735 -2.22 -0.735 -2.22 z"></path>
        </marker>
      </defs>
      <line
        x1="0"
        y1="0"
        x2="120"
        y2="0"
        strokeWidth="1"
        stroke={color}
        markerEnd="url(#reaction-arrowhead)"
      ></line>
    </svg>
  )
}

export default Arrow
