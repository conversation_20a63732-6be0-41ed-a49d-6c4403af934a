import { getEnvConfig, getWord, toLoginPage } from '@/utils'
import {
  LogoutOutlined,
  SettingOutlined,
  UserOutlined
} from '@ant-design/icons'
import { useEmotionCss } from '@ant-design/use-emotion-css'
import { Spin, Typography } from 'antd'
import type { MenuInfo } from 'rc-menu/lib/interface'
import React, { useCallback } from 'react'
import { flushSync } from 'react-dom'
import { history, useModel } from 'umi'
import HeaderDropdown from '../HeaderDropdown'

export type GlobalHeaderRightProps = {
  menu?: boolean
  children?: React.ReactNode
}

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState')
  return (
    <span className="anticon">
      <Typography.Text style={{ maxWidth: 160 }} ellipsis={{ tooltip: true }}>
        {initialState?.userInfo?.username}
      </Typography.Text>
    </span>
  )
}

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({
  menu,
  children
}) => {
  const loginOut = () => {
    const { logoutUrl } = getEnvConfig()
    return logoutUrl ? window.location.replace(logoutUrl) : toLoginPage()
  }

  const actionClassName = useEmotionCss(({ token }) => {
    return {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover
      }
    }
  })
  const { initialState, setInitialState } = useModel('@@initialState')

  const onMenuClick = useCallback(
    (event: MenuInfo) => {
      const { key } = event
      if (key === 'logout') {
        flushSync(() => {
          setInitialState((s) => ({ ...s, userInfo: undefined }))
        })
        loginOut()
        return
      }
      history.push(`/account/${key}`)
    },
    [setInitialState]
  )

  const loading = (
    <span className={actionClassName}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8
        }}
      />
    </span>
  )

  if (!initialState) {
    return loading
  }

  const { userInfo } = initialState
  if (!userInfo || !userInfo?.username) {
    return loading
  }

  const menuItems = [
    ...(menu
      ? [
          {
            key: 'center',
            icon: <UserOutlined />,
            label: `${getWord('menu.account.center')}`
          },
          {
            key: 'settings',
            icon: <SettingOutlined />,
            label: `${getWord('menu.account.settings')}`
          },
          {
            type: 'divider' as const
          }
        ]
      : []),
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: `${getWord('logout')}`
    }
  ]

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems
      }}
    >
      {children}
    </HeaderDropdown>
  )
}
