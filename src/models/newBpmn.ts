import { apiSaveTaskInfo, parseResponseResult } from '@/services'
import message from '@/utils/message'
interface TaskInfoParams {
  task_no: string
  data: string // formData
  experiment_design_no: string
}
export default () => {
  const saveTaskInfo = async (params: TaskInfoParams) => {
    const res = await apiSaveTaskInfo({
      data: params
    })
    if (!parseResponseResult(res).ok) return
    message.success({
      message: '保存成功',
      description: '可以继续进行下一步操作~'
    })
  }
  return {
    saveTaskInfo
  }
}
