import { matchResultToProcedure } from '@/pages/reaction/util'
import { Procedure, ProcedureRxnMatchResult, service } from '@/services/brain'
export default () => {
  const fetchProcedures = async (ids: number[]): Promise<Procedure[]> => {
    if (!ids.length) {
      return []
    }
    const res = await service<Procedure>('procedure/query', {
      method: 'post',
      data: { ids },
      normalizeData: false
    })
      .select()
      .get()
    return (res as unknown as ProcedureRxnMatchResult[])
      .sort((a, b) => b.id - a.id)
      .map(matchResultToProcedure)
  }

  return {
    fetchProcedures
  }
}
