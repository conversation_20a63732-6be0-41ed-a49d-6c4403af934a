import type { ProjectCompound } from '@/services/brain'
import { query, service } from '@/services/brain'
import { IOption } from '@/types/common'
import { changeKeyObjects } from '@/utils'
import type { LabeledValue } from 'antd/es/select'
import { useState } from 'react'
export default () => {
  const [moleculeList, setMoleculeList] = useState<LabeledValue[]>([])
  const [targetMoleculeList, setTargetMoleculeList] = useState<IOption[]>([])

  const queryTargetMoleculeList = async (projectId: string) => {
    const { data: targetCompounds } = await service<ProjectCompound>(
      'project-compounds'
    )
      .select(['id', 'no'])
      .filterDeep('project.id', 'eq', projectId)
      .equalTo('type', 'target')
      .populateWith('project_routes', ['id'])
      .get()
    let options: IOption[] = changeKeyObjects(targetCompounds, {
      id: 'value',
      no: 'label'
    })
    setTargetMoleculeList(options)
  }

  const queryMoleculeList = async (projectId: string) => {
    const { data } = await query<ProjectCompound>('project-compounds')
      .filterDeep('project.id', 'eq', projectId)
      .notEqualTo('type', 'temp_block')
      .populateWith('project_routes', ['id'])
      .populateDeep([
        {
          path: 'retro_processes',
          fields: ['id'],
          children: [{ key: 'retro_backbones', fields: ['id'] }]
        }
      ])
      .sortBy([{ field: 'no', order: 'asc' }])
      .paginate(1, 1000)
      .get()
    let _moleculeList = data?.map((u) => ({
      value: `${u?.no}`,
      label: `${u?.no}`
    })) as LabeledValue[]
    setMoleculeList(_moleculeList)
  }

  return {
    queryMoleculeList,
    queryTargetMoleculeList,
    targetMoleculeList,
    moleculeList
  }
}
