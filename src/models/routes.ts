import { ProjectRoute, service } from '@/services/brain'
import { IOption } from '@/types/Common'
import { changeKeyObjects } from '@/utils'
import { isNil } from 'lodash'
import { useState } from 'react'
export default () => {
  const [routesOption, setRoutesOption] = useState<ProjectRoute[]>([])
  const queryRoutesOption = async (
    projectId: string,
    projectRouteIds?: number[]
  ) => {
    const request = service<ProjectRoute>('project-routes')
      .select(['id', 'name'])
      .equalTo('status', 'confirmed')
      .filterDeep('project_compound.project.id', 'eq', projectId)
    if (projectRouteIds) request.filterDeep('id', 'in', projectRouteIds)
    const { data: routes } = await request.get()
    if (isNil(routes)) return
    let options: IOption[] = changeKeyObjects(routes, {
      id: 'value',
      name: 'label'
    })
    setRoutesOption(options)
  }

  return {
    queryRoutesOption,
    routesOption
  }
}
