import { DiffCost } from './../pages/projects/quotation-records/detail/index.d'

import { service, type IQuote } from '@/services/brain'
import { getWord } from '@/utils'
import { message } from 'antd'
import { cloneDeep } from 'lodash'
import { useState } from 'react'
export interface OtherQuoteCB {
  _curQuoteMoleculeId: string | null
  _repeatedWeight?: string
  _quotationNo?: string
}

export interface OtherQuote {
  purity?: number
  ratio?: number
  compound_no?: string
  target_weight: number
  target_unit: 'mg' | 'g'
  quote_id?: string
}
export type AmendOtherQuote = Pick<OtherQuote, 'target_unit' | 'target_weight'>

export default () => {
  const [diffCosts, setDiffCosts] = useState<DiffCost[]>([])
  const [quotesDetail, setQuotesDetail] = useState<IQuote | null>(null)
  const [isCheckedAll, setIsCheckedAll] = useState<boolean>(false)
  const [quoteCheckedList, setQuoteCheckedList] = useState<number[]>([])
  const updateQuotesDetail = async (newData: any) => {
    setQuotesDetail(newData)
  }

  const updatetDiffCosts = (_diffCosts: DiffCost[]) => {
    setDiffCosts(_diffCosts)
  }

  const updatetIsCheckedAll = (isCheckedAll: boolean) => {
    if (!isCheckedAll) setQuoteCheckedList([])
    setIsCheckedAll(isCheckedAll)
  }

  const updatetQuoteCheckedList = (ids: number[]) => setQuoteCheckedList(ids)
  const quoteCheckedListChange = (e: Event, id: number) => {
    let _checkedList = cloneDeep(quoteCheckedList)
    let isChecked: boolean = (e.target as HTMLInputElement).checked
    if (isChecked) _checkedList.push(id)
    else _checkedList = _checkedList.filter((e) => e !== id)
    setQuoteCheckedList(_checkedList)
  }

  const updateQuotes = async (
    quoteMoleculeId: string,
    values: any,
    tip?: string
  ) => {
    const { data, error } = await service(`quotes/${quoteMoleculeId}`, {
      method: 'PUT'
    }).create({ ...values, status: values?.status || 'editing' })
    if (error?.message) {
      message.error(error?.message)
      return false
    }
    updateQuotesDetail(data)
    message.success(tip || getWord('operate-success'))
    return true
  }

  /* 创建报价 或 其他重量报价 */
  const createQuote = async (
    parmas: OtherQuote,
    cb: (values: OtherQuoteCB) => void
  ) => {
    const { data, error } = await service('quote-requests').create({
      ...parmas
    })
    if (error) {
      if (error?.details?.error_code === 'QUOTE_PARAMS_ALREADY_EXIST') {
        cb({
          _curQuoteMoleculeId: null,
          _repeatedWeight: `${parmas.target_weight}${parmas.target_unit}`,
          _quotationNo: error?.details?.quote_request_no
        })
      } else {
        message.error(error?.message)
      }
    } else if (data && data?.quote_route[0]?.quote_id) {
      cb({
        _curQuoteMoleculeId: data?.quote_route[0]?.quote_id as string
      })
      message.success(getWord('operate-success'))
    }
  }

  return {
    quotesDetail,
    quoteCheckedList,
    updatetQuoteCheckedList,
    isCheckedAll,
    updatetIsCheckedAll,
    quoteCheckedListChange,
    createQuote,
    updateQuotes,
    updateQuotesDetail,
    updatetDiffCosts,
    diffCosts
  }
}
