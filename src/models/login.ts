import { query } from '@/services/brain'
import { UserSetting } from '@/types/models'
import { message } from 'antd'

export default () => {
  const getUserConfigs = async () => {
    const { data, error } = await query<UserSetting>('user-settings')
      .sortBy([{ field: 'setting_label', order: 'desc' }])
      .get()
    if (error) {
      message.error(error.message)
      return false
    }
    if (data) return data
  }

  return { getUserConfigs }
}
