import { unique } from '@/utils'

import { Project, query } from '@/services/brain'
import { message } from 'antd'
import type { LabeledValue } from 'antd/es/select'
import { isArray, isEmpty, isNil } from 'lodash'
import { useState } from 'react'
import { useModel } from 'umi'
export default () => {
  const { initialState } = useModel('@@initialState')
  const [projectInfo, setProjectInfo] = useState<Project>()
  const [userList, setUserList] = useState<LabeledValue[]>([])

  const getProjectInfo = async (projectId: string) => {
    const { data, error } = await query<Project>(`projects`)
      .equalTo('id', Number(projectId))
      .populateWith('project_members', ['user_id', 'role'], true)
      .get()
    if (error?.message) message.error(error?.message)
    if (!isArray(data) || isEmpty(data[0])) return
    if (data[0]?.project_members) {
      let _PM: string = ''
      for (const curItem of data[0]?.project_members) {
        if (isNil(curItem)) return
        if (curItem?.role?.code === 'pm') {
          let curPM = `${curItem?.user_info?.username}`
          _PM = _PM ? _PM.concat(`、${curPM}`) : `${curPM}`
        }
      }
      data[0] = {
        ...data[0],
        PM: _PM
      }
      setProjectInfo(data[0])
      let newData = unique(data[0]?.project_members, 'user_id')
      let _userList = newData?.map(
        (u) =>
          ({
            value: `${u?.user_id}`,
            label: `${u?.user_info?.username}`
          } || [
            {
              value: `${initialState?.userInfo?.id}`,
              label: initialState?.userInfo?.username
            }
          ])
      )
      setUserList(_userList)
    }
  }

  return {
    getProjectInfo,
    projectInfo,
    userList
  }
}
