import { apiExperimentException } from '@/services/experiment-exception'
import type { TaskExceptionItem } from '@/services/experiment-exception/index.d'
import { useState } from 'react'
export default () => {
  const [exceptionData, setExceptionData] = useState<TaskExceptionItem[]>([])
  const [exceptionLoading, setExceptionLoading] = useState<boolean>(false)
  const fetchExceptionData = async (taskNo: string, experimentNo: string) => {
    setExceptionLoading(true)
    const res = await apiExperimentException({
      routeParams: `${experimentNo}/${taskNo}`
    })
    setExceptionLoading(false)
    setExceptionData(res?.data?.exceptions || [])
  }
  const updateExceptionData = (values: TaskExceptionItem[]) =>
    setExceptionData(values)
  return {
    fetchExceptionData,
    updateExceptionData,
    exceptionLoading,
    exceptionData
  }
}
