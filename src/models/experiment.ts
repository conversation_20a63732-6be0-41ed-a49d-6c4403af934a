import { apiSearchExperimentCheck, parseResponseResult } from '@/services'
import { OperationCheckRequest, OperationCheckResponse } from '@/types/models'
import { useState } from 'react'
import { useParams } from 'umi'
export default () => {
  const [experimentListParams, setExperimentListParams] =
    useState<OperationCheckRequest>()
  const [experimentCheckList, setExperimentCheckList] = useState<
    OperationCheckResponse[]
  >([])

  const { reactionId } = useParams()
  const searchExperimentCheck = async (params?: OperationCheckRequest) => {
    if (params) setExperimentListParams(params)
    const res: { data: OperationCheckResponse } =
      await apiSearchExperimentCheck({
        data: {
          project_reaction_id: reactionId,
          ...params
        }
      })
    if (parseResponseResult(res).ok) setExperimentCheckList(res?.data)
  }

  return { searchExperimentCheck, experiment<PERSON>heck<PERSON>ist, experimentListParams }
}
