import { apiGetTaskInfo, apiGetTaskList, parseResponseResult } from '@/services'
import { cloneDeep, isObject, size } from 'lodash'
import { useState } from 'react'

export default () => {
  const [taskList, setTaskList] = useState(null)
  const [taskInfo, setTaskInfo] = useState<any>(null)

  const queryTaskList = async () => {
    const res = await apiGetTaskList({})
    if (parseResponseResult(res).ok) {
      sessionStorage.setItem('bpmnTaskList', JSON.stringify(res?.data))
      setTaskList(res?.data)
    }
  }

  const updateTaskInfo = (data: any) => {
    setTaskInfo(data)
  }

  /**
   * 获取task明细用于 Panel 展示
   */
  const handleDefaultData = (originData: any) => {
    let _originData = cloneDeep(originData)
    for (const item in originData) {
      if (isObject(originData[item])) {
        _originData = { ..._originData, ...originData[item] }
      }
    }
    console.log('---_originData---', _originData)
    return _originData
  }

  const getTaskInfo = async (taskId: string) => {
    updateTaskInfo([])
    const res = await apiGetTaskInfo({
      routeParams: taskId
    })
    if (
      parseResponseResult(res).ok &&
      res?.data?.task_schema?.properties &&
      size(res?.data?.task_schema?.properties) > 0
    ) {
      console.log('---task info---', res?.data)
      if (res?.data?.data) res.data.data = handleDefaultData(res.data.data)
      updateTaskInfo(res?.data)
      console.log('---默认值---', res?.data?.data)
      // console.log('---properties---', res?.data?.task_schema?.properties)
      // console.log('---校验失败的值---', res?.data?.validation_error)
    }
  }

  return {
    updateTaskInfo,
    getTaskInfo,
    queryTaskList,
    taskInfo,
    taskList
  }
}
