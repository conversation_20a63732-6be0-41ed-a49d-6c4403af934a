import { ImmerReducer } from 'umi'

export interface ThemeState {
  borderRadius: number
  // 基础主题色
  colorPrimary: string
  // 暗夜模式
  darkMode: boolean
}

export interface GlobalModelsType {
  state: ThemeState
  reducers: {
    changeState: ImmerReducer<any>
    handleColorPrimary: ImmerReducer<any>
    handleDarkMode: ImmerReducer<any>
  }
}

const ThemeModel: GlobalModelsType = {
  state: {
    borderRadius: 6,
    colorPrimary: '#1777ff',
    darkMode: false
  },
  reducers: {
    changeState(state: ThemeState, { payload }) {
      return { ...state, ...payload }
    },
    handleColorPrimary: (state, { payload }) => {
      colorPrimary = payload
    },
    handleDarkMode: (state, { payload }) => {
      darkMode = payload
    }
  }
}
export default ThemeModel
