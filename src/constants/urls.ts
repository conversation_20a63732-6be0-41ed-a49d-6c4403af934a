// ------route path
export const LOGIN_PATH = `/user/login`

// ------request
// export const LOGIN = `/connect/keycloak`
export const LOGIN = `/auth/realms/strapi/protocol/openid-connect/token`
export const LOGIN_LOCAL = `/auth/local`
export const USER_INFO = `/auth/keycloak/callback`
export const MS_USER_INFO = `/auth/microsoft/callback`
export const NEW_USER_INFO = `/me`
export const ROBOT_TASKS = `/robot-tasks`

/* 分子列表 */
export const TARGET_MOLECULES = `/target-molecules`
export const ROUTE_DETAIL = `/routes`

/* 实验设计 */
export const PROCEDURES = `/procedures`
export const EXPERIMENT_DESIGNS_LIST = `/experiment-designs/search` // 创建 获取列表 更新
export const EXPERIMENT_DESIGNS = `/experiment-designs` // 创建 获取列表 更新（PUT） {id} 获取单个设计
export const COPY_EXPERIMENT_DESIGNS = `/experiment-designs/copy` // 复制
export const FROM_PROCEDURE = `/experiment-designs/from-procedure` // 实验流程
export const EXPERIMENT_DESIGNS_PUBLISH = `/experiment-designs/publish` // 发布
export const SIMULATE_EXPERIMENT = `/experiment-designs/simulate` // 模拟实验
export const GENERATE_PROCEDURE = `/experiment-designs/to-format-procedure` // 生成格式化Procedure
export const EXPERIMENT_DESIGN_RESULTS = `/experiment-designs/get-experiment-results` // 获取实验结果 /{rxn_no}

/* bpmn */
export const EXPERIMENT_DESIGNS_TASK_LIST = `/experiment-designs/tasks/name` // 获取编辑器中所有的task List
export const MIX_DETAIL = `/experiment-designs/mix-detail` // 加料/投料 编辑模式 点击获取数据 GET
export const SAVE_MIX_DETAIL = `/experiment-designs/save-mix-detail` // 保存编辑加料参数弹窗 POST

/* 实验计划 */
export const EXPERIMENT_PLANS_LIST = `/experiment-plans/search` // 创建 获取列表
export const EXPERIMENT_PLANS = `/experiment-plans` // PUT 更新
export const EXPERIMENT_PLANS_PLAN = `/experiment-plans/experiment-plan` // PUT 根据实验编号获取实验计划信息 experiment-plans/experiment-plan/{experiment_no}
export const EXPERIMENT_PLANS_FEED = `/experiment-plans/feed` // 获取物料表投料量
// TODO 暂无使用场景 GET 获取实验计划详情 /experiment-plans/{plan_id}

/* 实验列表 */
export const EXPERIMENT_SEARCH = `/experiment/search` // 按条件搜索实验列表
export const EXPERIMENT_DETAIL = `/experiment` // 根据实验编号获取实验详情 /{experiment_no}
export const EXPERIMENT_STAUTS = `/experiment/status` // 设置实验状态
export const EXPERIMENT_REACTION_SEARCH = `/experiment/reaction-experiment-search` // 根据Project reaction ids 获取对应实验个数

/* 状态枚举 */
export const STATUS_ENUM = `/experiment/status/meta`
export const EXPERIMENT_DESIGNS_TASK = `/experiment-designs/task` // [http://vision-dev1.labwise.cn/admin/content-manager/collectionType/api::task-meta.task-meta?page=1&pageSize=100&sort=name:ASC]中 taskName_ 拼接 uuid，作为请求字段 task_no

/*  获取实验进度情况API integration and testing */
export const OPERATION_STATUS = `/operation`

/* Login */
export const ENVIRONMENT_CONFIG = `environment-config`

/** 实验结论 */
export const EXPERIMENT_RESULT = `/experiment/result` // 获取实验结论详情 /{experiment_no}
export const EXPERIMENT_CONCLUSION = `/experiment/conclusion` // 保存实验结论
export const EXPERIMENT_CONCLUTION_RESULT = `/experiment/conclusion/result` // 设置实验结果
export const EXPERIMENT_AUDITOR = `/experiment/auditor` // 确认审核

/** 异常记录 */
export const EXPERIMENT_EXCEPTION = `/experiment/exception` // 异常记录 /{experiment_no}/{task_no}
export const EXPERIMENT_EXCEPTION_METHOD = `/exception/handle-method`
export const EXPERIMENT_EXCEPTION_REASON_TYPE = `/exception/reason/types`

/* 加入知识库按钮跳转页面前调用更新数据 */
export const UPDATE_WORKFLOW_PARAM = `/experiment/update-workflow-param` // 异常记录 /{task_id}
export const ADD_REACTION_LIB = `/experiment-designs/add-reaction-lib` // 异常记录 /{task_id}

export const SMILES_WEIGHT = `/experiment-plans/smiles/mol/weight`

export const EXPERIMENT_ANALYSIS = `/experiment/analysis` // 获取实验的检测记录列表 /{experiment_no}
export const EXPERIMENT_ANALYSIS_CHECK = `/experiment/analysis/check` // 创建，保存检测分析记录

export const LAB_SOLVENTS = `/lab/solvents` // /{analysis_type}
export const VISIONKEY_STATS = `/visionkey-stats` // /api/visionkey-stats/{id}

export const DEL_QUOTE_NO = `/quote/delete_by_quote_no` // /:quote_request_no

/* 实验设计更新物料表 */
export const EXEPERIMENT_DESIGNS_MATERIAL = `/experiment-designs/material-table` // /:quote_request_no

/* 检测记录list */
export const EXEPERIMENT_CHECK_SEARCH = `/experiment/check/search`
// 获取反应的实验编号
export const EXEPERIMENT_PLAN_NO = `/experiment-plans/experiment_no`
export const EXEPERIMENT_CHECK_OPERATORS = `/experiment/check/operators`
export const EXEPERIMENT_ANALYSIS_REPORT = `/operation/check` // /{check_no}
export const UPLOAD_ANALYSIS_REPORT = `/experiment/check/analysis`
