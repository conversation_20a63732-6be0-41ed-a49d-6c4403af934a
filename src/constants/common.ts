export const initFilter = { page_no: 1, page_size: 10 }
export const initPagination = { current: 1, pageSize: 10 }

export const devToken =
  'f18c1c1834477576a1729c266e27dd61c353063fef883acafc18118164b96045f8ea9af9a706415adf157efe59bac78aea508e4fbf7cb0dfdd8442545286b73c933d0960bea903e53064dfd3d161d84486fc9edb3fd0da366ce06dca8b4cacfa19bb8dc4c1b42eab27f3c0105c47c249a13db7a7cbd931df4f05a4bdafdcf2a1'

export const defatulFilterInfo = {
  group: 'start_material',
  similarId: undefined,
  sortFiled: 'score',
  sortOrder: 'desc',
  knownReactionRateRange: [0, 100],
  scoreRange: [0, 100]
}
