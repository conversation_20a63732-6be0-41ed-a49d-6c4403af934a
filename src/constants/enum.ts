export enum reasonTypeDes {
  'op_exception' = '操作失误',
  'optimize' = '操作优化',
  'continue_last' = '上个异常的继续处理'
}

export enum statusDes {
  'created' = '草稿',
  'published' = '已发布',
  'canceled' = '已作废'
}

export enum statusColor {
  'created' = '#cd891f',
  'published' = '#f5f5f5',
  'success' = '#3eb0c0',
  'completed' = '#54a054',
  'canceled' = '#848484',
  'failed' = '#ebb9e5',
  'ready' = 'magenta',
  'running' = '#5081d0',
  'hold' = '#ba2b1b',
  'incident' = '#ecce2c'
}

export enum robotStatusColor {
  working = '#5081d0',
  holding = '#ba2b1b',
  error = '#ebb9e5',
  idle = '#54a054'
}
