import { getLocaleTag } from '@/components/BpmnEditor/utils'
import message from '@/components/BpmnEditor/utils/message'
import { getToken } from '@/utils'
import { useState } from 'react'

const useDownloadFile = () => {
  const [fileDownloading, setFileDownloading] = useState<boolean>()

  function downloadFile(
    path: string,
    fileName: string,
    method?: 'post' | 'get',
    data?: any
  ) {
    setFileDownloading(true)
    const xhr = new XMLHttpRequest()
    xhr.open(method || 'get', path)
    let token = getToken()
    xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    xhr.setRequestHeader('locale', getLocaleTag())
    xhr.setRequestHeader('Content-type', 'application/json')
    xhr.responseType = 'blob'
    xhr.send(data)
    xhr.onload = function () {
      if (this.status === 200 || this.status === 304) {
        const fileReader = new FileReader()
        fileReader.readAsDataURL(this.response)
        fileReader.onload = function () {
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = this.result
          console.log('文件加载完成！')
          a.download = fileName
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          console.log('返回你文件完成情况吧！')
          setFileDownloading(false)
        }
      } else {
        let blobData = xhr.response,
          reader = new FileReader()
        reader.onloadend = () => {
          let errorResult = JSON.parse(reader.result as string) // 获取错误提示信息
          if (errorResult?.error?.message)
            message.error({ message: errorResult?.error?.message })
          console.log('出错了哦！')
          setFileDownloading(false)
        }
        reader.readAsText(blobData) // 将 Blob 对象转换为文本
      }
    }
  }
  return { downloadFile, fileDownloading }
}

export default useDownloadFile
