import { ReactionRole } from '@/services/brain'
import { IOption } from '@/types/common'
import { omit } from 'lodash'
import { getWord } from '../utils/lang'

const useOptions = () => {
  const moleculeStatusOptions = [
    { value: 'created', label: getWord('molecules-status.created') },
    { value: 'designing', label: getWord('molecules-status.designing') },
    { value: 'synthesizing', label: getWord('molecules-status.synthesizing') },
    {
      value: 'finished',
      label: getWord('component.notification.statusValue.success')
    },
    {
      value: 'canceled',
      label: getWord('pages.projectTable.statusLabel.cancelled')
    }
  ]

  const reactionRoleOptions: {
    value: ReactionRole | string
    label: string
  }[] = [
    { value: 'product', label: getWord('product') },
    { value: 'main_reactant', label: getWord('main-reactant') },
    { value: 'reactant', label: getWord('reactant') },
    { value: 'other_reagent', label: getWord('other-reagent') }
  ]

  const groupOptions: IOption[] = [
    { label: getWord('same-key-material'), value: 'start_material' },
    { label: getWord('algorithm-cluster'), value: 'cluster' },
    { label: getWord('not-grouped'), value: 'ungrouped' }
  ]

  const proportionOptions: IOption[] = [
    { label: getWord('algorithmic-score'), value: 'score' },
    {
      label: getWord('known-reaction-proportion'),
      value: 'known_reaction_rate'
    },
    { label: getWord('longest-chain-l'), value: 'backbone_length' },
    { label: getWord('route-length'), value: 'min_n_main_tree_steps' }
  ]

  const sortStandard = {
    createdAt: getWord('creation-time'),
    updatedAt: getWord('last-update-time'),
    no: getWord('name')
  }

  const typeMap = {
    target: getWord('target-molecules'),
    building_block: getWord('key-intermediate'),
    temp_block: getWord('show-materials')
  }

  const typeMapForSelect = omit(typeMap, 'temp_block')

  const editableConfig = {
    onlyOneLineEditorAlertMessage: getWord('only-one-edit'),
    onlyAddOneLineAlertMessage: getWord('only-one-added')
  }

  const chargeDes = {
    total_cost: getWord('total'),
    material_cost: getWord('material-cost'),
    labor_cost: getWord('labor-cost')
  }

  const materialManageStauts = {
    draft: getWord('draft'),
    published: getWord('in-use'),
    deleted: getWord('deleted')
  }

  const aiAIInferenceStauts = {
    success: getWord('pages.experiment.statusLabel.success'),
    fail: getWord('pages.experiment.statusLabel.failed'),
    processing: getWord('pages.experiment.statusLabel.running')
  }

  const aiGenerateStauts = {
    limited: getWord('component.notification.statusValue.limited'),
    completed: getWord('component.notification.statusValue.success'),
    running: getWord('component.notification.statusValue.running'),
    pending: getWord('component.notification.statusValue.pending'),
    failed: getWord('component.notification.statusValue.failed')
  }

  const robotStatus = {
    working: getWord('working'),
    holding: getWord('hold'),
    error: getWord('unavailable'),
    idle: getWord('idle')
  }

  return {
    moleculeStatusOptions,
    reactionRoleOptions,
    groupOptions,
    proportionOptions,
    typeMap,
    sortStandard,
    typeMapForSelect,
    editableConfig,
    chargeDes,
    materialManageStauts,
    aiGenerateStauts,
    aiAIInferenceStauts,
    robotStatus
  }
}
export default useOptions
