import { ProcedureFilter } from '@/components/ReactionTabs/ReactionLibTab/useProcedure'
import { query, RetroParamConfig } from '@/services/brain'
import {
  UserQuotationSetting,
  UserRetroSetting,
  UserSetting
} from '@/services/brain/types/user-setting'
import { useQuery } from '@tanstack/react-query'
import { isNil } from 'lodash'
import { useEffect, useState } from 'react'

const userSettingParamLabels: string[] = ['max_search_time']

interface UserSettingData {
  retro?: Partial<UserRetroSetting>
  quote?: Partial<UserQuotationSetting>
  procedure?: Partial<ProcedureFilter>
  idMap: {
    retro?: number
    quote?: number
    procedure?: number
  }
}

export const fetchUserSetting = async (): Promise<UserSettingData> => {
  const { data } = await query<UserSetting>('user-settings').get()
  if (data?.length) {
    const res = data.reduce<UserSettingData>(
      (acc, cur) => {
        if (cur.setting_label === 'quotation') {
          acc.quote = cur.setting_value as UserQuotationSetting
          acc.idMap['quote'] = cur.id
        } else if (cur.setting_label === 'retro_params') {
          acc.retro = cur.setting_value as UserRetroSetting
          acc.idMap['retro'] = cur.id
        } else if (cur.setting_label === 'procedure') {
          acc.procedure = cur.setting_value as ProcedureFilter
          acc.idMap['procedure'] = cur.id
        }
        return acc
      },
      { idMap: {} } as UserSettingData
    )
    return res
  }
  return { idMap: {} }
}

export const useUserSetting = (): {
  setting: UserSettingData
  fetch: () => Promise<UserSettingData>
} => {
  const [setting, setSetting] = useState<UserSettingData>({ idMap: {} })

  useEffect(() => {
    let unmount = false

    fetchUserSetting().then((s) => !unmount && setSetting(s))

    return () => {
      unmount = true
    }
  }, [])

  return {
    setting,
    fetch: async () => {
      const data = await fetchUserSetting()
      setSetting(data)
      return data
    }
  }
}

export const useUserSettingQuery = () => {
  const { data, isLoading, refetch } = useQuery({
    queryKey: ['user-setting'],
    queryFn: fetchUserSetting
  })

  return { setting: data, isLoading, refetch }
}

export const updateParamsConfigDefaultWithUserSetting = (
  configs: RetroParamConfig[],
  setting: UserSettingData
): RetroParamConfig[] => {
  return configs.map((c) => {
    if (userSettingParamLabels.includes(c.field)) {
      const settingValue =
        setting?.retro?.[c.field as keyof UserSettingData['retro']]
      return {
        ...c,
        default_value: isNil(settingValue) ? c.default_value : settingValue
      }
    }
    return c
  })
}
