import { MaterialItem, query } from '@/services/brain'
import { inchifySmiles } from '@/utils/smiles'
import { groupBy, mapValues } from 'lodash'
import { useState } from 'react'

interface SmilesToCasHook {
  map: Record<string, string>
  add: (smiles: string[]) => Promise<string[]>
}

const fetchMaterialsBySmiles = async (
  smiles: string[]
): Promise<Record<string, MaterialItem[]>> => {
  const inchied = await inchifySmiles(smiles)
  const { data } = await query<MaterialItem>('material-items')
    .filterDeep('inchified_smiles', 'in', inchied.map(encodeURIComponent))
    .get()
  const materialsBySmiles = groupBy(data || [], (d) => d.inchified_smiles)
  return smiles.reduce<Record<string, MaterialItem[]>>((acc, cur, index) => {
    acc[cur] = materialsBySmiles[inchied[index]] || []
    return acc
  }, {})
}

export const useSmilesToCas = (): SmilesToCasHook => {
  const [map, setMap] = useState<Record<string, string>>({})

  const add = async (smiles: string[]): Promise<string[]> => {
    const map = await fetchMaterialsBySmiles(smiles)
    const mapWithCas = mapValues(
      map,
      (m) => m.map((m) => m.cas_no).filter((c) => !!c)[0] || ''
    )
    setMap((prev) => ({ ...prev, ...mapWithCas }))
    return smiles.map((s) => mapWithCas[s])
  }

  return { map, add }
}
