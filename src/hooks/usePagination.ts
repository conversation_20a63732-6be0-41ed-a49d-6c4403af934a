import { useState } from 'react'

interface Pagination {
  page: number
  pageSize: number
}

const usePagination = (
  defaultPage: number = 1,
  defaultPageSize: number = 10
) => {
  const [pagination, setPaginationState] = useState<Pagination>({
    page: defaultPage,
    pageSize: defaultPageSize
  })

  const setPage = (page: number) => {
    setPaginationState((prev) => ({ ...prev, page }))
  }

  const setPageSize = (pageSize: number) => {
    setPaginationState((prev) => ({ ...prev, pageSize }))
  }

  const setPagination = (pagination: Pagination) => {
    setPaginationState(pagination)
  }

  return {
    ...pagination,
    setPage,
    setPageSize,
    setPagination
  }
}

export default usePagination
