import { Project, query } from '@/services/brain'
import { uniqBy } from 'lodash'
import { useEffect, useState } from 'react'
import { useBrainFetch } from './useBrainFetch'

export const useProjectMembers = (projectId: number | number[]) => {
  const [members, setMembers] = useState<{ label: string; value: number }[]>([])
  const { fetch, loading } = useBrainFetch()

  const fetchProjectMemberOptions = async (projectId: number | number[]) => {
    const ids = typeof projectId === 'number' ? [projectId] : projectId
    if (!ids.length) {
      return []
    }

    const { data } = await fetch(
      query<Project>('projects')
        .filterDeep('id', 'in', ids)
        .populateWith('project_members')
        .get()
    )
    return (
      uniqBy(data?.[0]?.project_members, 'user_id')?.map((d) => ({
        label: d.user_info?.username || '',
        value: d.user_info?.id || -1
      })) || []
    )
  }

  useEffect(() => {
    let mounted = true
    if (projectId) {
      fetchProjectMemberOptions(projectId).then(
        (data) => mounted && setMembers(data)
      )
    }

    return () => {
      mounted = false
    }
  }, [projectId])
  return { members, loading }
}
