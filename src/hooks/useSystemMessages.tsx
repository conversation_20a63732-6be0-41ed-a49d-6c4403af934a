import { useBrainFetch } from '@/hooks/useBrainFetch'
import { Paginate, query } from '@/services/brain'
import { MessageReadersResponse } from '@/types/models'
import { formatYMDHMTime, getWord, isValidArray } from '@/utils'
import { useModel } from '@umijs/max'
import { isNil } from 'lodash'
import { useEffect, useState } from 'react'

const useSystemMessages = ({ readOnly }: { readOnly: boolean }) => {
  const { fetch, loading } = useBrainFetch()
  const { initialState } = useModel('@@initialState')
  const [reload, setReload] = useState<Record<never, never>>({})
  const [paginate, setPaginate] = useState<Paginate>({ page: 1, pageSize: 10 })
  const [systemMessagesDatas, setSystemMessagesDatas] = useState([])
  const [total, setTotal] = useState<number>(0)

  const formatMessageReaders = (data) => {
    let newMessageReaders = []
    data?.map((e) => {
      if (e?.system_message === null) return
      let oldMessage = getWord('test-report-inference-success-message')
      let newMessage = oldMessage.replace(
        /check_type|experiment_no|report_url/g,
        (match) => {
          switch (match) {
            case 'check_type':
              return e?.system_message?.check_type === 'M'
                ? getWord('intermediate-detection')
                : getWord('product-detection')
            case 'experiment_no':
              return e?.system_message?.experiment_no
            case 'report_url':
              return e?.system_message?.report_url
            case 'robot_start_handle_time':
              return formatYMDHMTime(e?.system_message?.robot_start_handle_time)
            default:
              return match
          }
        }
      )
      e.system_message.message = newMessage
      newMessageReaders.push(e)
    })
    return newMessageReaders
  }

  const getMessageReaders = async () => {
    const request = await query<MessageReadersResponse>(`message-readers`)
      .filterDeep('user_id', 'eq', initialState?.userInfo?.id)
      .populate(['system_message'])
      .sortBy([{ field: 'createdAt', order: 'desc' }])
    if (readOnly) {
      request.equalTo('readed', true)
    }
    const { data, meta } = await fetch(request.get())
    setTotal(meta?.pagination.total || 0)
    return isValidArray(data) ? formatMessageReaders(data) : []
  }

  const updateSystemMessagesDatas = async (newData) => {
    setSystemMessagesDatas(newData)
  }

  useEffect(() => {
    if (!initialState?.userInfo?.id) return
    getMessageReaders().then((d) => !isNil(d) && updateSystemMessagesDatas(d))
  }, [paginate, readOnly, initialState?.userInfo?.id])

  useEffect(() => {
    setPaginate({ page: 1, pageSize: 10 })
  }, [reload])

  return {
    loading,
    paginate,
    systemMessagesDatas,
    total,
    setPaginate,
    updateSystemMessagesDatas
  }
}

export default useSystemMessages
