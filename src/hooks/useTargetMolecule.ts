import { ProjectCompound, service } from '@/services/brain'
import message from '@/utils/message'
import { isNaN } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
export const useTargetMolecule = (
  moleculeId?: number | string
): { targetMolecule?: ProjectCompound; smiles: string } => {
  const [targetMolecule, setTargetMolecule] = useState<ProjectCompound>()
  const [smiles, setSmiles] = useState<string>('')
  /**
   * 分子卡片跳转 获取目标分子详情数据
   */
  const getMolecule = useCallback(async () => {
    const idNum =
      typeof moleculeId !== 'number'
        ? Number.parseInt(moleculeId || '')
        : moleculeId
    if (isNaN(idNum)) return
    const { data: compoundData, error } = await service<ProjectCompound>(
      'project-compounds'
    )
      .selectManyByID([idNum])
      .populateWith('project_routes', ['id'])
      .populateWith('compound', ['id', 'smiles'])
      .populateDeep([
        {
          path: 'retro_processes',
          fields: ['id'],
          children: [{ key: 'retro_backbones', fields: ['id'] }]
        }
      ])
      .populateWith('default_route', ['id'])
      .get()
    if (error) {
      message.error({
        message: '数据获取失败，请稍后再试或联系管理员',
        description: error.message as string
      })
    } else if (compoundData?.[0]) {
      const compound = compoundData[0]
      compound.project_routes_number = compound.project_routes?.length
      compound.retro_backbones_number = compound.retro_processes?.flatMap(
        (p) => p.retro_backbones
      ).length
      return compound
    }
  }, [moleculeId])

  useEffect(() => {
    getMolecule()
      .then((m) => {
        setTargetMolecule(m)
        setSmiles(m?.compound?.smiles || m?.input_smiles || '')
      })
      .catch(() => setTargetMolecule(undefined))
  }, [getMolecule])
  return { targetMolecule, smiles }
}
