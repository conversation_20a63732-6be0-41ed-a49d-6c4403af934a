import {
  EXPERIMENT_DESIGNS,
  EXPERIMENT_PLANS,
  EXPERIMENT_SEARCH
} from '@/constants'
import {
  apiExperimentDesigns,
  apiExperimentList,
  apiExperimentPlansList,
  parseResponseResult
} from '@/services'
import { useEffect, useState } from 'react'
export default function useFetchData(
  queryParams: any,
  path: string,
  refresh?: boolean
) {
  const [total, setTotal] = useState<number>(0)
  const [listData, setListData] = useState([])
  const [loading, setLoading] = useState(false)
  async function fetchDataByPage() {
    let res: any
    switch (path) {
      case EXPERIMENT_DESIGNS:
        res = await apiExperimentDesigns({ data: queryParams })
        break
      case EXPERIMENT_PLANS:
        res = await apiExperimentPlansList({ data: queryParams })
        break
      case EXPERIMENT_SEARCH:
        res = await apiExperimentList({ data: queryParams })
      default:
        break
    }
    if (parseResponseResult(res).ok) {
      const responseData: any = res?.data // TODO test isMockTime? mockExperimentDesignData:
      setListData(responseData?.data)
      setTotal(responseData?.meta?.total)
    }
    setLoading(false)
  }

  const requestData = () => {
    setLoading(true)
    fetchDataByPage()
  }

  useEffect(() => {
    setListData([])
    requestData()
  }, [queryParams])

  useEffect(() => {
    if (refresh) requestData()
  }, [refresh])

  return { loading, listData, total }
}
