import { getAiSession } from '@/components/LabBot/utils'
import { useBrainFetch } from '@/hooks/useBrainFetch'
import { getSignedUrl } from '@/pages/batch-retro/request'
import {
  Paginate,
  queryWithDefaultOrder,
  RetroProcess,
  service
} from '@/services/brain'
import {
  BatchRetroNotification,
  JobNotification,
  TempMaterialUpdateNotification
} from '@/services/brain/types/job-notification'
import { getEnvConfig, getToken, getWord } from '@/utils'
import { get, set } from '@/utils/storage'
import { history, useIntl, useModel } from '@umijs/max'
import { App } from 'antd'
import { camelCase, isNil } from 'lodash'
import { useEffect, useState } from 'react'
import { io } from 'socket.io-client'
import { useUserSetting } from './useUserSetting'

const cachedAnalyzedKey = 'cachedAnalyzed'

const removeCached = (
  project_id: number,
  compound_id: number,
  retro_id: number
) => {
  const cached: Record<string, number>[] = get(cachedAnalyzedKey)
  if (!cached) return
  const updated = cached?.filter(
    (c) =>
      c.project_id === project_id &&
      c.compound_id === compound_id &&
      c.retro_id === retro_id
  )
  set(cachedAnalyzedKey, updated)
}

const addCached = (
  project_id: number,
  compound_id: number,
  retro_id: number
) => {
  const cached: Record<string, number>[] = get(cachedAnalyzedKey) || []
  set(cachedAnalyzedKey, [...cached, { project_id, compound_id, retro_id }])
}

interface Params {
  unreadOnly?: boolean
  handleCheck?: (url: string) => void
}

const useNotification = ({
  unreadOnly: propUnreadOnly,
  handleCheck
}: Params = {}) => {
  const { fetch, loading } = useBrainFetch()
  const { initialState } = useModel('@@initialState')
  const { notification } = App.useApp()
  const [reload, setReload] = useState<Record<never, never>>({})
  const [unreadOnly, setUnreadOnly] = useState<boolean>(propUnreadOnly || false)
  const [notifications, setNotifications] = useState<JobNotification[]>([])
  const [paginate, setPaginate] = useState<Paginate>({ page: 1, pageSize: 10 })
  const [total, setTotal] = useState<number>(0)
  const [unreadCount, setUnreadCount] = useState<number>(0)
  const intl = useIntl()
  const {
    setting: { retro: { auto_analyze_route_project_ids } = {} }
  } = useUserSetting()
  const [analyzedParams] = useState<Set<string>>(new Set())

  const fetchNotifications = async (): Promise<JobNotification[] | null> => {
    const request = service<JobNotification>('job-notifications')
      .select([
        'id',
        'access_url',
        'job_id',
        'job_type',
        'status',
        'readed',
        'queue_time',
        'predict_start_time',
        'queue_count',
        'name'
      ])
      .sortBy([{ field: 'updatedAt', order: 'desc' }])
      .paginate(paginate.page, paginate.pageSize)
    if (unreadOnly) request.equalTo('readed', 'false')
    const { data, meta } = await fetch(request.get())
    setTotal(meta?.pagination.total || 0)
    return data || null
  }

  const fetchUnreadCount = async (callback: (d: number) => void) => {
    let xhr = new XMLHttpRequest()
    xhr.open('GET', `${getEnvConfig().apiBase}/api/message-reader/count`, true)
    xhr.setRequestHeader('Authorization', `Bearer ${getToken()}`)
    xhr.onreadystatechange = function () {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === 200) {
          let responseData = JSON.parse(xhr.responseText)
          callback(responseData?.count)
        } else {
          console.error('Error:', xhr.status)
        }
      }
    }
    xhr.send()
  }

  const startAutoAnalyze = async (
    project_id: number,
    compound_id: number,
    retro_id: number
  ) => {
    const pramsKey = `${project_id}-${compound_id}-${retro_id}`
    if (analyzedParams.has(pramsKey)) return
    analyzedParams.add(pramsKey)

    const session = await getAiSession({ project_id, compound_id, retro_id })
    if (session.initialize_finished) {
      removeCached(project_id, compound_id, retro_id)
      return
    }

    let id = 0
    let running = false
    const handle = async () => {
      if (running) return
      running = true
      const session = await getAiSession({ project_id, compound_id, retro_id })
      if (session.initialize_finished) {
        notification.success({ message: getWord('route-analyze-finished') })
        clearInterval(id)
        removeCached(project_id, compound_id, retro_id)
      }
      running = false
    }

    id = window.setInterval(handle, 5000)
    addCached(project_id, compound_id, retro_id)
  }

  const handleJobNotificationUpdate = (update: JobNotification) => {
    const projectId = Number.parseInt(
      update.access_url?.match(/\/projects\/(\d+)\//)?.[1] || ''
    )
    const doAutoAnalyze = auto_analyze_route_project_ids?.includes(projectId)
    const message = `${getWord('pages.Notification.task')} ${
      update?.name || update?.job_id || '-'
    } ${getWord('component.notification.statusValue.success')}`

    if (update.status !== 'success') {
      return
    }

    if (doAutoAnalyze && projectId) {
      const compound_id = Number.parseInt(
        update.access_url?.match(/\/compound\/(\d+)(\?|\/)/)?.[1] || ''
      )
      if (compound_id) {
        queryWithDefaultOrder<RetroProcess>('retro-processes', undefined, [
          'id'
        ])
          .filterDeep('project_compound.id', 'eq', compound_id)
          .paginate(1, 1)
          .get()
          .then(({ data }) => {
            const retro_process_id = data?.[0]?.id
            if (retro_process_id)
              startAutoAnalyze(projectId, compound_id, retro_process_id)
          })
      }
    }
    notification.success({
      message: (
        <>
          {message}
          {doAutoAnalyze && (
            <>
              <br />
              {getWord('start-route-analyze')}
            </>
          )}
        </>
      ),
      description: (
        <>
          {update.access_url && (
            <a
              onClick={() =>
                handleCheck && update.access_url
                  ? handleCheck(update.access_url)
                  : history.push(update.access_url)
              }
            >
              {getWord('view-results')}
            </a>
          )}
        </>
      )
    })
  }

  const notifyBatchRetro = async (
    msg: BatchRetroNotification | TempMaterialUpdateNotification
  ) => {
    const notify =
      msg.status === 'success' ? notification.success : notification.error
    const failedDownloadUrl =
      'failed_file_id' in msg.data && msg.data.failed_file_id
        ? await getSignedUrl(msg.data.failed_file_id)
        : ''
    const description =
      msg.type === 'batch-task' ? (
        <a href="/batch-retro">{getWord('view-results')}</a>
      ) : (
        intl.formatMessage(
          { id: 'pages.batchRetro.label.tempMaterialInfo' },
          {
            successCount: msg.data.success_count,
            failedCount: msg.data.failed_count,
            a: (chunk: string) => (
              <a href={failedDownloadUrl} target="_blank" rel="noreferrer">
                {chunk}
              </a>
            )
          }
        )
      )
    notify({
      message: getWord(
        `pages.batchRetro.label.${camelCase(`${msg.type}-${msg.status}`)}`
      ),
      description
    })
  }

  useEffect(() => {
    const cached = get(cachedAnalyzedKey)
    if (!cached) return
    for (const { project_id, compound_id, retro_id } of cached) {
      startAutoAnalyze(project_id, compound_id, retro_id)
    }
  }, [])

  useEffect(() => {
    if (!initialState?.userInfo?.id) return
    fetchUnreadCount((d) => setUnreadCount(d))
    fetchNotifications().then((d) => !isNil(d) && setNotifications(d))
  }, [paginate, initialState?.userInfo?.id])

  useEffect(() => {
    setPaginate({ page: 1, pageSize: 10 })
  }, [reload])

  useEffect(() => {
    if (!initialState?.userInfo?.id) return
    const socket = io(`${getEnvConfig().apiBase}`)
    socket.on('connect', () => {
      socket.emit('job-notification:join', initialState.userInfo.id)
      socket.emit('batch-retro:join', initialState.userInfo.id)
    })
    socket.on('job-notification:update', (updates: JobNotification[]) => {
      updates.forEach(handleJobNotificationUpdate)
      setReload({})
    })

    socket.on('batch-retro:update', notifyBatchRetro)
    return () => {
      socket.off('job-notification:update')
      socket.off('batch-retro:update')
    }
  }, [initialState?.userInfo?.id])

  return {
    notifications,
    unreadCount,
    loading,
    unreadOnly,
    setUnreadOnly,
    refetch: () => setReload({}),
    paginate,
    total,
    setPaginate
  }
}

export default useNotification
