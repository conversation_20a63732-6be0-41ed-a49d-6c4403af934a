import { SearchParamProps } from '@/pages/compound/components/SearchParam'
import { SearchParamFields } from '@/pages/compound/components/SearchParam/index.d'
import { useState } from 'react'

const useSearchParams = (): {
  getParams: () => Promise<SearchParamFields>
  props: SearchParamProps
} => {
  const [getEvent, setGetEvent] = useState<{
    onGetFilters?: (value: SearchParamFields) => void
  }>()

  const getParams = async (): Promise<SearchParamFields> =>
    new Promise((resolve) => {
      setGetEvent({ onGetFilters: (value) => resolve(value) })
    })

  return {
    getParams,
    props: { getFilterEvent: getEvent }
  }
}

export default useSearchParams
