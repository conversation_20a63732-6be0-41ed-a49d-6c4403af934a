import { StrapiResponse, service } from '@/services/brain'
import { App } from 'antd'
import { useState } from 'react'

export const useBrainFetch = (
  notifyMethod: string = 'message',
  normalizeData: boolean = true
) => {
  const { message, notification } = App.useApp()
  const [loading, setLoading] = useState<boolean>(false)
  const notify =
    notifyMethod === 'message'
      ? (info: string) => message.error(info)
      : (info: string) => notification.error({ message: info })

  return {
    fetch: async <T>(response: Promise<StrapiResponse<T>>) => {
      setLoading(true)
      const res = await response
      let { data, meta, error } = res
      if (!normalizeData) {
        data = res as T
      }
      setLoading(false)
      if (error || !data) {
        notify(error?.message || 'Request Error')
        return { data: null }
      } else {
        return { data, meta }
      }
    },
    loading,
    updateByStrapi: async (url: string, params: unknown, cb: () => void) => {
      const { error } = await service(url, {
        method: 'PUT'
      }).create(params)
      if (error?.message) return message.error(error?.message)
      if (cb) cb()
    }
  }
}
