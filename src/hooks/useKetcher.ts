import { KetcherWithInputEditorProps } from '@/components/MoleculeEditor/type'
import { getWord } from '@/utils'
import { inchifySmiles, kekulizeSmiles } from '@/utils/smiles'
import { App } from 'antd'
import { <PERSON><PERSON><PERSON> } from 'ketcher-core'
import { isEmpty, isNil } from 'lodash'
import { useState } from 'react'

export interface GetSmilesConfig {
  kekulize?: boolean
  inchify?: boolean
  validate?: boolean
  forbidMultiple?: boolean
}

const useKetcher = (
  smiles?: string
): {
  props: KetcherWithInputEditorProps
  ketcher?: Ketcher
  getSmiles: (config?: GetSmilesConfig) => Promise<string | false>
  setSmiles: (smiles: string) => void
} => {
  const [updateEvent, setUpdateEvent] = useState<Record<'update', string>>()
  const [ketcher, setKetcher] = useState<Ketcher>()
  const { message } = App.useApp()

  const getSmiles = async (config: GetSmilesConfig = {}) => {
    const {
      kekulize = true,
      inchify = false,
      validate = true,
      forbidMultiple = false
    } = config
    const ketcherSmiles = await ketcher?.getSmiles()
    if (!validate) {
      return ketcherSmiles || false
    }

    if (isNil(ketcherSmiles) || isEmpty(ketcherSmiles)) {
      message.error(getWord('no-molecule-tip'))
      return false
    }
    if (forbidMultiple && ketcherSmiles.includes('.')) {
      message.error('一次只能创建一个分子，如果您想创建多个分子，请分多次创建')
      return false
    }

    let smiles = ketcherSmiles
    if (kekulize) {
      const [kekulized] = await kekulizeSmiles([ketcherSmiles])
      smiles = kekulized
    }
    if (inchify) {
      const [inchifiedSmiles] = await inchifySmiles([smiles])
      smiles = inchifiedSmiles
    }

    if (!smiles.length) {
      message.error(getWord('valid-molecule-enter'))
      return false
    }
    return smiles
  }

  const setSmiles = (smiles?: string) => {
    setUpdateEvent({ update: smiles || '' })
  }

  return {
    ketcher,
    getSmiles,
    setSmiles,
    props: {
      getSmiles,
      setSmiles,
      onKetcherUpdate: setKetcher,
      updateEvent,
      initMoleculeSmiles: smiles
    }
  }
}

export default useKetcher
