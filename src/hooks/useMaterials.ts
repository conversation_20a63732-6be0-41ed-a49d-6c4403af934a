import { apiExperimentDesignDetail, parseResponseResult } from '@/services'
import { MaterialTable } from '@/services/brain'
import { useEffect, useState } from 'react'

export const useMaterials = (experimentDesignId: number) => {
  const [material, setMaterial] = useState<MaterialTable[]>([])
  const getDetailInfo = async () => {
    // let curId = JSON.parse(decodeUrl(String(experimentDesignId)))
    const res = await apiExperimentDesignDetail({
      routeParams: String(experimentDesignId) // curId
    })
    if (parseResponseResult(res).ok) {
      return res?.data?.materials
      // setExpErimentPlanDetail(res?.data)
      // setIsTraditionalType(res?.data?.mode === 'traditional')
    }
    return []
  }

  useEffect(() => {
    let mounted = true
    if (experimentDesignId) {
      getDetailInfo().then((data) => mounted && setMaterial(data))
    }
    return () => {
      mounted = false
    }
  }, [experimentDesignId])
  return { material }
}
