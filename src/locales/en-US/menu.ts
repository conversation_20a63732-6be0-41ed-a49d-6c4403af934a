export default {
  'menu.login': 'Login',
  'menu.dashboard': 'Dashboard',
  'menu.exception.403': '403',
  'menu.exception.404': '404',
  'menu.exception.500': '500',
  'menu.list.lab-management': 'Lab Management',
  'menu.list.playground': 'Internal Functions',
  'menu.list.playground.commend': 'Comments Review',
  'menu.list.playground.viewByBackbone': 'Routes Review',
  'menu.list.playground.AISandbox': 'AI Sandbox',
  'menu.list.workspace': 'Home',
  'menu.list.workspace.myWorkbench': 'My workbench',
  'menu.list.workspace.myCompound': 'My Molecules',
  'menu.list.workspace.myReaction': 'My reactions',
  'menu.list.workspace.myExperiment': 'My Experiments',
  'menu.list.project-list': 'Projects',
  'menu.list.project-list.detail': 'Project Details',
  'menu.list.project-list.detail.reaction': 'Reaction Details',
  'menu.list.project-list.detail.reaction.execute.detail': 'Experiment Details',
  'menu.list.project-list.detail.experimentalProcedure':
    'Experiment Design Details',
  'menu.list.project-list.detail.experiment-conclusion':
    'Experiment Conclusions',
  'menu.list.project-list.detail.experiment-conclusion.knowledgeBase':
    'Add To Knowledge Base',
  'menu.list.project-list.detail.experiment-conclusion.reportDetail':
    'Report Details',
  'menu.list.project-list.quotation': 'Quotation',
  'menu.list.project-list.detail.addMolecule': 'Create a Molecule',
  'add-molecule-in-route-tip': 'Add a Molecule',
  'menu.list.project-list.detail.addMolecule.tip':
    'Please Add a Molecule First',
  'menu.list.project-list.detail.quoteDetail': 'Quotation Details',
  'menu.list.project-list.detail.addQuote': 'Create a Quotation',
  'menu.list.project-list.detail.compound': 'Molecule Details',
  'menu.list.project-list.detail.compound.detail': 'Molecule Details',
  'menu.list.project-list.detail.compound.detail.create': 'Create route',
  'menu.list.project-list.detail.compound.viewByBackbone': 'Routes Review',
  'menu.list.project-list.detail.compound.edit': 'Edit Route',
  'menu.list.reaction': 'Reaction',
  'menu.list.reaction.detail': 'Reaction Detail',
  'menu.list.route': 'Route',
  'menu.list.route.view': 'Routes Review',
  'menu.list.route.viewByBackbone': 'Routes Review',
  'menu.list.route.edit': 'Edit Route',
  'menu.list.experiment.plan': 'Experiment Plan',
  'menu.list.experiment': 'Experiment',
  'menu.list.material-manage': 'Materials',
  'menu.list.material-manage.storage': 'Raw Materials Management',
  'menu.list.material-manage.search-molecule': 'Structural Formula Search',
  'menu.list.material-manage.black-list': 'Material Black List',
  'menu.list.material-manage.black-list.add': 'Add Material Black List',
  'menu.list.procedure': 'Experiment Procedure List',
  'menu.list.procedure.detail': 'Experiment Procedure Detail',
  'menu.list.experiment.execute': 'Experiment List',
  'menu.list.experiment.execute.detail': 'Experiment Detail',
  'menu.list.experiment.execute.conclusion': 'Experiment Conclusion',
  'menu.list.table-list': 'Search Table',
  'menu.list.basic-list': 'Basic List',
  'menu.list.card-list': 'Card List',
  'menu.list.search-list': 'Search List',
  'menu.list.search-list.articles': 'Search List(articles)',
  'menu.list.search-list.projects': 'Search List(projects)',
  'menu.list.search-list.applications': 'Search List(applications)',
  'menu.list.dashboard': 'Project Tracking',
  'menu.list.experimental-zone': 'Playground',
  'menu.list.experimental-zone.search': 'Retro with AI',
  'menu.list.lab-management.robot-details': 'Robot Details',
  'menu.list.batch-retro': 'Batch Retro',
  'menu.profile': 'Profile',
  'menu.profile.basic': 'Basic Profile',
  'menu.profile.advanced': 'Advanced Profile',
  'menu.result': 'Result',
  'menu.result.success': 'Success',
  'menu.result.fail': 'Fail',
  'menu.exception': 'Exception',
  'menu.exception.not-permission': '403',
  'menu.exception.not-find': '404',
  'menu.exception.server-error': '500',
  'menu.exception.trigger': 'Trigger',
  'menu.account': 'Account',
  'menu.account.center': 'Account Center',
  'menu.account.settings': 'Settings',
  'menu.account.trigger': 'Trigger Error',
  'menu.editor.mind': 'Mind Editor',
  'menu.editor.koni': 'Koni Editor',
  'pages.batchRetro.label.tempMaterialInfo': `{failedCount, plural,
  =0 {Published {successCount} materials}
  other {{successCount} materials published, <a>{failedCount} failed</a>}
}`
}
