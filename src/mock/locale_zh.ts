const Mock = require('mockjs')
const data = Mock.mock([
  {
    id: 3564,
    attributes: {
      code: 'test-method',
      value: '检测方法'
    }
  },
  {
    id: 3609,
    attributes: {
      code: 'developing-agent',
      value: '展开剂'
    }
  },
  {
    id: 3611,
    attributes: {
      code: 'operate-failed',
      value: '操作失败'
    }
  },
  {
    id: 3613,
    attributes: {
      code: 'ratio',
      value: '配比'
    }
  },
  {
    id: 3614,
    attributes: {
      code: 'show-read-messages',
      value: '查看已读消息'
    }
  },
  {
    id: 3616,
    attributes: {
      code: 'reaction-tab',
      value: '反应'
    }
  },
  {
    id: 3530,
    attributes: {
      code: 'task-name',
      value: '任务名称'
    }
  },
  {
    id: 3532,
    attributes: {
      code: 'task-status',
      value: '任务状态'
    }
  },
  {
    id: 3534,
    attributes: {
      code: 'start-time',
      value: '开始时间'
    }
  },
  {
    id: 3536,
    attributes: {
      code: 'end-time',
      value: '结束时间'
    }
  },
  {
    id: 3548,
    attributes: {
      code: 'pages.experiment.check.statusLabel.finished',
      value: '已完成'
    }
  },
  {
    id: 3550,
    attributes: {
      code: 'date',
      value: '日期'
    }
  },
  {
    id: 3586,
    attributes: {
      code: 'app.general.message.failed ',
      value: '失败'
    }
  },
  {
    id: 3553,
    attributes: {
      code: 'task-finished',
      value: 'Completed'
    }
  },
  {
    id: 3555,
    attributes: {
      code: 'task-todo',
      value: 'To be Done'
    }
  },
  {
    id: 3557,
    attributes: {
      code: 'task-canceled',
      value: 'Cancelled'
    }
  },
  {
    id: 3559,
    attributes: {
      code: 'task-running',
      value: 'Running'
    }
  },
  {
    id: 3561,
    attributes: {
      code: 'task-abnormalterminated',
      value: 'Abnormal Terminated'
    }
  },
  {
    id: 3562,
    attributes: {
      code: 'pages.experiment.label.operation.detectRecord',
      value: '检测记录'
    }
  },
  {
    id: 3563,
    attributes: {
      code: 'test-sample-id',
      value: '检测样品ID'
    }
  },
  {
    id: 3565,
    attributes: {
      code: 'test-launched-time',
      value: '检测发起时间'
    }
  },
  {
    id: 3566,
    attributes: {
      code: 'test-sponsor',
      value: '检测发起人'
    }
  },
  {
    id: 3567,
    attributes: {
      code: 'test-status',
      value: '检测状态'
    }
  },
  {
    id: 3618,
    attributes: {
      code: 'by-product',
      value: '副产物'
    }
  },
  {
    id: 3621,
    attributes: {
      code: 'other-and-impurity',
      value: '其他，包括杂质'
    }
  },
  {
    id: 3622,
    attributes: {
      code: 'intermediate-detection-next-action-tip',
      value: '机器人会'
    }
  },
  {
    id: 3624,
    attributes: {
      code: 'yes',
      value: '是'
    }
  },
  {
    id: 3631,
    attributes: {
      code: 'reference-report',
      value: '参考报告'
    }
  },
  {
    id: 3652,
    attributes: {
      code: 'progress',
      value: '当前进度'
    }
  },
  {
    id: 3654,
    attributes: {
      code: 'estimated-completed-date',
      value: '预计结束日期'
    }
  },
  {
    id: 3656,
    attributes: {
      code: 'experiment-actual-end-time',
      value: '实验实际结束时间'
    }
  },
  {
    id: 3658,
    attributes: {
      code: 'summary',
      value: '总体情况'
    }
  },
  {
    id: 3660,
    attributes: {
      code: 'product-color',
      value: '产物颜色'
    }
  },
  {
    id: 3590,
    attributes: {
      code: 'left-eye',
      value: '左眼'
    }
  },
  {
    id: 3591,
    attributes: {
      code: 'right-eye',
      value: '右眼'
    }
  },
  {
    id: 3662,
    attributes: {
      code: 'declaration',
      value: '声明'
    }
  },
  {
    id: 3665,
    attributes: {
      code: 'property',
      value: '产物性状'
    }
  },
  {
    id: 3666,
    attributes: {
      code: 'i',
      value: '本人'
    }
  },
  {
    id: 3668,
    attributes: {
      code: 'declare-tip',
      value: '已经完成本报告内容的审核'
    }
  },
  {
    id: 3538,
    attributes: {
      code: 'lab',
      value: '实验室'
    }
  },
  {
    id: 3540,
    attributes: {
      code: 'working',
      value: '工作中'
    }
  },
  {
    id: 3542,
    attributes: {
      code: 'idle',
      value: '空闲中'
    }
  },
  {
    id: 3544,
    attributes: {
      code: 'hold',
      value: '暂停中'
    }
  },
  {
    id: 3546,
    attributes: {
      code: 'unavailable',
      value: '不可用'
    }
  },
  {
    id: 2937,
    attributes: {
      code: 'molecules-no',
      value: '分子编号'
    }
  },
  {
    id: 2938,
    attributes: {
      code: 'molecules-status',
      value: '分子状态'
    }
  },
  {
    id: 2781,
    attributes: {
      code: 'blacklist',
      value: '黑名单'
    }
  },
  {
    id: 2939,
    attributes: {
      code: 'molecules-status.created',
      value: '待设计'
    }
  },
  {
    id: 2940,
    attributes: {
      code: 'molecules-status.designing',
      value: '设计中'
    }
  },
  {
    id: 2941,
    attributes: {
      code: 'molecules-status.synthesizing',
      value: '合成中'
    }
  },
  {
    id: 2942,
    attributes: {
      code: 'molecules-type',
      value: '分子类型'
    }
  },
  {
    id: 2943,
    attributes: {
      code: 'target-molecules',
      value: '目标分子'
    }
  },
  {
    id: 2944,
    attributes: {
      code: 'last-update-time',
      value: '最后修改时间'
    }
  },
  {
    id: 2945,
    attributes: {
      code: 'key-intermediate',
      value: '关键中间体'
    }
  },
  {
    id: 2948,
    attributes: {
      code: 'smiles-recognize',
      value: '结构式识别(支持jpg/png，图片大小不超过1M)'
    }
  },
  {
    id: 2876,
    attributes: {
      code: 'project-delivery-date',
      value: '项目交期'
    }
  },
  {
    id: 2877,
    attributes: {
      code: 'enter-molecule',
      value: '请输入分子'
    }
  },
  {
    id: 2878,
    attributes: {
      code: 'experimental-procedure-name',
      value: '请输入实验流程名称～'
    }
  },
  {
    id: 2879,
    attributes: {
      code: 'positive-integer-tip',
      value: '请输入正整数'
    }
  },
  {
    id: 2880,
    attributes: {
      code: 'enter-ex-name',
      value: '请输入实验名称'
    }
  },
  {
    id: 3016,
    attributes: {
      code: 'undo',
      value: '撤销'
    }
  },
  {
    id: 2958,
    attributes: {
      code: 'role',
      value: '角色'
    }
  },
  {
    id: 2962,
    attributes: {
      code: 'transfer-to-structure',
      value: '转结构'
    }
  },
  {
    id: 2772,
    attributes: {
      code: 'download-quatation',
      value: '下载报价单'
    }
  },
  {
    id: 2963,
    attributes: {
      code: 'del',
      value: '删除'
    }
  },
  {
    id: 2964,
    attributes: {
      code: 'reaction-no',
      value: '反应编号'
    }
  },
  {
    id: 3626,
    attributes: {
      code: 'no',
      value: '否'
    }
  },
  {
    id: 2965,
    attributes: {
      code: 'nums-of-my-reactions',
      value: '我的反应数量'
    }
  },
  {
    id: 2966,
    attributes: {
      code: 'nums-of-my-experiments',
      value: '我的实验数量'
    }
  },
  {
    id: 2967,
    attributes: {
      code: 'associated-routes',
      value: '关联路线'
    }
  },
  {
    id: 2968,
    attributes: {
      code: 'to-be-proceeded',
      value: '未推进'
    }
  },
  {
    id: 2969,
    attributes: {
      code: 'proceeded',
      value: '已推进'
    }
  },
  {
    id: 2970,
    attributes: {
      code: 'create-project-tip-I',
      value: '暂无项目'
    }
  },
  {
    id: 2971,
    attributes: {
      code: 'create-project-tip-II',
      value: '，请点击'
    }
  },
  {
    id: 2972,
    attributes: {
      code: 'create-project-tip-target',
      value: '【新建】'
    }
  },
  {
    id: 2973,
    attributes: {
      code: 'create-project-tip-III',
      value: '创建项目'
    }
  },
  {
    id: 2777,
    attributes: {
      code: 'save-draft',
      value: '存为草稿'
    }
  },
  {
    id: 3323,
    attributes: {
      code: 'menu.editor.koni',
      value: '拓扑编辑器'
    }
  },
  {
    id: 3324,
    attributes: {
      code: 'app.pwa.offline',
      value: '当前处于离线状态'
    }
  },
  {
    id: 3325,
    attributes: {
      code: 'app.pwa.serviceworker.updated',
      value: '有新内容'
    }
  },
  {
    id: 3120,
    attributes: {
      code: 'pages.experimentDesign.label.test',
      value: '小试'
    }
  },
  {
    id: 3343,
    attributes: {
      code: 'pages.projectTable.statusLabel.synthesizing',
      value: '合成中'
    }
  },
  {
    id: 3346,
    attributes: {
      code: 'cost-conflict-quote-double-check',
      value: '以下物料计算成本和成本不一致，是否确认报价？'
    }
  },
  {
    id: 3164,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleProps.title',
      value: '配置规则属性'
    }
  },
  {
    id: 3165,
    attributes: {
      code: 'pages.searchTable.updateForm.object',
      value: '监控对象'
    }
  },
  {
    id: 2838,
    attributes: {
      code: 'cancel-reason',
      value: '取消原因'
    }
  },
  {
    id: 2839,
    attributes: {
      code: 'start',
      value: '开始'
    }
  },
  {
    id: 2998,
    attributes: {
      code: 'comment',
      value: '评论'
    }
  },
  {
    id: 3449,
    attributes: {
      code: 'robot-model',
      value: '机器人型号'
    }
  },
  {
    id: 3450,
    attributes: {
      code: 'robot-status',
      value: '机器人状态'
    }
  },
  {
    id: 3451,
    attributes: {
      code: 'task-in-progress',
      value: '当前执行任务'
    }
  },
  {
    id: 3452,
    attributes: {
      code: 'robot-details',
      value: '机器人详情'
    }
  },
  {
    id: 3453,
    attributes: {
      code: 'pages.experiment.label.operation',
      value: '操作'
    }
  },
  {
    id: 3007,
    attributes: {
      code: 'export',
      value: '导出'
    }
  },
  {
    id: 3008,
    attributes: {
      code: 'view-reaction-lib',
      value: '查看反应库'
    }
  },
  {
    id: 3009,
    attributes: {
      code: 'copy-reaction',
      value: '复制当前反应'
    }
  },
  {
    id: 3010,
    attributes: {
      code: 'enlarge-molecule',
      value: '放大分子'
    }
  },
  {
    id: 3011,
    attributes: {
      code: 'copy-molecule',
      value: '复制分子'
    }
  },
  {
    id: 3012,
    attributes: {
      code: 'fetch-target-molecule',
      value: '以当前分子为目标分子搜索'
    }
  },
  {
    id: 3013,
    attributes: {
      code: 'del-molecule-routes',
      value: '删除当前分子及其合成路线'
    }
  },
  {
    id: 3014,
    attributes: {
      code: 'add-intermediate',
      value: '添加中间体'
    }
  },
  {
    id: 3454,
    attributes: {
      code: 'details',
      value: '详情'
    }
  },
  {
    id: 3455,
    attributes: {
      code: 'live',
      value: '直播'
    }
  },
  {
    id: 3456,
    attributes: {
      code: 'playback',
      value: '回放'
    }
  },
  {
    id: 3353,
    attributes: {
      code: 'please-quote',
      value: '请询价'
    }
  },
  {
    id: 3355,
    attributes: {
      code: 'please-input-positive-number',
      value: '请输入一个大于0的数字'
    }
  },
  {
    id: 2723,
    attributes: {
      code: 'quotate-price-tip',
      value:
        '表中原料价格的时效性为供应商列的标注日期，并非是供应商网站上的实时价格，请通过链接前往供应商的购买页面核对价格！'
    }
  },
  {
    id: 2724,
    attributes: {
      code: 'other-weight-quotate',
      value: '其他重量报价'
    }
  },
  {
    id: 3399,
    attributes: {
      code: 'cancel-reason',
      value: '取消原因'
    }
  },
  {
    id: 3400,
    attributes: {
      code: 'please-enter',
      value: '请输入'
    }
  },
  {
    id: 3029,
    attributes: {
      code: 'deselected',
      value: '取消选择'
    }
  },
  {
    id: 3031,
    attributes: {
      code: 'structural',
      value: '结构式'
    }
  },
  {
    id: 3375,
    attributes: {
      code: 'futures',
      value: '期货'
    }
  },
  {
    id: 3056,
    attributes: {
      code: 'pages.getCaptchaSecondText',
      value: '秒后重新获取'
    }
  },
  {
    id: 2983,
    attributes: {
      code: 'mode-selection',
      value: '模式选择'
    }
  },
  {
    id: 3126,
    attributes: {
      code: 'pages.experiment.label.no',
      value: '实验编号'
    }
  },
  {
    id: 3127,
    attributes: {
      code: 'pages.experiment.label.type',
      value: '实验类型'
    }
  },
  {
    id: 3128,
    attributes: {
      code: 'pages.experiment.label.procedureName',
      value: '反应名称'
    }
  },
  {
    id: 2794,
    attributes: {
      code: 'search-again',
      value: '重新搜索'
    }
  },
  {
    id: 2795,
    attributes: {
      code: 'no-search-log',
      value: '暂无搜索日志'
    }
  },
  {
    id: 2796,
    attributes: {
      code: 'error-detail',
      value: '详细错误信息：'
    }
  },
  {
    id: 3212,
    attributes: {
      code: 'AutoMix',
      value: '自动投料'
    }
  },
  {
    id: 3213,
    attributes: {
      code: 'MakeIngredients',
      value: '配料'
    }
  },
  {
    id: 2727,
    attributes: {
      code: 'try-replace-img',
      value: '请尝试换一张图片或在画板上编辑'
    }
  },
  {
    id: 2728,
    attributes: {
      code: 'choose-route-tip',
      value: '选中路线后，原来路线会被替换成该条路线，且不可撤回！'
    }
  },
  {
    id: 3017,
    attributes: {
      code: 'redo',
      value: '重做'
    }
  },
  {
    id: 3255,
    attributes: {
      code: 'menu.list.playground.viewByBackbone',
      value: '查看路线'
    }
  },
  {
    id: 3256,
    attributes: {
      code: 'menu.list.playground.AISandbox',
      value: 'AI沙盒'
    }
  },
  {
    id: 3257,
    attributes: {
      code: 'menu.list.workspace',
      value: '首页'
    }
  },
  {
    id: 3258,
    attributes: {
      code: 'menu.list.workspace.myWorkbench',
      value: '工作台'
    }
  },
  {
    id: 2974,
    attributes: {
      code: 'gnerate-routes',
      value: '生成路线'
    }
  },
  {
    id: 2975,
    attributes: {
      code: 'results',
      value: '搜索记录'
    }
  },
  {
    id: 2976,
    attributes: {
      code: 'success-results',
      value: '仅显示搜索成功记录'
    }
  },
  {
    id: 2977,
    attributes: {
      code: 'owner',
      value: '搜索人'
    }
  },
  {
    id: 2995,
    attributes: {
      code: 'new-group',
      value: '组'
    }
  },
  {
    id: 3149,
    attributes: {
      code: 'pages.experiment.statusLabel.failed',
      value: '失败'
    }
  },
  {
    id: 3628,
    attributes: {
      code: 'restore-robot-operations',
      value: '恢复机器人操作'
    }
  },
  {
    id: 3633,
    attributes: {
      code: 'show-reference-report',
      value: '仅展示参考报告'
    }
  },
  {
    id: 2814,
    attributes: {
      code: 'search',
      value: '搜索'
    }
  },
  {
    id: 2823,
    attributes: {
      code: 'select-all',
      value: '全选'
    }
  },
  {
    id: 3166,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleProps.templateLabel',
      value: '规则模板'
    }
  },
  {
    id: 3110,
    attributes: {
      code: 'pages.reaction.statusLabel.canceled',
      value: '已取消'
    }
  },
  {
    id: 3475,
    attributes: {
      code: 'product-not-identified-no-materials-remaining',
      value: '未出现产物，无原料剩余'
    }
  },
  {
    id: 3479,
    attributes: {
      code: 'continue',
      value: '继续实验'
    }
  },
  {
    id: 2775,
    attributes: {
      code: 'deleted',
      value: '已删除'
    }
  },
  {
    id: 2845,
    attributes: {
      code: 'modify-reaction-tip',
      value: '如果对反应进行修改，需要重新计算整个路线的报价'
    }
  },
  {
    id: 2949,
    attributes: {
      code: 'CAS-name-tip',
      value: 'CAS/中文名/英文名'
    }
  },
  {
    id: 2951,
    attributes: {
      code: 'molecule-priority',
      value: '分子优先级'
    }
  },
  {
    id: 2952,
    attributes: {
      code: 'molecule-owner',
      value: '分子负责人'
    }
  },
  {
    id: 2953,
    attributes: {
      code: 'max-l-50',
      value: '最长50个字符'
    }
  },
  {
    id: 2954,
    attributes: {
      code: 'max-l-30',
      value: '最长为30个字符'
    }
  },
  {
    id: 2955,
    attributes: {
      code: 'search-molecule',
      value: '查找分子'
    }
  },
  {
    id: 2956,
    attributes: {
      code: 'Routes',
      value: '路线'
    }
  },
  {
    id: 2957,
    attributes: {
      code: 'team-allocation',
      value: '人员配置'
    }
  },
  {
    id: 2961,
    attributes: {
      code: 'select-a-document',
      value: '选择文件'
    }
  },
  {
    id: 2924,
    attributes: {
      code: 'company',
      value: 'C12.ai'
    }
  },
  {
    id: 3109,
    attributes: {
      code: 'pages.reaction.statusLabel.confirmed',
      value: '已确认'
    }
  },
  {
    id: 3116,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.validated',
      value: '编辑中'
    }
  },
  {
    id: 3192,
    attributes: {
      code: 'pages.experiment.conclusion.label.phase.liquid',
      value: '液体'
    }
  },
  {
    id: 3459,
    attributes: {
      code: 'operation-details',
      value: '操作详情'
    }
  },
  {
    id: 2990,
    attributes: {
      code: 'confirm',
      value: '确定'
    }
  },
  {
    id: 2991,
    attributes: {
      code: 'route-length',
      value: '路线长度'
    }
  },
  {
    id: 2761,
    attributes: {
      code: 'weight',
      value: '质量'
    }
  },
  {
    id: 3359,
    attributes: {
      code: 'experiment-completed',
      value: '已完成'
    }
  },
  {
    id: 3267,
    attributes: {
      code: 'menu.list.project-list.detail.experiment-conclusion',
      value: '实验结论'
    }
  },
  {
    id: 3268,
    attributes: {
      code: 'menu.list.project-list.detail.experiment-conclusion.knowledgeBase',
      value: '加入知识库'
    }
  },
  {
    id: 3269,
    attributes: {
      code: 'menu.list.project-list.quotation',
      value: '报价'
    }
  },
  {
    id: 3284,
    attributes: {
      code: 'menu.list.reaction',
      value: '反应'
    }
  },
  {
    id: 3064,
    attributes: {
      code: 'pages.projectTable.label.no',
      value: '编号'
    }
  },
  {
    id: 3066,
    attributes: {
      code: 'pages.projectTable.label.type',
      value: '类型'
    }
  },
  {
    id: 3067,
    attributes: {
      code: 'pages.projectTable.label.status',
      value: '状态'
    }
  },
  {
    id: 3068,
    attributes: {
      code: 'pages.projectTable.label.pm',
      value: '项目经理'
    }
  },
  {
    id: 3072,
    attributes: {
      code: 'pages.projectTable.statusLabel.created',
      value: '待设计'
    }
  },
  {
    id: 3371,
    attributes: {
      code: 'spot-or-futures',
      value: '现货/期货'
    }
  },
  {
    id: 2693,
    attributes: {
      code: 'pattern-positive-number',
      value: '输入成本不合法，请输入一个大于0的数字'
    }
  },
  {
    id: 3387,
    attributes: {
      code: 'poisonous',
      value: '剧毒'
    }
  },
  {
    id: 3389,
    attributes: {
      code: 'explosive',
      value: '易爆'
    }
  },
  {
    id: 3391,
    attributes: {
      code: 'danger-reaction',
      value: '危险'
    }
  },
  {
    id: 3393,
    attributes: {
      code: 'reaction-reliability',
      value: '反应可靠度'
    }
  },
  {
    id: 3395,
    attributes: {
      code: 'has-procedure-only',
      value: '仅展示有procedure的反应'
    }
  },
  {
    id: 2843,
    attributes: {
      code: 'Mass',
      value: '投料量'
    }
  },
  {
    id: 2844,
    attributes: {
      code: 'no-auth-tip',
      value: '没有当前项目权限，请联系项目的管理者将您添加到项目成员中~'
    }
  },
  {
    id: 2873,
    attributes: {
      code: 'project-status',
      value: '项目状态'
    }
  },
  {
    id: 2874,
    attributes: {
      code: 'project-details',
      value: '项目信息'
    }
  },
  {
    id: 2786,
    attributes: {
      code: 'sort',
      value: '排序'
    }
  },
  {
    id: 3106,
    attributes: {
      code: 'pages.reaction.label.warn.productDiff',
      value: '产物和该反应不一致，请重新输入'
    }
  },
  {
    id: 3204,
    attributes: {
      code: 'Dry',
      value: '干燥'
    }
  },
  {
    id: 2978,
    attributes: {
      code: 'number-of-routes',
      value: '路线数量'
    }
  },
  {
    id: 2979,
    attributes: {
      code: 'creation-time',
      value: '创建时间'
    }
  },
  {
    id: 2980,
    attributes: {
      code: 'complete-time',
      value: '完成时间'
    }
  },
  {
    id: 2981,
    attributes: {
      code: 'parameters',
      value: '搜索参数'
    }
  },
  {
    id: 2881,
    attributes: {
      code: 'project-num',
      value: '项目编号'
    }
  },
  {
    id: 2882,
    attributes: {
      code: 'project-ID',
      value: '项目编号'
    }
  },
  {
    id: 2883,
    attributes: {
      code: 'input-cancel-reason',
      value: '请输入取消实验需求的原因'
    }
  },
  {
    id: 2884,
    attributes: {
      code: 'enter-reason',
      value: '请输入原因'
    }
  },
  {
    id: 2768,
    attributes: {
      code: 'created-by',
      value: '报价创建人：'
    }
  },
  {
    id: 2885,
    attributes: {
      code: 'enter-two-decimal',
      value: '请输入最多两位小数的正数'
    }
  },
  {
    id: 2886,
    attributes: {
      code: 'valid-molecule-enter',
      value: '请输入合法分子'
    }
  },
  {
    id: 2887,
    attributes: {
      code: 'no-molecule-tip',
      value: '没有输入分子！请输入后重新提交'
    }
  },
  {
    id: 3084,
    attributes: {
      code: 'pages.projectTable.typeValue.fte',
      value: 'FTE'
    }
  },
  {
    id: 3327,
    attributes: {
      code: 'app.pwa.serviceworker.updated.ok',
      value: '刷新'
    }
  },
  {
    id: 3119,
    attributes: {
      code: 'pages.experimentDesign.label.updateExperiment',
      value: '编辑实验'
    }
  },
  {
    id: 3069,
    attributes: {
      code: 'pages.project.settings',
      value: '项目设置'
    }
  },
  {
    id: 2780,
    attributes: {
      code: 'material-cost',
      value: '物料费'
    }
  },
  {
    id: 3483,
    attributes: {
      code: 'send-time',
      value: '发送时间'
    }
  },
  {
    id: 2784,
    attributes: {
      code: 'updated-at',
      value: '更新于'
    }
  },
  {
    id: 2749,
    attributes: {
      code: 'steps',
      value: '步骤'
    }
  },
  {
    id: 2750,
    attributes: {
      code: 'charge-items',
      value: '收费项'
    }
  },
  {
    id: 2751,
    attributes: {
      code: 'quotate-route',
      value: '报价路线'
    }
  },
  {
    id: 3144,
    attributes: {
      code: 'pages.experiment.label.operation.viewConclusion',
      value: '查看实验结论'
    }
  },
  {
    id: 3145,
    attributes: {
      code: 'pages.experiment.statusLabel.created',
      value: '待设计'
    }
  },
  {
    id: 2984,
    attributes: {
      code: 'fast-mode',
      value: '快捷模式'
    }
  },
  {
    id: 2985,
    attributes: {
      code: 'complex-mode',
      value: '精细模式'
    }
  },
  {
    id: 2986,
    attributes: {
      code: 'recommend-to-me',
      value: '为我推荐'
    }
  },
  {
    id: 2988,
    attributes: {
      code: 'raw-material-lib',
      value: '原料库'
    }
  },
  {
    id: 2996,
    attributes: {
      code: 'group',
      value: '分组'
    }
  },
  {
    id: 2997,
    attributes: {
      code: 'favorite',
      value: '收藏'
    }
  },
  {
    id: 3000,
    attributes: {
      code: 'intel-recommend',
      value: '智能推荐'
    }
  },
  {
    id: 3001,
    attributes: {
      code: 'all-routes',
      value: '所有路线'
    }
  },
  {
    id: 3002,
    attributes: {
      code: 'default-route',
      value: '默认路线'
    }
  },
  {
    id: 3003,
    attributes: {
      code: 'default-route-set',
      value: '设置默认路线成功'
    }
  },
  {
    id: 3005,
    attributes: {
      code: 'data-loading',
      value: '正在加载最新数据...'
    }
  },
  {
    id: 3006,
    attributes: {
      code: 'copy-to-clipboard',
      value: '已复制到剪贴板'
    }
  },
  {
    id: 3040,
    attributes: {
      code: 'pages.Notification.task-no',
      value: '任务编号'
    }
  },
  {
    id: 3041,
    attributes: {
      code: 'pages.my-workbench.todo-list',
      value: '待办事项'
    }
  },
  {
    id: 3042,
    attributes: {
      code: 'pages.layouts.userLayout.title',
      value: 'Build AI-Native R&D Labs'
    }
  },
  {
    id: 3043,
    attributes: {
      code: 'pages.login.accountLogin.errorMessage',
      value: '错误的用户名和密码(admin/ant.design)'
    }
  },
  {
    id: 3044,
    attributes: {
      code: 'pages.login.failure',
      value: '登录失败，请重试！'
    }
  },
  {
    id: 3045,
    attributes: {
      code: 'pages.login.success',
      value: '登录成功！'
    }
  },
  {
    id: 3046,
    attributes: {
      code: 'pages.login.username.required',
      value: '请输入用户名'
    }
  },
  {
    id: 3047,
    attributes: {
      code: 'pages.login.password.required',
      value: '请输入密码'
    }
  },
  {
    id: 3048,
    attributes: {
      code: 'pages.login.phoneLogin.tab',
      value: '手机号登录'
    }
  },
  {
    id: 3049,
    attributes: {
      code: 'pages.login.phoneLogin.errorMessage',
      value: '验证码错误'
    }
  },
  {
    id: 3050,
    attributes: {
      code: 'pages.login.phoneNumber.placeholder',
      value: '请输入手机号！'
    }
  },
  {
    id: 3309,
    attributes: {
      code: 'menu.profile.advanced',
      value: '高级详情页'
    }
  },
  {
    id: 3311,
    attributes: {
      code: 'menu.result.success',
      value: '成功页'
    }
  },
  {
    id: 3313,
    attributes: {
      code: 'menu.exception',
      value: '异常页'
    }
  },
  {
    id: 3314,
    attributes: {
      code: 'menu.exception.not-permission',
      value: '403'
    }
  },
  {
    id: 3315,
    attributes: {
      code: 'menu.exception.not-find',
      value: '404'
    }
  },
  {
    id: 3316,
    attributes: {
      code: 'menu.exception.server-error',
      value: '500'
    }
  },
  {
    id: 3317,
    attributes: {
      code: 'menu.exception.trigger',
      value: '触发错误'
    }
  },
  {
    id: 3318,
    attributes: {
      code: 'menu.account',
      value: '个人页'
    }
  },
  {
    id: 3319,
    attributes: {
      code: 'menu.account.center',
      value: '个人中心'
    }
  },
  {
    id: 3320,
    attributes: {
      code: 'menu.account.settings',
      value: '设置'
    }
  },
  {
    id: 3321,
    attributes: {
      code: 'menu.account.trigger',
      value: '触发报错'
    }
  },
  {
    id: 3322,
    attributes: {
      code: 'menu.editor.mind',
      value: '脑图编辑器'
    }
  },
  {
    id: 3350,
    attributes: {
      code: 'calculate-quotation',
      value: '计算报价'
    }
  },
  {
    id: 3222,
    attributes: {
      code: 'SetupReaction',
      value: '架设反应'
    }
  },
  {
    id: 2686,
    attributes: {
      code: 'generating-tip',
      value: '正在努力生成路线，请稍后～'
    }
  },
  {
    id: 3121,
    attributes: {
      code: 'pages.experimentDesign.label.scale_up',
      value: '放大'
    }
  },
  {
    id: 3122,
    attributes: {
      code: 'pages.experimentDesign.label.auto',
      value: '为我安排'
    }
  },
  {
    id: 3123,
    attributes: {
      code: 'pages.experimentDesign.label.asap',
      value: '尽快开始'
    }
  },
  {
    id: 3124,
    attributes: {
      code: 'pages.experimentDesign.label.custom',
      value: '期望时间'
    }
  },
  {
    id: 3125,
    attributes: {
      code: 'pages.experiment.label.name',
      value: '实验名称'
    }
  },
  {
    id: 3129,
    attributes: {
      code: 'pages.experiment.label.experimentDesignName',
      value: '实验设计名称'
    }
  },
  {
    id: 3130,
    attributes: {
      code: 'pages.experiment.label.status',
      value: '实验状态'
    }
  },
  {
    id: 3104,
    attributes: {
      code: 'pages.reaction.label.editReaction',
      value: '编辑反应'
    }
  },
  {
    id: 3105,
    attributes: {
      code: 'pages.reaction.label.createMaterialTableFromRxn',
      value: '通过反应smiles生成物料表'
    }
  },
  {
    id: 2925,
    attributes: {
      code: 'copyright',
      value: '版权所有'
    }
  },
  {
    id: 2926,
    attributes: {
      code: 'beian',
      value: '沪ICP备2022035008号-1'
    }
  },
  {
    id: 2927,
    attributes: {
      code: 'logout',
      value: '退出登录'
    }
  },
  {
    id: 3635,
    attributes: {
      code: 'publish',
      value: '发布'
    }
  },
  {
    id: 3568,
    attributes: {
      code: 'AI-inference-status',
      value: 'AI解读状态'
    }
  },
  {
    id: 2928,
    attributes: {
      code: 'noticeIcon.empty',
      value: '暂无数据'
    }
  },
  {
    id: 2929,
    attributes: {
      code: 'name',
      value: '名字'
    }
  },
  {
    id: 2930,
    attributes: {
      code: 'reset',
      value: '重置'
    }
  },
  {
    id: 2931,
    attributes: {
      code: 'Search',
      value: '查询'
    }
  },
  {
    id: 2934,
    attributes: {
      code: 'select-tip',
      value: '请选择'
    }
  },
  {
    id: 3117,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.canceled',
      value: '已取消'
    }
  },
  {
    id: 3118,
    attributes: {
      code: 'pages.experimentDesign.label.newExperiment',
      value: '新建实验'
    }
  },
  {
    id: 3292,
    attributes: {
      code: 'menu.list.material-manage.black-list.add',
      value: '添加黑名单原料'
    }
  },
  {
    id: 3294,
    attributes: {
      code: 'menu.list.procedure.detail',
      value: '实验流程详情'
    }
  },
  {
    id: 3295,
    attributes: {
      code: 'menu.list.experiment.execute',
      value: '实验列表'
    }
  },
  {
    id: 3296,
    attributes: {
      code: 'menu.list.experiment.execute.detail',
      value: '实验详情'
    }
  },
  {
    id: 3297,
    attributes: {
      code: 'menu.list.table-list',
      value: '查询表格'
    }
  },
  {
    id: 3298,
    attributes: {
      code: 'menu.list.basic-list',
      value: '标准列表'
    }
  },
  {
    id: 3131,
    attributes: {
      code: 'pages.experiment.label.priority',
      value: '实验优先级'
    }
  },
  {
    id: 3132,
    attributes: {
      code: 'pages.experiment.label.actualStartTime',
      value: '开始日期'
    }
  },
  {
    id: 3133,
    attributes: {
      code: 'pages.experiment.label.actualEndTime',
      value: '结束日期'
    }
  },
  {
    id: 2817,
    attributes: {
      code: 'no-AI-returned-create-I',
      value: '暂无路线，请点击'
    }
  },
  {
    id: 2767,
    attributes: {
      code: 'created-at',
      value: '报价创建时间：'
    }
  },
  {
    id: 3135,
    attributes: {
      code: 'pages.experiment.label.personInCharge',
      value: '实验负责人'
    }
  },
  {
    id: 3136,
    attributes: {
      code: 'pages.experiment.label.operation',
      value: '操作'
    }
  },
  {
    id: 3137,
    attributes: {
      code: 'pages.experiment.label.operation.view',
      value: '查看实验流程'
    }
  },
  {
    id: 3138,
    attributes: {
      code: 'pages.experiment.label.operation.edit',
      value: '编辑'
    }
  },
  {
    id: 3139,
    attributes: {
      code: 'pages.experiment.label.operation.cancel',
      value: '取消'
    }
  },
  {
    id: 3140,
    attributes: {
      code: 'pages.experiment.label.operation.conclution',
      value: '实验结论'
    }
  },
  {
    id: 3141,
    attributes: {
      code: 'pages.experiment.label.operation.stop',
      value: '终止'
    }
  },
  {
    id: 3142,
    attributes: {
      code: 'pages.experiment.label.operation.pause',
      value: '暂停'
    }
  },
  {
    id: 3143,
    attributes: {
      code: 'pages.experiment.label.operation.resume',
      value: '恢复'
    }
  },
  {
    id: 3146,
    attributes: {
      code: 'pages.experiment.statusLabel.running',
      value: '进行中'
    }
  },
  {
    id: 3147,
    attributes: {
      code: 'pages.experiment.statusLabel.hold',
      value: '暂停中'
    }
  },
  {
    id: 2818,
    attributes: {
      code: 'no-AI-returned',
      value: '无AI路线生成'
    }
  },
  {
    id: 2819,
    attributes: {
      code: 'no-routes-create-tip',
      value: '暂无路线，请保存或确认已有的AI路线或者点击'
    }
  },
  {
    id: 3148,
    attributes: {
      code: 'pages.experiment.statusLabel.canceled',
      value: '已取消'
    }
  },
  {
    id: 2830,
    attributes: {
      code: 'actual-mass',
      value: '实际投料量'
    }
  },
  {
    id: 2831,
    attributes: {
      code: 'expected-mass',
      value: '理论投料量'
    }
  },
  {
    id: 3154,
    attributes: {
      code: 'pages.experiment.check.statusLabel.checking',
      value: '待检测'
    }
  },
  {
    id: 3155,
    attributes: {
      code: 'pages.experiment.check.statusLabel.canceled',
      value: '已取消'
    }
  },
  {
    id: 3156,
    attributes: {
      code: 'pages.searchTable.createForm.newRule',
      value: '新建规则'
    }
  },
  {
    id: 3157,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleConfig',
      value: '规则配置'
    }
  },
  {
    id: 3158,
    attributes: {
      code: 'pages.searchTable.updateForm.basicConfig',
      value: '基本信息'
    }
  },
  {
    id: 3159,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleName.nameLabel',
      value: '规则名称'
    }
  },
  {
    id: 3160,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleName.nameRules',
      value: '请输入规则名称！'
    }
  },
  {
    id: 3161,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleDesc.descLabel',
      value: '规则描述'
    }
  },
  {
    id: 3162,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleDesc.descPlaceholder',
      value: '请输入至少五个字符'
    }
  },
  {
    id: 2914,
    attributes: {
      code: 'entire-material-lib',
      value: '全部原料库'
    }
  },
  {
    id: 2915,
    attributes: {
      code: 'operate-success',
      value: '操作成功'
    }
  },
  {
    id: 3163,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleDesc.descRules',
      value: '请输入至少五个字符的规则描述！'
    }
  },
  {
    id: 3167,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleProps.typeLabel',
      value: '规则类型'
    }
  },
  {
    id: 3332,
    attributes: {
      code: 'click-icon-to-copy',
      value: '点击复制CAS'
    }
  },
  {
    id: 3333,
    attributes: {
      code: 'click-to-find-cas',
      value: '点击查看CAS'
    }
  },
  {
    id: 3334,
    attributes: {
      code: 'cas-not-found',
      value: '未查询到CAS'
    }
  },
  {
    id: 3168,
    attributes: {
      code: 'pages.searchTable.updateForm.schedulingPeriod.title',
      value: '设定调度周期'
    }
  },
  {
    id: 3169,
    attributes: {
      code: 'pages.searchTable.updateForm.schedulingPeriod.timeLabel',
      value: '开始时间'
    }
  },
  {
    id: 3170,
    attributes: {
      code: 'pages.searchTable.updateForm.schedulingPeriod.timeRules',
      value: '请选择开始时间！'
    }
  },
  {
    id: 3569,
    attributes: {
      code: 'view-report',
      value: '查看报告'
    }
  },
  {
    id: 3171,
    attributes: {
      code: 'pages.searchTable.titleDesc',
      value: '描述'
    }
  },
  {
    id: 2798,
    attributes: {
      code: 'search-been-created',
      value: '已生成搜索任务！'
    }
  },
  {
    id: 2792,
    attributes: {
      code: 'cancel-warning',
      value: '取消后不可撤回'
    }
  },
  {
    id: 2793,
    attributes: {
      code: 'tasks-in-queue',
      value: '排队任务数'
    }
  },
  {
    id: 2801,
    attributes: {
      code: 'compound-not-exist',
      value: '未找到分子'
    }
  },
  {
    id: 2802,
    attributes: {
      code: 'try-diff-search',
      value: '请尝试换一个搜索词或在画板上编辑'
    }
  },
  {
    id: 2803,
    attributes: {
      code: 'cancel-task-confirm',
      value: '确认取消该任务？'
    }
  },
  {
    id: 2804,
    attributes: {
      code: 'estimate-start',
      value: '预计搜索开始时间'
    }
  },
  {
    id: 2805,
    attributes: {
      code: 'apply',
      value: '应用'
    }
  },
  {
    id: 2833,
    attributes: {
      code: 'not-grouped',
      value: '不分组'
    }
  },
  {
    id: 2809,
    attributes: {
      code: 'set-default-route',
      value: '设为默认路线'
    }
  },
  {
    id: 2810,
    attributes: {
      code: 'copy-route',
      value: '复制路线'
    }
  },
  {
    id: 2811,
    attributes: {
      code: 'copy-full',
      value: '复制全文'
    }
  },
  {
    id: 2812,
    attributes: {
      code: 'material-demand',
      value: '物料需求'
    }
  },
  {
    id: 2813,
    attributes: {
      code: 'search-failed-I',
      value: '搜索失败'
    }
  },
  {
    id: 2815,
    attributes: {
      code: 'experiment-design',
      value: '实验设计'
    }
  },
  {
    id: 2816,
    attributes: {
      code: 'no-AI-returned-create-II',
      value: '发起搜索'
    }
  },
  {
    id: 3172,
    attributes: {
      code: 'pages.searchTable.ruleName',
      value: '规则名称为必填项'
    }
  },
  {
    id: 3173,
    attributes: {
      code: 'pages.searchTable.titleCallNo',
      value: '服务调用次数'
    }
  },
  {
    id: 3174,
    attributes: {
      code: 'pages.searchTable.titleStatus',
      value: '状态'
    }
  },
  {
    id: 3175,
    attributes: {
      code: 'pages.searchTable.nameStatus.default',
      value: '关闭'
    }
  },
  {
    id: 3176,
    attributes: {
      code: 'pages.searchTable.nameStatus.running',
      value: '运行中'
    }
  },
  {
    id: 3177,
    attributes: {
      code: 'pages.searchTable.nameStatus.online',
      value: '已上线'
    }
  },
  {
    id: 2825,
    attributes: {
      code: 'last-modified-time',
      value: '最近修改时间'
    }
  },
  {
    id: 2826,
    attributes: {
      code: 'experiment-log',
      value: '实验记录'
    }
  },
  {
    id: 2827,
    attributes: {
      code: 'reaction-parameters',
      value: '反应参数'
    }
  },
  {
    id: 2828,
    attributes: {
      code: 'batch-add-blacklist',
      value: '批量加入黑名单'
    }
  },
  {
    id: 2829,
    attributes: {
      code: 'reson-add-blacklist',
      value: '加入黑名单原因'
    }
  },
  {
    id: 3179,
    attributes: {
      code: 'pages.searchTable.titleUpdatedAt',
      value: '上次调度时间'
    }
  },
  {
    id: 3180,
    attributes: {
      code: 'pages.searchTable.exception',
      value: '请输入异常原因！'
    }
  },
  {
    id: 3181,
    attributes: {
      code: 'pages.searchTable.config',
      value: '配置'
    }
  },
  {
    id: 3182,
    attributes: {
      code: 'pages.searchTable.subscribeAlert',
      value: '订阅警报'
    }
  },
  {
    id: 3183,
    attributes: {
      code: 'pages.searchTable.title',
      value: '查询表格'
    }
  },
  {
    id: 3184,
    attributes: {
      code: 'pages.searchTable.new',
      value: '新建'
    }
  },
  {
    id: 3185,
    attributes: {
      code: 'pages.searchTable.chosen',
      value: '已选择'
    }
  },
  {
    id: 3186,
    attributes: {
      code: 'pages.searchTable.item',
      value: '项'
    }
  },
  {
    id: 3187,
    attributes: {
      code: 'pages.searchTable.totalServiceCalls',
      value: '服务调用次数总计'
    }
  },
  {
    id: 3188,
    attributes: {
      code: 'pages.searchTable.tenThousand',
      value: '万'
    }
  },
  {
    id: 3189,
    attributes: {
      code: 'pages.searchTable.batchDeletion',
      value: '批量删除'
    }
  },
  {
    id: 3085,
    attributes: {
      code: 'pages.projectTable.typeValue.ffs',
      value: 'FFS'
    }
  },
  {
    id: 3086,
    attributes: {
      code: 'pages.projectTable.typeValue.personal',
      value: '个人项目'
    }
  },
  {
    id: 3087,
    attributes: {
      code: 'pages.projectTable.actionLabel.viewDetail',
      value: '查看'
    }
  },
  {
    id: 2710,
    attributes: {
      code: 'confirm-the-route',
      value: '确认路线'
    }
  },
  {
    id: 3190,
    attributes: {
      code: 'pages.searchTable.batchApproval',
      value: '批量审批'
    }
  },
  {
    id: 3460,
    attributes: {
      code: 'chat-with',
      value: '与{机器人名称}的对话'
    }
  },
  {
    id: 2916,
    attributes: {
      code: 'yield',
      value: '收率'
    }
  },
  {
    id: 2919,
    attributes: {
      code: 'reaction-details',
      value: '反应明细'
    }
  },
  {
    id: 2920,
    attributes: {
      code: 'cite',
      value: '引用'
    }
  },
  {
    id: 2921,
    attributes: {
      code: 'same-reaction',
      value: '相同反应'
    }
  },
  {
    id: 3191,
    attributes: {
      code: 'pages.experiment.conclusion.label.phase.solid',
      value: '固体'
    }
  },
  {
    id: 3193,
    attributes: {
      code: 'status',
      value: '状态'
    }
  },
  {
    id: 3194,
    attributes: {
      code: 'ID-contains-spaces',
      value: '编号中包含空格'
    }
  },
  {
    id: 3195,
    attributes: {
      code: 'ID-empty-tip',
      value: '编号为空'
    }
  },
  {
    id: 3196,
    attributes: {
      code: 'ID-exists-tip',
      value: '编号已存在，当前修改未生效'
    }
  },
  {
    id: 3197,
    attributes: {
      code: 'enter-ID-tip',
      value: '请输入编号'
    }
  },
  {
    id: 3198,
    attributes: {
      code: 'experiment-procedure-ID',
      value: '实验流程编号'
    }
  },
  {
    id: 3199,
    attributes: {
      code: 'STARTEVENT',
      value: '创建开始事件'
    }
  },
  {
    id: 3200,
    attributes: {
      code: 'ENDEVENT',
      value: '创建结束事件'
    }
  },
  {
    id: 3570,
    attributes: {
      code: 'upload-report',
      value: '上传报告'
    }
  },
  {
    id: 3571,
    attributes: {
      code: 'new-test',
      value: '发起检测'
    }
  },
  {
    id: 3572,
    attributes: {
      code: 'read',
      value: '已读'
    }
  },
  {
    id: 3589,
    attributes: {
      code: 'usage-right',
      value: '右手'
    }
  },
  {
    id: 3588,
    attributes: {
      code: 'usage-left',
      value: '左手'
    }
  },
  {
    id: 3637,
    attributes: {
      code: 'switch-to-workflow',
      value: '转化为流程'
    }
  },
  {
    id: 3202,
    attributes: {
      code: 'Crystal',
      value: '析晶'
    }
  },
  {
    id: 3203,
    attributes: {
      code: 'Dissolve',
      value: '溶解'
    }
  },
  {
    id: 3206,
    attributes: {
      code: 'DrySolution',
      value: '干燥有机相'
    }
  },
  {
    id: 3207,
    attributes: {
      code: 'Exsolution',
      value: '脱溶'
    }
  },
  {
    id: 3210,
    attributes: {
      code: 'GetMaterial',
      value: '领料'
    }
  },
  {
    id: 3214,
    attributes: {
      code: 'MicroWaveReaction',
      value: '微波反应'
    }
  },
  {
    id: 3216,
    attributes: {
      code: 'PhaseSeparate',
      value: '分液'
    }
  },
  {
    id: 3217,
    attributes: {
      code: 'Purify',
      value: '湿法上样过柱'
    }
  },
  {
    id: 3218,
    attributes: {
      code: 'Quench',
      value: '淬灭'
    }
  },
  {
    id: 3219,
    attributes: {
      code: 'Recrystal',
      value: '重结晶'
    }
  },
  {
    id: 3220,
    attributes: {
      code: 'Reaction',
      value: '反应'
    }
  },
  {
    id: 3221,
    attributes: {
      code: 'ReturnMaterial',
      value: '还料'
    }
  },
  {
    id: 3223,
    attributes: {
      code: 'TLC',
      value: 'TLC点板'
    }
  },
  {
    id: 3445,
    attributes: {
      code: 'lab-supervisor',
      value: '实验室负责人'
    }
  },
  {
    id: 3446,
    attributes: {
      code: 'robot-count',
      value: '机器人数量'
    }
  },
  {
    id: 2788,
    attributes: {
      code: 'only-one-added',
      value: '只能同时新增一行'
    }
  },
  {
    id: 3338,
    attributes: {
      code: 'default-material-sources-for-quotation',
      value: '报价用原料库'
    }
  },
  {
    id: 2797,
    attributes: {
      code: 'route-generate-failed',
      value: '生成路线失败，请稍后再试或联系管理员'
    }
  },
  {
    id: 2947,
    attributes: {
      code: 'myRoutes',
      value: '我的路线'
    }
  },
  {
    id: 2933,
    attributes: {
      code: 'input-tip',
      value: '请输入'
    }
  },
  {
    id: 3004,
    attributes: {
      code: 'new-route',
      value: '新建路线'
    }
  },
  {
    id: 3075,
    attributes: {
      code: 'pages.projectTable.statusLabel.cancelled',
      value: '已取消'
    }
  },
  {
    id: 3205,
    attributes: {
      code: 'PH',
      value: 'PH'
    }
  },
  {
    id: 3231,
    attributes: {
      code: 'AGCT',
      value: '激活全局连接工具'
    }
  },
  {
    id: 2832,
    attributes: {
      code: 'no-routes-returned',
      value: '没有满足条件的路线'
    }
  },
  {
    id: 2834,
    attributes: {
      code: 'algorithm-cluster',
      value: '算法聚类'
    }
  },
  {
    id: 3344,
    attributes: {
      code: 'yuan',
      value: '元'
    }
  },
  {
    id: 2835,
    attributes: {
      code: 'same-key-material',
      value: '关键起始物料一致'
    }
  },
  {
    id: 2836,
    attributes: {
      code: 'reason',
      value: '原因'
    }
  },
  {
    id: 2837,
    attributes: {
      code: 'reason-type',
      value: '原因类型'
    }
  },
  {
    id: 3234,
    attributes: {
      code: 'GENERAL',
      value: '常规'
    }
  },
  {
    id: 3235,
    attributes: {
      code: 'VERSION_TAG',
      value: '版本标签'
    }
  },
  {
    id: 3236,
    attributes: {
      code: 'Name',
      value: '名称'
    }
  },
  {
    id: 3237,
    attributes: {
      code: 'REMOVE',
      value: '移除'
    }
  },
  {
    id: 3238,
    attributes: {
      code: 'EXTENSIONS',
      value: '扩展'
    }
  },
  {
    id: 2769,
    attributes: {
      code: 'expected-quantity',
      value: '目标质量：'
    }
  },
  {
    id: 2770,
    attributes: {
      code: 'only-show-confirmed-routes',
      value: '仅显示有确认路线的分子'
    }
  },
  {
    id: 2774,
    attributes: {
      code: 'synthesised',
      value: '待合成'
    }
  },
  {
    id: 3239,
    attributes: {
      code: 'component.tagSelect.expand',
      value: '展开'
    }
  },
  {
    id: 3240,
    attributes: {
      code: 'component.tagSelect.collapse',
      value: '收起'
    }
  },
  {
    id: 3242,
    attributes: {
      code: 'component.notification.typeValue.retro',
      value: '路线生成任务'
    }
  },
  {
    id: 3243,
    attributes: {
      code: 'component.notification.statusValue.limited',
      value: '准备资源'
    }
  },
  {
    id: 2922,
    attributes: {
      code: 'generated-reaction',
      value: '生成反应'
    }
  },
  {
    id: 2923,
    attributes: {
      code: 'my-reaction-experimental',
      value: '我的反应-实验设计'
    }
  },
  {
    id: 3244,
    attributes: {
      code: 'component.notification.statusValue.running',
      value: '进行中'
    }
  },
  {
    id: 3245,
    attributes: {
      code: 'component.notification.statusValue.pending',
      value: '排队中'
    }
  },
  {
    id: 3442,
    attributes: {
      code: 'lab-management',
      value: '实验室管理'
    }
  },
  {
    id: 3443,
    attributes: {
      code: 'lab-site',
      value: '地点'
    }
  },
  {
    id: 2725,
    attributes: {
      code: 'continue-edit',
      value: '继续编辑原路线'
    }
  },
  {
    id: 3594,
    attributes: {
      code: 'usage-board',
      value: '通风橱顶'
    }
  },
  {
    id: 3593,
    attributes: {
      code: 'usage-down',
      value: '后方'
    }
  },
  {
    id: 3592,
    attributes: {
      code: 'usage-up',
      value: '前方'
    }
  },
  {
    id: 2726,
    attributes: {
      code: 'replaced-route-success',
      value: '已替换为选中路线'
    }
  },
  {
    id: 2729,
    attributes: {
      code: 'choose-route',
      value: '确认选中路线'
    }
  },
  {
    id: 2730,
    attributes: {
      code: 'fail-convert',
      value: '转换失败'
    }
  },
  {
    id: 2731,
    attributes: {
      code: 'upload-img',
      value: '请上传png或jpg格式的图片文件'
    }
  },
  {
    id: 2732,
    attributes: {
      code: 'select-an-img',
      value: '请选择一个图片文件'
    }
  },
  {
    id: 2760,
    attributes: {
      code: 'required-weight',
      value: '需求质量'
    }
  },
  {
    id: 2762,
    attributes: {
      code: 'FTE-per-day',
      value: 'FTE价格'
    }
  },
  {
    id: 2787,
    attributes: {
      code: 'favorite-routes',
      value: '仅显示收藏路线'
    }
  },
  {
    id: 3357,
    attributes: {
      code: 'project-summary',
      value: '项目概览'
    }
  },
  {
    id: 3447,
    attributes: {
      code: 'lab-monitor',
      value: '实验室监控'
    }
  },
  {
    id: 3448,
    attributes: {
      code: 'robot-name',
      value: '机器人名称'
    }
  },
  {
    id: 3405,
    attributes: {
      code: 'from-this-search',
      value: '来自本次搜索'
    }
  },
  {
    id: 3407,
    attributes: {
      code: 'from-other-search',
      value: '来自其他搜索'
    }
  },
  {
    id: 2992,
    attributes: {
      code: 'route-id',
      value: '路线编号'
    }
  },
  {
    id: 2840,
    attributes: {
      code: 'calculate-materials',
      value: '计算其他物料质量'
    }
  },
  {
    id: 2841,
    attributes: {
      code: 'start-time-limit',
      value: '实验开始时间限制'
    }
  },
  {
    id: 2989,
    attributes: {
      code: 'advanced-settings',
      value: '高级设置'
    }
  },
  {
    id: 3201,
    attributes: {
      code: 'Dilute',
      value: '稀释'
    }
  },
  {
    id: 3015,
    attributes: {
      code: 'collapse-route',
      value: '收起路线'
    }
  },
  {
    id: 3018,
    attributes: {
      code: 'reaction-search',
      value: '反应检索'
    }
  },
  {
    id: 3019,
    attributes: {
      code: 'recommended-reactions',
      value: '推荐反应'
    }
  },
  {
    id: 3020,
    attributes: {
      code: 'view-materials',
      value: '查看物料'
    }
  },
  {
    id: 3021,
    attributes: {
      code: 'reaction-lib',
      value: '反应库'
    }
  },
  {
    id: 3293,
    attributes: {
      code: 'menu.list.procedure',
      value: '实验流程列表'
    }
  },
  {
    id: 3208,
    attributes: {
      code: 'Extract',
      value: '萃取'
    }
  },
  {
    id: 3209,
    attributes: {
      code: 'Filtration',
      value: '过滤'
    }
  },
  {
    id: 3280,
    attributes: {
      code: 'menu.list.route',
      value: '路线'
    }
  },
  {
    id: 3279,
    attributes: {
      code: 'menu.list.project-list.detail.compound.edit',
      value: '编辑路线'
    }
  },
  {
    id: 3247,
    attributes: {
      code: 'component.notification.statusValue.failed',
      value: '失败'
    }
  },
  {
    id: 3249,
    attributes: {
      code: 'menu.dashboard',
      value: 'Dashboard'
    }
  },
  {
    id: 3639,
    attributes: {
      code: 'switching',
      value: '转化中'
    }
  },
  {
    id: 3641,
    attributes: {
      code: 'formulated-procedure',
      value: '格式化文本'
    }
  },
  {
    id: 3250,
    attributes: {
      code: 'menu.exception.403',
      value: '403'
    }
  },
  {
    id: 3251,
    attributes: {
      code: 'menu.exception.404',
      value: '404'
    }
  },
  {
    id: 3252,
    attributes: {
      code: 'menu.exception.500',
      value: '500'
    }
  },
  {
    id: 3253,
    attributes: {
      code: 'menu.list.playground',
      value: '内部实验区'
    }
  },
  {
    id: 3254,
    attributes: {
      code: 'menu.list.playground.commend',
      value: '评价查看'
    }
  },
  {
    id: 3259,
    attributes: {
      code: 'menu.list.workspace.myCompound',
      value: '我的分子'
    }
  },
  {
    id: 3260,
    attributes: {
      code: 'menu.list.workspace.myReaction',
      value: '我的反应'
    }
  },
  {
    id: 3261,
    attributes: {
      code: 'menu.list.workspace.myExperiment',
      value: '我的实验'
    }
  },
  {
    id: 3262,
    attributes: {
      code: 'menu.list.project-list',
      value: '项目'
    }
  },
  {
    id: 3263,
    attributes: {
      code: 'menu.list.project-list.detail',
      value: '项目详情'
    }
  },
  {
    id: 3264,
    attributes: {
      code: 'menu.list.project-list.detail.reaction',
      value: '反应详情'
    }
  },
  {
    id: 3265,
    attributes: {
      code: 'menu.list.project-list.detail.reaction.execute.detail',
      value: '实验详情'
    }
  },
  {
    id: 2908,
    attributes: {
      code: 'price',
      value: '价格'
    }
  },
  {
    id: 2909,
    attributes: {
      code: 'min-price',
      value: '价格（最小值）'
    }
  },
  {
    id: 3266,
    attributes: {
      code: 'menu.list.project-list.detail.experimentalProcedure',
      value: '实验设计详情'
    }
  },
  {
    id: 3270,
    attributes: {
      code: 'menu.list.project-list.detail.addMolecule',
      value: '添加分子'
    }
  },
  {
    id: 3271,
    attributes: {
      code: 'add-molecule-in-route-tip',
      value: '添加分子'
    }
  },
  {
    id: 3363,
    attributes: {
      code: 'experiment-prepared',
      value: '已准备'
    }
  },
  {
    id: 3365,
    attributes: {
      code: 'experiment-to-be-ready',
      value: '未准备'
    }
  },
  {
    id: 3367,
    attributes: {
      code: 'experiment-exception',
      value: '异常'
    }
  },
  {
    id: 3369,
    attributes: {
      code: 'experiment-pending',
      value: '暂停中'
    }
  },
  {
    id: 3444,
    attributes: {
      code: 'lab-name',
      value: '实验室名称'
    }
  },
  {
    id: 3022,
    attributes: {
      code: 'prev-step',
      value: '上一步'
    }
  },
  {
    id: 3035,
    attributes: {
      code: 'reference',
      value: '参考反应'
    }
  },
  {
    id: 2993,
    attributes: {
      code: 'algorithmic-score',
      value: '算法智能评分:'
    }
  },
  {
    id: 3457,
    attributes: {
      code: 'call-robot',
      value: '召唤机器人'
    }
  },
  {
    id: 3039,
    attributes: {
      code: 'pages.Notification.clear-tasks',
      value: '清空已结束任务'
    }
  },
  {
    id: 2987,
    attributes: {
      code: 'longest-chain-l',
      value: '最长链长度:'
    }
  },
  {
    id: 2994,
    attributes: {
      code: 'known-reaction-proportion',
      value: '已知反应占比:'
    }
  },
  {
    id: 3643,
    attributes: {
      code: 'traditional-mode',
      value: '传统模式'
    }
  },
  {
    id: 3036,
    attributes: {
      code: 'materials-not-available',
      value: '存在物料无法买到或价格过高'
    }
  },
  {
    id: 3037,
    attributes: {
      code: 'routes-citing',
      value: '引用该反应的路线'
    }
  },
  {
    id: 3038,
    attributes: {
      code: 'pages.Notification.task',
      value: '任务'
    }
  },
  {
    id: 2779,
    attributes: {
      code: 'labor-cost',
      value: '人工费'
    }
  },
  {
    id: 3361,
    attributes: {
      code: 'experiment-ongoing',
      value: '进行中'
    }
  },
  {
    id: 3272,
    attributes: {
      code: 'menu.list.project-list.detail.addMolecule.tip',
      value: '请先添加分子'
    }
  },
  {
    id: 3273,
    attributes: {
      code: 'menu.list.project-list.detail.quoteDetail',
      value: '报价详情'
    }
  },
  {
    id: 3274,
    attributes: {
      code: 'menu.list.project-list.detail.addQuote',
      value: '添加报价'
    }
  },
  {
    id: 3275,
    attributes: {
      code: 'menu.list.project-list.detail.compound',
      value: '分子详情'
    }
  },
  {
    id: 3276,
    attributes: {
      code: 'menu.list.project-list.detail.compound.detail',
      value: '分子详情'
    }
  },
  {
    id: 3277,
    attributes: {
      code: 'menu.list.project-list.detail.compound.detail.create',
      value: '创建路线'
    }
  },
  {
    id: 3278,
    attributes: {
      code: 'menu.list.project-list.detail.compound.viewByBackbone',
      value: '查看路线'
    }
  },
  {
    id: 3281,
    attributes: {
      code: 'menu.list.route.view',
      value: '查看路线'
    }
  },
  {
    id: 3282,
    attributes: {
      code: 'menu.list.route.viewByBackbone',
      value: '查看路线'
    }
  },
  {
    id: 3285,
    attributes: {
      code: 'menu.list.reaction.detail',
      value: '查看反应'
    }
  },
  {
    id: 3419,
    attributes: {
      code: 'complete-molecule',
      value: '完成分子'
    }
  },
  {
    id: 3286,
    attributes: {
      code: 'menu.list.experiment.plan',
      value: '实验计划列表'
    }
  },
  {
    id: 3287,
    attributes: {
      code: 'menu.list.experiment',
      value: '实验执行'
    }
  },
  {
    id: 3288,
    attributes: {
      code: 'menu.list.material-manage',
      value: '原料'
    }
  },
  {
    id: 3289,
    attributes: {
      code: 'menu.list.material-manage.storage',
      value: '原料库管理'
    }
  },
  {
    id: 3290,
    attributes: {
      code: 'menu.list.material-manage.search-molecule',
      value: '结构式搜索'
    }
  },
  {
    id: 3291,
    attributes: {
      code: 'menu.list.material-manage.black-list',
      value: '原料黑名单管理'
    }
  },
  {
    id: 3326,
    attributes: {
      code: 'app.pwa.serviceworker.updated.hint',
      value: '请点击“刷新”按钮或者手动刷新页面'
    }
  },
  {
    id: 3328,
    attributes: {
      code: 'app.general.message.success',
      value: '成功'
    }
  },
  {
    id: 2677,
    attributes: {
      code: 'retro_params-settings',
      value: '逆合成配置'
    }
  },
  {
    id: 3645,
    attributes: {
      code: 'auto-mode',
      value: '自动模式'
    }
  },
  {
    id: 3379,
    attributes: {
      code: 'route-price-score',
      value: '价格得分'
    }
  },
  {
    id: 3397,
    attributes: {
      code: 'cancel-projects',
      value: '取消项目'
    }
  },
  {
    id: 3421,
    attributes: {
      code: 'complete-molecule-tip',
      value: '确认完成分子？'
    }
  },
  {
    id: 3648,
    attributes: {
      code: 'generate',
      value: '生成'
    }
  },
  {
    id: 3458,
    attributes: {
      code: 'stop',
      value: '暂停机器人'
    }
  },
  {
    id: 3398,
    attributes: {
      code: 'cancel-projects-note',
      value: '项目取消后不可撤回'
    }
  },
  {
    id: 2743,
    attributes: {
      code: 'man-days',
      value: '所需人天'
    }
  },
  {
    id: 2744,
    attributes: {
      code: 'add-new-charge',
      value: '新增收费项'
    }
  },
  {
    id: 2746,
    attributes: {
      code: 'show-all',
      value: '显示全部分子'
    }
  },
  {
    id: 2747,
    attributes: {
      code: 'estimate-work-day',
      value: '预计工时/天'
    }
  },
  {
    id: 3411,
    attributes: {
      code: 'start-project',
      value: '启动项目'
    }
  },
  {
    id: 2748,
    attributes: {
      code: 'estimate-delivery',
      value: '预计交付时间（人天）'
    }
  },
  {
    id: 3650,
    attributes: {
      code: 'substance-name',
      value: '物料名称'
    }
  },
  {
    id: 2670,
    attributes: {
      code: 'default-yield',
      value: '默认收率'
    }
  },
  {
    id: 2671,
    attributes: {
      code: 'Estimate-reaction',
      value: '根据反应难易程度进行估算'
    }
  },
  {
    id: 3413,
    attributes: {
      code: 'start-project-tip',
      value: '确认启动项目？'
    }
  },
  {
    id: 3415,
    attributes: {
      code: 'cancel-molecule',
      value: '取消分子'
    }
  },
  {
    id: 3417,
    attributes: {
      code: 'cancel-molecule-tip',
      value: '取消分子后不可撤回。'
    }
  },
  {
    id: 2776,
    attributes: {
      code: 'in-use',
      value: '启用中'
    }
  },
  {
    id: 3351,
    attributes: {
      code: 'similarity',
      value: '相似度'
    }
  },
  {
    id: 2791,
    attributes: {
      code: 'modified-time',
      value: '编辑时间'
    }
  },
  {
    id: 3023,
    attributes: {
      code: 'next-step',
      value: '下一步'
    }
  },
  {
    id: 3024,
    attributes: {
      code: 'from',
      value: '来自'
    }
  },
  {
    id: 3461,
    attributes: {
      code: 'video-list',
      value: '视频列表'
    }
  },
  {
    id: 3462,
    attributes: {
      code: 'report',
      value: '报告'
    }
  },
  {
    id: 3463,
    attributes: {
      code: 'substance-inference',
      value: '物质推断'
    }
  },
  {
    id: 3464,
    attributes: {
      code: 'structural',
      value: '结构式'
    }
  },
  {
    id: 3465,
    attributes: {
      code: 'exact-mass',
      value: '精确分子量'
    }
  },
  {
    id: 3466,
    attributes: {
      code: 'ultra-spectra',
      value: '有无紫外信号'
    }
  },
  {
    id: 3467,
    attributes: {
      code: 'mass-spectra',
      value: '有无MS谱图'
    }
  },
  {
    id: 3468,
    attributes: {
      code: 'role',
      value: '角色'
    }
  },
  {
    id: 3469,
    attributes: {
      code: 'proportion-254',
      value: '比例（254nm）'
    }
  },
  {
    id: 3470,
    attributes: {
      code: 'proportion-214',
      value: '比例（214nm）'
    }
  },
  {
    id: 3471,
    attributes: {
      code: 'ai-inference',
      value: 'AI结论判定'
    }
  },
  {
    id: 3472,
    attributes: {
      code: 'product-identified-materials-remaining',
      value: '已出现产物，有原料剩余'
    }
  },
  {
    id: 3473,
    attributes: {
      code: 'product-identified-no-materials-remaining',
      value: '已出现产物，无原料剩余'
    }
  },
  {
    id: 2918,
    attributes: {
      code: 'open-route-reaction',
      value: '打开路线与反应'
    }
  },
  {
    id: 3025,
    attributes: {
      code: 'this',
      value: '本次'
    }
  },
  {
    id: 3026,
    attributes: {
      code: 'other',
      value: '其他'
    }
  },
  {
    id: 3595,
    attributes: {
      code: 'intermediate-detection',
      value: '中控'
    }
  },
  {
    id: 3596,
    attributes: {
      code: 'product-detection',
      value: '产物检测'
    }
  },
  {
    id: 2745,
    attributes: {
      code: 'same-quatation',
      value: '重复报价'
    }
  },
  {
    id: 3071,
    attributes: {
      code: 'pages.projectTable.label.compoundNumber',
      value: '分子数量'
    }
  },
  {
    id: 3597,
    attributes: {
      code: 'test-type',
      value: '检测类型'
    }
  },
  {
    id: 2763,
    attributes: {
      code: 'FTE-per-day-unit',
      value: '元/天'
    }
  },
  {
    id: 2764,
    attributes: {
      code: 'coefficient',
      value: '系数'
    }
  },
  {
    id: 2765,
    attributes: {
      code: 'quote-parmas',
      value: '报价参数'
    }
  },
  {
    id: 2766,
    attributes: {
      code: 'quote-sum',
      value: '总计报价：'
    }
  },
  {
    id: 2785,
    attributes: {
      code: 'Filter',
      value: '过滤条件'
    }
  },
  {
    id: 2752,
    attributes: {
      code: 'material-list',
      value: '物料明细表'
    }
  },
  {
    id: 2753,
    attributes: {
      code: 'work-time-detils',
      value: '工时明细表'
    }
  },
  {
    id: 2868,
    attributes: {
      code: 'material-ID',
      value: '物料编号'
    }
  },
  {
    id: 2869,
    attributes: {
      code: 'last-comment-date',
      value: '最后评论时间'
    }
  },
  {
    id: 2870,
    attributes: {
      code: 'reaction-step-ID',
      value: '反应步骤编号'
    }
  },
  {
    id: 3241,
    attributes: {
      code: 'component.tagSelect.all',
      value: '全部'
    }
  },
  {
    id: 3027,
    attributes: {
      code: 'searches',
      value: '搜索'
    }
  },
  {
    id: 2982,
    attributes: {
      code: 'log',
      value: '搜索日志'
    }
  },
  {
    id: 3028,
    attributes: {
      code: 'reported',
      value: '报道反应'
    }
  },
  {
    id: 2683,
    attributes: {
      code: 'max-search-time',
      value: '最大搜索时长'
    }
  },
  {
    id: 2684,
    attributes: {
      code: 'search-setting',
      value: '搜索设置'
    }
  },
  {
    id: 2685,
    attributes: {
      code: 'update-confirm-tip',
      value: '确认更新？'
    }
  },
  {
    id: 2687,
    attributes: {
      code: 'max-30-l',
      value: '最长为30个字符'
    }
  },
  {
    id: 3573,
    attributes: {
      code: 'unread',
      value: '未读'
    }
  },
  {
    id: 2688,
    attributes: {
      code: 'experimental-zone-empty-tip',
      value: '请点击右上角的快速逆合成按钮'
    }
  },
  {
    id: 2690,
    attributes: {
      code: 'experiment-history',
      value: '实验记录库'
    }
  },
  {
    id: 2691,
    attributes: {
      code: 'conclusion',
      value: '实验结果'
    }
  },
  {
    id: 2692,
    attributes: {
      code: 'min-cost',
      value: '计算最低成本'
    }
  },
  {
    id: 2694,
    attributes: {
      code: 'add-molecule-tip',
      value: '请先添加分子'
    }
  },
  {
    id: 2695,
    attributes: {
      code: 'creator',
      value: '创建人'
    }
  },
  {
    id: 2696,
    attributes: {
      code: 'edit-molecules',
      value: '编辑物料'
    }
  },
  {
    id: 2697,
    attributes: {
      code: 'all-my-reactions',
      value: '全部'
    }
  },
  {
    id: 2698,
    attributes: {
      code: 'new-my-reaction',
      value: '新建我的反应'
    }
  },
  {
    id: 2754,
    attributes: {
      code: 'cost-empty-tip',
      value: '如果成本为空，表示没有匹配到合适的物料，请人工选择或询价后编辑'
    }
  },
  {
    id: 2755,
    attributes: {
      code: 'quotation-sheet',
      value: '报价单'
    }
  },
  {
    id: 2756,
    attributes: {
      code: 'quotation',
      value: '报价'
    }
  },
  {
    id: 2699,
    attributes: {
      code: 'confirm-to-del',
      value: '确认删除'
    }
  },
  {
    id: 2689,
    attributes: {
      code: 'enter-select-tip',
      value: '请选择'
    }
  },
  {
    id: 3152,
    attributes: {
      code: 'pages.experiment.check.statusLabel.todo',
      value: '待送样'
    }
  },
  {
    id: 3153,
    attributes: {
      code: 'pages.experiment',
      value: '实验'
    }
  },
  {
    id: 3030,
    attributes: {
      code: 'material-sheet',
      value: '物料表'
    }
  },
  {
    id: 2906,
    attributes: {
      code: 'material-not-available',
      value: '否'
    }
  },
  {
    id: 3211,
    attributes: {
      code: 'ParallelReaction',
      value: '平行反应'
    }
  },
  {
    id: 3477,
    attributes: {
      code: 'action-proposal-tip',
      value: '请确认以下方案，若超时未确认，则会自动触发第一个方案'
    }
  },
  {
    id: 2871,
    attributes: {
      code: 'task-type',
      value: '任务类型'
    }
  },
  {
    id: 2778,
    attributes: {
      code: 'draft',
      value: '草稿'
    }
  },
  {
    id: 2959,
    attributes: {
      code: 'edit',
      value: '编辑'
    }
  },
  {
    id: 2960,
    attributes: {
      code: 'add-member',
      value: '添加人员'
    }
  },
  {
    id: 3246,
    attributes: {
      code: 'component.notification.statusValue.success',
      value: '已完成'
    }
  },
  {
    id: 3381,
    attributes: {
      code: 'route-safety-score',
      value: '安全性得分'
    }
  },
  {
    id: 3383,
    attributes: {
      code: 'route-sort-dimension-weight-setting',
      value: '路线排序偏好设置'
    }
  },
  {
    id: 3385,
    attributes: {
      code: 'demension-weight',
      value: '排序维度权重'
    }
  },
  {
    id: 3228,
    attributes: {
      code: 'ACTIVATE_HAND',
      value: '激活抓手工具'
    }
  },
  {
    id: 3229,
    attributes: {
      code: 'ACTIVATE_LASSO',
      value: '激活套索工具'
    }
  },
  {
    id: 3230,
    attributes: {
      code: 'ACRST',
      value: '激活创建/删除空间工具'
    }
  },
  {
    id: 3248,
    attributes: {
      code: 'menu.login',
      value: '登录'
    }
  },
  {
    id: 2782,
    attributes: {
      code: 'total',
      value: '总计'
    }
  },
  {
    id: 2771,
    attributes: {
      code: 'download-selected-quatation',
      value: '下载选中报价单'
    }
  },
  {
    id: 2875,
    attributes: {
      code: 'project-type',
      value: '项目类型'
    }
  },
  {
    id: 2950,
    attributes: {
      code: 'Priority',
      value: '优先级'
    }
  },
  {
    id: 2936,
    attributes: {
      code: 'molecules',
      value: '分子'
    }
  },
  {
    id: 2888,
    attributes: {
      code: 'purity',
      value: '纯度'
    }
  },
  {
    id: 2889,
    attributes: {
      code: 'ID',
      value: '编号'
    }
  },
  {
    id: 2935,
    attributes: {
      code: 'is-required',
      value: '是必填项！'
    }
  },
  {
    id: 2820,
    attributes: {
      code: 'search-done',
      value: '路线生成完成'
    }
  },
  {
    id: 3215,
    attributes: {
      code: 'Mix',
      value: '投料'
    }
  },
  {
    id: 3232,
    attributes: {
      code: 'TOOLS',
      value: '工具栏'
    }
  },
  {
    id: 2842,
    attributes: {
      code: 'experiment-actual-start-time',
      value: '实验实际开始时间'
    }
  },
  {
    id: 3233,
    attributes: {
      code: 'CUDIA',
      value: '使用数据输入关联连接'
    }
  },
  {
    id: 2917,
    attributes: {
      code: 'open-reaction-details',
      value: '打开反应详情'
    }
  },
  {
    id: 3070,
    attributes: {
      code: 'pages.projectTable.label.deliveryDate',
      value: '交期'
    }
  },
  {
    id: 3299,
    attributes: {
      code: 'menu.list.card-list',
      value: '卡片列表'
    }
  },
  {
    id: 2700,
    attributes: {
      code: 'molecule-exist',
      value: '分子已存在'
    }
  },
  {
    id: 2707,
    attributes: {
      code: 'reaction',
      value: '反应式'
    }
  },
  {
    id: 2735,
    attributes: {
      code: 'molecules-resynthesized',
      value: '分子正在重新合成中...'
    }
  },
  {
    id: 2736,
    attributes: {
      code: 'save-build-block',
      value: '保存为Building Block'
    }
  },
  {
    id: 2738,
    attributes: {
      code: 'success-save-draft',
      value: '已存为草稿'
    }
  },
  {
    id: 2701,
    attributes: {
      code: 'confirm-delete-molecule-route',
      value: '确认删除这个中间体及其合成路径吗？'
    }
  },
  {
    id: 2702,
    attributes: {
      code: 'confirm-delete-synthesis-route',
      value: '确认删除该分子及其合成路线吗？'
    }
  },
  {
    id: 2703,
    attributes: {
      code: 'system-update',
      value: '系统已更新，页面将自动刷新'
    }
  },
  {
    id: 2704,
    attributes: {
      code: 'success-update',
      value: '更新成功'
    }
  },
  {
    id: 2705,
    attributes: {
      code: 'no-permission-tip',
      value: '无操作权限，请联系管理员开通权限！'
    }
  },
  {
    id: 2706,
    attributes: {
      code: 'delete-quotation',
      value: '是否删除该报价单?'
    }
  },
  {
    id: 2737,
    attributes: {
      code: 'copy-molecule-route',
      value: '复制当前分子及其合成路线'
    }
  },
  {
    id: 2739,
    attributes: {
      code: 'success-confirm-quotate',
      value: '确认报价操作成功'
    }
  },
  {
    id: 2740,
    attributes: {
      code: 'view-results',
      value: '查看结果'
    }
  },
  {
    id: 2741,
    attributes: {
      code: 'expand-route',
      value: '展开路线'
    }
  },
  {
    id: 2742,
    attributes: {
      code: 'confirm-quote',
      value: '确认报价'
    }
  },
  {
    id: 2757,
    attributes: {
      code: 'cost',
      value: '成本'
    }
  },
  {
    id: 2666,
    attributes: {
      code: 'apply',
      value: '应用'
    }
  },
  {
    id: 2675,
    attributes: {
      code: 'FTE-unit',
      value: 'FTE价格'
    }
  },
  {
    id: 2668,
    attributes: {
      code: 'route-detail-display-style',
      value: '路线详情展示策略'
    }
  },
  {
    id: 2669,
    attributes: {
      code: 'route-list-display',
      value: '路线列表展示策略'
    }
  },
  {
    id: 2667,
    attributes: {
      code: 'min',
      value: '分钟'
    }
  },
  {
    id: 2783,
    attributes: {
      code: 'commend',
      value: '评价'
    }
  },
  {
    id: 3377,
    attributes: {
      code: 'route-novelty-score',
      value: '新颖性得分'
    }
  },
  {
    id: 3474,
    attributes: {
      code: 'product-not-identified-materials-remaining',
      value: '未出现产物，有原料剩余'
    }
  },
  {
    id: 3108,
    attributes: {
      code: 'pages.reaction.statusLabel.editing',
      value: '编辑中'
    }
  },
  {
    id: 3032,
    attributes: {
      code: 'amount',
      value: '规格'
    }
  },
  {
    id: 3033,
    attributes: {
      code: 'supplier',
      value: '供应商'
    }
  },
  {
    id: 3034,
    attributes: {
      code: 'version',
      value: '版本'
    }
  },
  {
    id: 3476,
    attributes: {
      code: 'action-proposal',
      value: '下一步措施'
    }
  },
  {
    id: 2712,
    attributes: {
      code: 'next',
      value: '下一条'
    }
  },
  {
    id: 3074,
    attributes: {
      code: 'pages.projectTable.statusLabel.holding',
      value: '暂停中'
    }
  },
  {
    id: 3076,
    attributes: {
      code: 'pages.projectTable.statusLabel.finished',
      value: '已完成'
    }
  },
  {
    id: 3077,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.created',
      value: '创建'
    }
  },
  {
    id: 3078,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.started',
      value: '开始'
    }
  },
  {
    id: 3080,
    attributes: {
      code: 'supplier.edit.finished',
      value: '完成'
    }
  },
  {
    id: 2904,
    attributes: {
      code: 'label',
      value: '标签'
    }
  },
  {
    id: 2905,
    attributes: {
      code: 'material-available',
      value: '是'
    }
  },
  {
    id: 2999,
    attributes: {
      code: 'multi-steps-tip',
      value: '若路线有多种支链方案，则是包含最短支链步数的路线总步数'
    }
  },
  {
    id: 3065,
    attributes: {
      code: 'pages.projectTable.label.customer',
      value: '客户'
    }
  },
  {
    id: 3306,
    attributes: {
      code: 'menu.list.experimental-zone.search',
      value: '快速逆合成'
    }
  },
  {
    id: 3307,
    attributes: {
      code: 'menu.profile',
      value: '详情页'
    }
  },
  {
    id: 3150,
    attributes: {
      code: 'pages.experiment.statusLabel.success',
      value: '成功'
    }
  },
  {
    id: 3151,
    attributes: {
      code: 'pages.experiment.statusLabel.completed',
      value: 'Completed'
    }
  },
  {
    id: 3178,
    attributes: {
      code: 'pages.searchTable.nameStatus.abnormal',
      value: '异常'
    }
  },
  {
    id: 2821,
    attributes: {
      code: 'search-failed',
      value: '搜索失败了，请试试换个模式、增大最大步数搜索'
    }
  },
  {
    id: 2822,
    attributes: {
      code: 'wait-tip',
      value: '正在排队中，请耐心等待'
    }
  },
  {
    id: 2824,
    attributes: {
      code: 'un-select',
      value: '全不选'
    }
  },
  {
    id: 3283,
    attributes: {
      code: 'menu.list.route.edit',
      value: '编辑路线'
    }
  },
  {
    id: 2846,
    attributes: {
      code: 'open-reaction-details-tip',
      value: '若要添加反应，请打开反应详情'
    }
  },
  {
    id: 2848,
    attributes: {
      code: 'add-blacklist-material',
      value: '添加黑名单原料'
    }
  },
  {
    id: 2849,
    attributes: {
      code: 'failed-add-material-blacklist',
      value: '添加原料黑名单失败'
    }
  },
  {
    id: 2850,
    attributes: {
      code: 'add-raw-material-lib',
      value: '添加原料库'
    }
  },
  {
    id: 2851,
    attributes: {
      code: 'exists-tip',
      value: '请勿添加重复的'
    }
  },
  {
    id: 2852,
    attributes: {
      code: 'at-least-add-one',
      value: '请至少添加一个'
    }
  },
  {
    id: 2853,
    attributes: {
      code: 'add-routes',
      value: '添加路线'
    }
  },
  {
    id: 2854,
    attributes: {
      code: 'add-to-route',
      value: '添加到路线中'
    }
  },
  {
    id: 2856,
    attributes: {
      code: 'add-raw-materials',
      value: '添加原料'
    }
  },
  {
    id: 2799,
    attributes: {
      code: 'confirm-to-replace',
      value: '确认替换'
    }
  },
  {
    id: 2800,
    attributes: {
      code: 'new-molecule-input',
      value: '您输入的新分子将覆盖画板上的结构'
    }
  },
  {
    id: 2857,
    attributes: {
      code: 'molecular-mass',
      value: '分子量'
    }
  },
  {
    id: 2858,
    attributes: {
      code: 'EWR',
      value: '当量/质量比'
    }
  },
  {
    id: 2859,
    attributes: {
      code: 'project-update-success',
      value: '更新项目状态成功'
    }
  },
  {
    id: 2860,
    attributes: {
      code: 'comment-object',
      value: '评论对象'
    }
  },
  {
    id: 2861,
    attributes: {
      code: 'created-succeed',
      value: '创建成功'
    }
  },
  {
    id: 2862,
    attributes: {
      code: 'project',
      value: '项目'
    }
  },
  {
    id: 2863,
    attributes: {
      code: 'project-name',
      value: '项目名称'
    }
  },
  {
    id: 2864,
    attributes: {
      code: 'project-create-failed',
      value: '项目创建失败'
    }
  },
  {
    id: 2865,
    attributes: {
      code: 'project-ID-duplicated',
      value: '项目编号重复！'
    }
  },
  {
    id: 2866,
    attributes: {
      code: 'reaction-ID',
      value: '反应式编号'
    }
  },
  {
    id: 2867,
    attributes: {
      code: 'experiment-ID',
      value: '试验编号'
    }
  },
  {
    id: 2907,
    attributes: {
      code: 'availability',
      value: '是否现货'
    }
  },
  {
    id: 2910,
    attributes: {
      code: 'max-price',
      value: '价格（最大值）'
    }
  },
  {
    id: 2911,
    attributes: {
      code: 'min-amount',
      value: '量（最小值）'
    }
  },
  {
    id: 2912,
    attributes: {
      code: 'max-amount',
      value: '量（最大值）'
    }
  },
  {
    id: 2913,
    attributes: {
      code: 'unit',
      value: '单位'
    }
  },
  {
    id: 3484,
    attributes: {
      code: 'send-from',
      value: '发送人'
    }
  },
  {
    id: 3485,
    attributes: {
      code: 'countdown',
      value: '待处理倒计时：'
    }
  },
  {
    id: 3134,
    attributes: {
      code: 'pages.experiment.label.owner',
      value: '负责人'
    }
  },
  {
    id: 2806,
    attributes: {
      code: 'unfavorite',
      value: '取消收藏'
    }
  },
  {
    id: 2807,
    attributes: {
      code: 'success-update-status',
      value: '更改路线状态成功'
    }
  },
  {
    id: 2808,
    attributes: {
      code: 'temporary-route-tip',
      value: '临时路线无法收藏，可以查看路线详情后保存'
    }
  },
  {
    id: 3224,
    attributes: {
      code: 'Triturate',
      value: '打浆'
    }
  },
  {
    id: 3225,
    attributes: {
      code: 'WeighProduct',
      value: '称重'
    }
  },
  {
    id: 3226,
    attributes: {
      code: 'MASTER_TOOL',
      value: '基础工具'
    }
  },
  {
    id: 3227,
    attributes: {
      code: 'TASK_ITEM',
      value: '任务框'
    }
  },
  {
    id: 2946,
    attributes: {
      code: 'aiGenerated',
      value: 'AI路线'
    }
  },
  {
    id: 2733,
    attributes: {
      code: 'regioselectivity',
      value: '选择性风险'
    }
  },
  {
    id: 2758,
    attributes: {
      code: 'cost-summary',
      value: '成本 Summary'
    }
  },
  {
    id: 2932,
    attributes: {
      code: 'reaction',
      value: '反应'
    }
  },
  {
    id: 2847,
    attributes: {
      code: 'successfully-added',
      value: '添加成功'
    }
  },
  {
    id: 3073,
    attributes: {
      code: 'pages.projectTable.statusLabel.started',
      value: '进行中'
    }
  },
  {
    id: 2759,
    attributes: {
      code: 'measurement-method',
      value: '计量方式'
    }
  },
  {
    id: 3079,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.finished',
      value: '完成'
    }
  },
  {
    id: 3081,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.holding',
      value: '暂停'
    }
  },
  {
    id: 3082,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.cancelled',
      value: '取消'
    }
  },
  {
    id: 3083,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.canceled',
      value: '取消'
    }
  },
  {
    id: 2711,
    attributes: {
      code: 'route-confirm',
      value: '是否确认当前路线？'
    }
  },
  {
    id: 2713,
    attributes: {
      code: 'previous',
      value: '上一条'
    }
  },
  {
    id: 2714,
    attributes: {
      code: 'chiral-separation',
      value: '手性分离'
    }
  },
  {
    id: 2715,
    attributes: {
      code: 'cant-input-negative-number',
      value: '不能为负数'
    }
  },
  {
    id: 2716,
    attributes: {
      code: 'solvent',
      value: '溶剂'
    }
  },
  {
    id: 2717,
    attributes: {
      code: 'product',
      value: '产物'
    }
  },
  {
    id: 2718,
    attributes: {
      code: 'other-reagent',
      value: '其他试剂'
    }
  },
  {
    id: 2719,
    attributes: {
      code: 'reactant',
      value: '反应物'
    }
  },
  {
    id: 2720,
    attributes: {
      code: 'main-reactant',
      value: '主反应物'
    }
  },
  {
    id: 2721,
    attributes: {
      code: 'quantity',
      value: '数量'
    }
  },
  {
    id: 2722,
    attributes: {
      code: 'unit-price',
      value: '单价'
    }
  },
  {
    id: 2734,
    attributes: {
      code: 'choose',
      value: '选择'
    }
  },
  {
    id: 2672,
    attributes: {
      code: 'Estimate-procedure',
      value: '根据procedure进行估算'
    }
  },
  {
    id: 2673,
    attributes: {
      code: 'Premium-coefficient',
      value: '溢价系数'
    }
  },
  {
    id: 2674,
    attributes: {
      code: 'WH-calculation-method',
      value: '工时计算逻辑'
    }
  },
  {
    id: 2676,
    attributes: {
      code: 'rmb-unit',
      value: '元/天'
    }
  },
  {
    id: 2678,
    attributes: {
      code: 'quotation-settings',
      value: '报价配置'
    }
  },
  {
    id: 2679,
    attributes: {
      code: 'show-yield',
      value: '路线上显示收率'
    }
  },
  {
    id: 2681,
    attributes: {
      code: 'default-all-route',
      value: '默认显示完整路线'
    }
  },
  {
    id: 2872,
    attributes: {
      code: 'experiment-design-status',
      value: '实验设计状态'
    }
  },
  {
    id: 3111,
    attributes: {
      code: 'pages.route.statusLabel.editing',
      value: '编辑中'
    }
  },
  {
    id: 3112,
    attributes: {
      code: 'pages.route.statusLabel.canceled',
      value: '已取消'
    }
  },
  {
    id: 3113,
    attributes: {
      code: 'pages.route.statusLabel.confirmed',
      value: '已确认'
    }
  },
  {
    id: 3478,
    attributes: {
      code: 'try-another-procedure',
      value: '换一个反应条件：'
    }
  },
  {
    id: 3480,
    attributes: {
      code: 'cancel-experiment',
      value: '终止实验'
    }
  },
  {
    id: 3481,
    attributes: {
      code: 'message',
      value: '系统消息'
    }
  },
  {
    id: 3482,
    attributes: {
      code: 'task-list',
      value: '发起任务列表'
    }
  },
  {
    id: 3114,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.created',
      value: '编辑中'
    }
  },
  {
    id: 3115,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.published',
      value: '已发布'
    }
  },
  {
    id: 2890,
    attributes: {
      code: 'modification-reason',
      value: '修改原因'
    }
  },
  {
    id: 2891,
    attributes: {
      code: 'chinese-name',
      value: '物料中文名'
    }
  },
  {
    id: 2892,
    attributes: {
      code: 'english-name',
      value: '物料英文名'
    }
  },
  {
    id: 2893,
    attributes: {
      code: 'modification-time',
      value: '修改时间'
    }
  },
  {
    id: 2894,
    attributes: {
      code: 'modifier',
      value: '修改人'
    }
  },
  {
    id: 2895,
    attributes: {
      code: 'show-materials',
      value: '展开物料'
    }
  },
  {
    id: 2896,
    attributes: {
      code: 'project-molecule-id',
      value: '项目分子ID'
    }
  },
  {
    id: 2897,
    attributes: {
      code: 'member',
      value: '员工'
    }
  },
  {
    id: 2898,
    attributes: {
      code: 'confirmed-route',
      value: '已确认路线'
    }
  },
  {
    id: 2899,
    attributes: {
      code: 'No',
      value: '无'
    }
  },
  {
    id: 2900,
    attributes: {
      code: 'has',
      value: '有'
    }
  },
  {
    id: 2901,
    attributes: {
      code: 'multiple',
      value: '多种'
    }
  },
  {
    id: 2902,
    attributes: {
      code: 'branched-chain',
      value: '支链'
    }
  },
  {
    id: 2903,
    attributes: {
      code: 'source',
      value: '来源'
    }
  },
  {
    id: 3300,
    attributes: {
      code: 'menu.list.search-list',
      value: '搜索列表'
    }
  },
  {
    id: 3301,
    attributes: {
      code: 'menu.list.search-list.articles',
      value: '搜索列表（文章）'
    }
  },
  {
    id: 3063,
    attributes: {
      code: 'pages.projectTable.label.name',
      value: '名称'
    }
  },
  {
    id: 3101,
    attributes: {
      code: 'pages.reaction.label.material-sheet',
      value: '投料表'
    }
  },
  {
    id: 2773,
    attributes: {
      code: 'nums-materials',
      value: '包含原料数量'
    }
  },
  {
    id: 3102,
    attributes: {
      code: 'pages.reaction.label.referenceReaction',
      value: '添加到我的反应'
    }
  },
  {
    id: 2789,
    attributes: {
      code: 'only-one-edit',
      value: '只能同时编辑一行'
    }
  },
  {
    id: 2790,
    attributes: {
      code: 'edit-molecule',
      value: '编辑分子'
    }
  },
  {
    id: 3302,
    attributes: {
      code: 'menu.list.search-list.projects',
      value: '搜索列表（项目）'
    }
  },
  {
    id: 3303,
    attributes: {
      code: 'menu.list.search-list.applications',
      value: '搜索列表（应用）'
    }
  },
  {
    id: 3304,
    attributes: {
      code: 'menu.list.dashboard',
      value: '项目跟踪'
    }
  },
  {
    id: 3305,
    attributes: {
      code: 'menu.list.experimental-zone',
      value: '快速逆合成'
    }
  },
  {
    id: 3308,
    attributes: {
      code: 'menu.profile.basic',
      value: '基础详情页'
    }
  },
  {
    id: 3310,
    attributes: {
      code: 'menu.result',
      value: '结果页'
    }
  },
  {
    id: 3051,
    attributes: {
      code: 'pages.login.phoneNumber.required',
      value: '手机号是必填项！'
    }
  },
  {
    id: 3052,
    attributes: {
      code: 'pages.login.phoneNumber.invalid',
      value: '不合法的手机号！'
    }
  },
  {
    id: 3053,
    attributes: {
      code: 'pages.login.captcha.placeholder',
      value: '请输入验证码！'
    }
  },
  {
    id: 3054,
    attributes: {
      code: 'pages.login.captcha.required',
      value: '验证码是必填项！'
    }
  },
  {
    id: 3055,
    attributes: {
      code: 'pages.login.phoneLogin.getVerificationCode',
      value: '获取验证码'
    }
  },
  {
    id: 3057,
    attributes: {
      code: 'pages.login.forgotPassword',
      value: '忘记密码 ?'
    }
  },
  {
    id: 3058,
    attributes: {
      code: 'pages.login.loginWith',
      value: '其他登录方式'
    }
  },
  {
    id: 3059,
    attributes: {
      code: 'pages.login.registerAccount',
      value: '注册账户'
    }
  },
  {
    id: 3060,
    attributes: {
      code: 'pages.welcome.link',
      value: '欢迎使用'
    }
  },
  {
    id: 3061,
    attributes: {
      code: 'pages.admin.subPage.title',
      value: '这个页面只有 admin 权限才能查看'
    }
  },
  {
    id: 3062,
    attributes: {
      code: 'pages.projectTable.title',
      value: '项目列表'
    }
  },
  {
    id: 3103,
    attributes: {
      code: 'pages.reaction.label.createReaction',
      value: '创建反应'
    }
  },
  {
    id: 3107,
    attributes: {
      code: 'pages.reaction.label.warn.materialDiff',
      value: '物料和该反应不一致，请重新输入'
    }
  },
  {
    id: 2680,
    attributes: {
      code: 'default-intermediates',
      value: '默认只显示中间体和关键起始物料'
    }
  },
  {
    id: 2682,
    attributes: {
      code: 'route-display-style',
      value: '路线展示策略'
    }
  },
  {
    id: 2708,
    attributes: {
      code: 'filter-routes-group',
      value: '点击筛选该组的全部路线'
    }
  },
  {
    id: 2709,
    attributes: {
      code: 'unsaved-changes-tip',
      value: '未保存的内容将会丢失'
    }
  },
  {
    id: 3410,
    attributes: {
      code: 'submit',
      value: '提交'
    }
  },
  {
    id: 3340,
    attributes: {
      code: 'default-material-sources-for-retrosynthesis',
      value: '搜索默认原料库'
    }
  },
  {
    id: 3312,
    attributes: {
      code: 'menu.result.fail',
      value: '失败页'
    }
  },
  {
    id: 3373,
    attributes: {
      code: 'spot',
      value: '现货'
    }
  },
  {
    id: 2855,
    attributes: {
      code: 'added-to-route',
      value: '已添加到路线中'
    }
  },
  {
    id: 3088,
    attributes: {
      code: 'pages.projectTable.actionLabel.config',
      value: '设置'
    }
  },
  {
    id: 3089,
    attributes: {
      code: 'pages.projectTable.actionLabel.new',
      value: '新建'
    }
  },
  {
    id: 3090,
    attributes: {
      code: 'pages.projectTable.modelLabel.newProject',
      value: '新建项目'
    }
  },
  {
    id: 3091,
    attributes: {
      code: 'pages.route.label.showComplete',
      value: '显示完整路线'
    }
  },
  {
    id: 3092,
    attributes: {
      code: 'pages.route.label.hideComplete',
      value: '显示路线主干'
    }
  },
  {
    id: 3093,
    attributes: {
      code: 'pages.route.label.addToMyReaction',
      value: '添加到我的反应'
    }
  },
  {
    id: 3094,
    attributes: {
      code: 'pages.route.label.confirmToDeleteMyReaction',
      value: '确认删除该反应？'
    }
  },
  {
    id: 3095,
    attributes: {
      code: 'pages.route.edit.label.route',
      value: '路线'
    }
  },
  {
    id: 3096,
    attributes: {
      code: 'pages.route.edit.label.save',
      value: '保存'
    }
  },
  {
    id: 3097,
    attributes: {
      code: 'pages.route.edit.label.confirm',
      value: '确认'
    }
  },
  {
    id: 3098,
    attributes: {
      code: 'pages.reaction.label.name',
      value: '路线名称'
    }
  },
  {
    id: 3099,
    attributes: {
      code: 'pages.reaction.label.status',
      value: '路线状态'
    }
  },
  {
    id: 3100,
    attributes: {
      code: 'pages.reaction.label.stepName',
      value: '步骤名称'
    }
  }
])
export { data }
