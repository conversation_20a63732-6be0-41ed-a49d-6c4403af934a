const Mock = require('mockjs')
const data = Mock.mock({
  experiment_no: 'WW-20230315-E01',
  project_no: 'b2aa5a7b-3e7c-484a-b647-e1e83d2b909e',
  experiment_design_no: 'test1',
  design_name: null,
  experiment_name: '胆固醇酯合体 - 第一步第一次实验',
  status: 'completed',
  rxn_no: 'b37aeb30-5ddf-44dc-97e0-1ddd402a002c',
  rxn: 'Health eat power start. Early dog less affect before. Fire performance miss foot laugh.',
  start_time: '2022-05-17T18:09:58',
  end_time: '2023-06-25T23:30:50',
  owner: '<PERSON>',
  experiment_type: 'normal',
  predict_end_date: null,
  progress: null,
  predict_yield: null,
  flow_data: null
})
export { data }
