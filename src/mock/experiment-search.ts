const Mock = require('mockjs')
const data = Mock.mock({
  data: [
    {
      experiment_no: '3fbe23b7-190c-421c-bb9b-9bcd837dfaed',
      project_no: '69b8070e-c134-4852-9ea9-7617f333ef1b',
      experiment_design_no: 'd368d7f7-1459-48e4-9727-0646fad4a18c',
      design_name: 'Painting TV today theory commercial.',
      experiment_name:
        'Step produce case despite speak. Name risk sit project. It very according view suggest pass.',
      status: 'completed',

      rxn_no: '7dd5e8eb-8fa5-4aa6-8755-93affe621317',

      rxn: 'War dream play edge should discover. Land worry tonight rise party.',
      start_time: '2022-06-11T20:41:07',
      end_time: '2023-12-12T07:43:27'
    }
  ]
})
export { data }
