const Mock = require('mockjs')
const data = Mock.mock({
  data: [
    {
      data: {
        method: 'reverse',
        add_method: {
          method_name: 'normal',
          solvents: [
            {
              solvent_name: '冰水',
              solvent_quantity: 30,
              solvent_unit: 'ml',
              concentration_unit: '%'
            }
          ],
          temperature: 0
        },
        duration: 1800,
        collect: 'solid',
        wash_time: 3,
        wash_solvent: {
          solvent_name: '水',
          solvent_quantity: -1,
          solvent_unit: 'ml',
          concentration_unit: '%'
        },
        filter_aid: '',
        task_name: 'Crystal'
      },
      task_schema: {
        title: 'Crystal',
        type: 'object',
        properties: {
          task_name: {
            title: '任务名',
            type: 'string'
          },
          method: {
            title: 'Method',
            type: 'string'
          },
          add_method: {
            $ref: '#/definitions/AddMethod'
          },
          temperature: {
            title: 'Temperature',
            type: 'number'
          },
          duration: {
            title: 'Duration',
            type: 'integer'
          },
          collect: {
            title: 'Collect',
            type: 'string'
          },
          wash_time: {
            title: 'Wash Time',
            type: 'integer'
          },
          wash_solvent: {
            $ref: '#/definitions/Solvent'
          },
          filter_aid: {
            title: 'Filter Aid',
            default: '',
            type: 'string'
          }
        },
        required: [
          'task_name',
          'method',
          'add_method',
          'duration',
          'collect',
          'wash_time',
          'wash_solvent'
        ],
        definitions: {
          Solvent: {
            title: 'Solvent',
            type: 'object',
            properties: {
              solvent_name: {
                title: 'Solvent Name',
                type: 'string'
              },
              solvent_quantity: {
                title: 'Solvent Quantity',
                default: -1,
                type: 'number'
              },
              solvent_unit: {
                title: 'Solvent Unit',
                default: 'ml',
                type: 'string'
              },
              concentration: {
                title: 'Concentration',
                type: 'number'
              },
              concentration_unit: {
                title: 'Concentration Unit',
                default: '%',
                type: 'string'
              }
            },
            required: ['solvent_name']
          },
          AddMethod: {
            title: 'AddMethod',
            type: 'object',
            properties: {
              method_name: {
                title: 'Method Name',
                type: 'string'
              },
              solvents: {
                title: 'Solvents',
                type: 'array',
                items: {
                  $ref: '#/definitions/Solvent'
                }
              },
              temperature: {
                title: '加料温度',
                type: 'number'
              },
              temperature_control: {
                title: '需要控制的温度',
                description: '加料时，需要把温度控制在这个值以下',
                type: 'number'
              },
              time_requirement: {
                title: '滴加的时间',
                description: '作为滴加速度的参考，如"半小时"内滴20ml',
                type: 'number'
              },
              volume_requirement: {
                title: '滴加的体积',
                description: '作为滴加速度的参考，如半小时内滴"20ml"',
                type: 'number'
              },
              flow_rate: {
                title: '流速',
                description: '用泵干的时候',
                type: 'number'
              },
              batch_count: {
                title: '固体加料时候分几批',
                type: 'integer'
              },
              notice: {
                title: 'Notice',
                type: 'string'
              }
            },
            required: ['method_name', 'solvents']
          }
        }
      },
      validation_error: '{}'
    }
  ]
})
export { data }
