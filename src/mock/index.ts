const quotationData = require('@/mock/quotation')?.data
const experimentExecuteData = require('@/mock/experiment-execute-list')?.data
const mockExperimentPlanListData = require('@/mock/experiment-plans')?.data
const erimentDetail = require('@/mock/experiment-detail')?.data
const experimentPlanDetail = require('@/mock/experiment-plan')?.data
const mockExperimentDesignData = require('@/mock/experiment-design')?.data
const mockOperationStatusData = require('@/mock/operation-status')?.data
const mockProjectDetailData = require('@/mock/project-detail')?.data
const mockExperimentalProcedureData =
  require('@/mock/experimental-procedure')?.data
const mockExperimentDesignsTaskData =
  require('@/mock/experiment-designs-task')?.data
const mockCompoundData = require('@/mock/compound')?.data
const mockWorkbenchData = require('@/mock/my-workbench')?.data
const mockCommendData = require('@/mock/commend')?.data
const mockUserData = require('@/mock/user')?.data
const mockBpmnData = require('@/mock/bpmn')?.data
export {
  erimentDetail,
  experimentExecuteData,
  experimentPlanDetail,
  mockBpmnData,
  mockCommendData,
  mockCompoundData,
  mockExperimentDesignData,
  mockExperimentDesignsTaskData,
  mockExperimentPlanListData,
  mockExperimentalProcedureData,
  mockOperationStatusData,
  mockProjectDetailData,
  mockUserData,
  mockWorkbenchData,
  quotationData
}
