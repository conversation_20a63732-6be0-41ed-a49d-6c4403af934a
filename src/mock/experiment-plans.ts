/* 获取列表（实验计划） */
const Mock = require('mockjs')
const data = Mock.mock({
  data: [
    {
      id: 12,
      experiment_plan_no: 'experiment_plan_no 10',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 11,
      experiment_plan_no: 'experiment_plan_no 9',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 10,
      experiment_plan_no: 'experiment_plan_no 8',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 9,
      experiment_plan_no: 'experiment_plan_no 7',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 8,
      experiment_plan_no: 'experiment_plan_no 6',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 7,
      experiment_plan_no: 'experiment_plan_no 5',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 6,
      experiment_plan_no: 'experiment_plan_no 4',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 5,
      experiment_plan_no: 'experiment_plan_no 3',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 4,
      experiment_plan_no: 'experiment_plan_no 2',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    },
    {
      id: 2,
      experiment_plan_no: 'experiment_plan_no 1',
      name: 'name',
      rxn: 'rxn',
      status: 'created',
      materials: [],
      earliest_start_time: '1972-01-17T09:20:54',
      latest_start_time: '2004-12-26T04:35:06',
      cancel_reason: null,
      project_no: 'voluptate',
      experiment_design_no: 'experiment_plan_no 1',
      experiment_design: null
    }
  ],
  meta: {
    total: 11
  }
})
export { data }
