const Mock = require('mockjs')
const data = Mock.mock({
  rxn: 'CC(C)(C)[O-].CCOC(=O)C(F)C(Br)c1cc(C)c(OC)c(C)c1.[Li+]>>CCOC(=O)C(F)C(NCC(C)C)c1cc(C)c(OC)c(C)c1', // 反应式
  formatted_text:
    '1、added 4-fluorobenzoyl chloride (62 mg, 0.39 mmol，in 1ml DCM) at 0°C<br/>2、the reaction mixture was stirred for half an hour at 0°C.<br/>3、xxxx', // bpmn 版 Procedure
  reference_text:
    '1、added 4-fluorobenzoyl chloride (62 mg, 0.39 mmol，in 1ml DCM) at 0°C<br/>2、the reaction mixture was stirred for half an hour at 0°C.<br/>3、xxxx' // bpmn 版 Procedure
})
export { data }
