const Mock = require('mockjs')
const data = Mock.mock({
  pathManageDetail: {
    data: [
      {
        id: 0,
        attributes: {
          routeNo: 'string',
          targetSmiles: 'string',
          targetMolecule: {
            data: {
              id: 0,
              attributes: {
                moleculeNo: 'string',
                projectNo: 'string',
                smiles: 'string',
                status: 'designing',
                routes: {
                  data: [
                    {
                      id: 0,
                      attributes: {
                        routeNo: 'string',
                        targetSmiles: 'string',
                        targetMolecule: {
                          data: {
                            id: 0,
                            attributes: {}
                          }
                        },
                        stepCount: 0,
                        status: 'created',
                        routeTree: null,
                        procedures: {
                          data: [
                            {
                              id: 0,
                              attributes: {
                                experimentDesigns: {},
                                route: {},
                                createdBy: {},
                                updatedBy: {}
                              }
                            }
                          ]
                        },
                        createdAt: '2019-08-24T14:15:22Z',
                        updatedAt: '2019-08-24T14:15:22Z',
                        publishedAt: '2019-08-24T14:15:22Z',
                        createdBy: {
                          data: {
                            id: 0,
                            attributes: {}
                          }
                        },
                        updatedBy: {
                          data: {
                            id: 0,
                            attributes: {}
                          }
                        }
                      }
                    }
                  ]
                },
                createdAt: '2019-08-24T14:15:22Z',
                updatedAt: '2019-08-24T14:15:22Z',
                publishedAt: '2019-08-24T14:15:22Z',
                createdBy: {
                  data: {
                    id: 0,
                    attributes: {}
                  }
                },
                updatedBy: {
                  data: {
                    id: 0,
                    attributes: {}
                  }
                }
              }
            }
          },
          stepCount: 0,
          status: 'created',
          routeTree: null,
          procedures: {
            data: [
              {
                id: 0,
                attributes: {}
              }
            ]
          },
          createdAt: '2019-08-24T14:15:22Z',
          updatedAt: '2019-08-24T14:15:22Z',
          publishedAt: '2019-08-24T14:15:22Z',
          createdBy: {
            data: {
              id: 0,
              attributes: {}
            }
          },
          updatedBy: {
            data: {
              id: 0,
              attributes: {}
            }
          }
        }
      }
    ],
    meta: {
      pagination: {
        page: 0,
        pageSize: 25,
        pageCount: 1,
        total: 0
      }
    }
  }
})
export { data }
