const Mock = require('mockjs')
/* bpmn拓展属性数据 */
const data = Mock.mock({
  MakeIngredients: {
    data: {
      ingredients: [],
      flat_ingredients: []
    },
    task_schema: {
      title: 'MakeIngredients',
      type: 'object',
      properties: {
        flat_ingredients: {
          title: 'Flat Ingredients',
          type: 'array',
          items: {
            $ref: '#/definitions/FlatIngredient'
          }
        }
      },
      required: ['flat_ingredients'],
      definitions: {
        Solvent: {
          title: 'Solvent',
          type: 'object',
          properties: {
            solvent_name: {
              title: 'Solvent Name',
              type: 'string'
            },
            solvent_quantity: {
              title: 'Solvent Quantity',
              default: -1,
              type: 'number'
            },
            solvent_unit: {
              title: 'Solvent Unit',
              default: 'ml',
              type: 'string'
            },
            concentration: {
              title: 'Concentration',
              type: 'number'
            },
            concentration_unit: {
              title: 'Concentration Unit',
              default: '%',
              type: 'string'
            }
          },
          required: ['solvent_name']
        },
        Normal: {
          title: 'Normal',
          type: 'object',
          properties: {
            method_name: {
              title: 'Method Name',
              type: 'string'
            },
            solvents: {
              title: 'Solvents',
              type: 'array',
              items: {
                $ref: '#/definitions/Solvent'
              }
            },
            temperature: {
              title: '加料温度',
              description: '加料开始时，必须把温度调整到这个温度',
              type: 'number'
            }
          },
          required: ['method_name', 'solvents']
        },
        Dropwise: {
          title: 'Dropwise',
          type: 'object',
          properties: {
            method_name: {
              title: 'Method Name',
              type: 'string'
            },
            solvents: {
              title: 'Solvents',
              type: 'array',
              items: {
                $ref: '#/definitions/Solvent'
              }
            },
            temperature: {
              title: '加料温度',
              description: '加料开始时，必须把温度调整到这个温度',
              type: 'number'
            },
            temperature_control: {
              title: '需要控制的温度',
              description: '加料时，需要把温度控制在这个值以下',
              type: 'number'
            }
          },
          required: ['method_name', 'solvents']
        },
        Batch: {
          title: 'Batch',
          type: 'object',
          properties: {
            method_name: {
              title: 'Method Name',
              type: 'string'
            },
            solvents: {
              title: 'Solvents',
              type: 'array',
              items: {
                $ref: '#/definitions/Solvent'
              }
            },
            temperature: {
              title: '加料温度',
              description: '加料开始时，必须把温度调整到这个温度',
              type: 'number'
            },
            temperature_control: {
              title: '需要控制的温度',
              description: '加料时，需要把温度控制在这个值以下',
              type: 'number'
            },
            batch_count: {
              title: '固体加料时候分几批',
              type: 'integer'
            }
          },
          required: ['method_name', 'solvents']
        },
        FlatIngredient: {
          title: 'FlatIngredient',
          type: 'object',
          properties: {
            ingredient_id: {
              title: 'Ingredient Id',
              type: 'string'
            },
            ingredient_name: {
              title: 'Ingredient Name',
              type: 'string'
            },
            material_no: {
              title: '物料的编号',
              description: '类似R1,R2,R3的编号',
              type: 'string'
            },
            scale_at_add: {
              title: '是否现配现加',
              description: '不在称量区称量，而是在通风橱称量',
              type: 'boolean'
            },
            add_method: {
              title: '加料方式',
              anyOf: [
                {
                  $ref: '#/definitions/Normal'
                },
                {
                  $ref: '#/definitions/Dropwise'
                },
                {
                  $ref: '#/definitions/Batch'
                }
              ]
            }
          },
          required: [
            'ingredient_id',
            'ingredient_name',
            'material_no',
            'scale_at_add'
          ]
        }
      }
    },
    validation_error: '{}'
  }
})
export { data }
