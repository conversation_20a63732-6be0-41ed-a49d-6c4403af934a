const Mock = require('mockjs')
const data = Mock.mock({
  data: [
    {
      id: 1,
      experiment_design_no: 'experiment_design_no',
      name: 'name',
      status: 'published',
      rxn: 'OC(C1=CN2CCCC3=C2C1=CC=C3)=O>[O-][Cu][Cr][Cr](=O)(=O)=O>C4(CCCN5C=C6)=C5C6=CC=C4',
      reference_text: 'reference_text',
      formatted_text: 'formatted_text',
      materials: [],
      workflow: null,
      experiments: [
        {
          id: 0,
          status: 'success'
        },
        {
          id: 1,
          status: 'success'
        },
        {
          id: 2,
          status: 'failed'
        },
        {
          id: 3,
          status: 'created'
        }
      ]
    }
  ],
  meta: {
    total: 1
  }
})
export { data }
