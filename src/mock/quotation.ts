const Mock = require('mockjs')
const data = Mock.mock({
  data: {
    data: [
      {
        id: 1,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-06T09:21:07.119Z',
          updatedAt: '2024-03-06T09:21:12.353Z',
          publishedAt: '2024-03-06T09:21:12.190Z',
          system_message: { data: null }
        }
      },
      {
        id: 3,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-06T09:28:50.746Z',
          updatedAt: '2024-03-06T09:28:57.867Z',
          publishedAt: '2024-03-06T09:28:57.716Z',
          system_message: { data: null }
        }
      },
      {
        id: 9,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:45:48.928Z',
          updatedAt: '2024-03-11T08:45:48.928Z',
          publishedAt: '2024-03-11T08:45:48.925Z',
          system_message: {
            data: {
              id: 13,
              attributes: {
                experiment_no: 'E-202311131',
                message_level: 'I',
                createdAt: '2024-03-11T08:45:48.874Z',
                updatedAt: '2024-03-11T08:45:48.874Z',
                publishedAt: '2024-03-11T08:45:48.871Z',
                message: null,
                project_no: null,
                event_type: null,
                event_data: {},
                event_level: 'experiment'
              }
            }
          }
        }
      },
      {
        id: 13,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T09:01:14.343Z',
          updatedAt: '2024-03-11T09:01:14.343Z',
          publishedAt: '2024-03-11T09:01:14.339Z',
          system_message: {
            data: {
              id: 17,
              attributes: {
                experiment_no: 'E-202311131',
                message_level: 'I',
                createdAt: '2024-03-11T09:01:14.285Z',
                updatedAt: '2024-03-11T09:01:14.285Z',
                publishedAt: '2024-03-11T09:01:14.283Z',
                message: null,
                project_no: null,
                event_type: null,
                event_data: {},
                event_level: 'experiment'
              }
            }
          }
        }
      },
      {
        id: 11,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:48:49.811Z',
          updatedAt: '2024-03-11T08:48:49.811Z',
          publishedAt: '2024-03-11T08:48:49.806Z',
          system_message: {
            data: {
              id: 15,
              attributes: {
                experiment_no: 'E-202311131',
                message_level: 'I',
                createdAt: '2024-03-11T08:48:49.787Z',
                updatedAt: '2024-03-11T08:48:49.787Z',
                publishedAt: '2024-03-11T08:48:49.782Z',
                message: null,
                project_no: null,
                event_type: null,
                event_data: {},
                event_level: 'experiment'
              }
            }
          }
        }
      },
      {
        id: 8,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:06:50.033Z',
          updatedAt: '2024-03-11T08:06:50.033Z',
          publishedAt: '2024-03-11T08:06:50.027Z',
          system_message: {
            data: {
              id: 12,
              attributes: {
                experiment_no: 'E-202311131',
                message_level: 'I',
                createdAt: '2024-03-11T08:06:50.001Z',
                updatedAt: '2024-03-11T08:06:50.001Z',
                publishedAt: '2024-03-11T08:06:49.995Z',
                message: null,
                project_no: null,
                event_type: null,
                event_data: {},
                event_level: 'experiment'
              }
            }
          }
        }
      },
      {
        id: 12,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:49:27.115Z',
          updatedAt: '2024-03-11T08:49:27.115Z',
          publishedAt: '2024-03-11T08:49:27.112Z',
          system_message: {
            data: {
              id: 16,
              attributes: {
                experiment_no: 'E-202311131',
                message_level: 'I',
                createdAt: '2024-03-11T08:49:27.092Z',
                updatedAt: '2024-03-11T08:49:27.092Z',
                publishedAt: '2024-03-11T08:49:27.085Z',
                message: null,
                project_no: null,
                event_type: null,
                event_data: {},
                event_level: 'experiment'
              }
            }
          }
        }
      },
      {
        id: 10,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:47:51.357Z',
          updatedAt: '2024-03-11T08:47:51.357Z',
          publishedAt: '2024-03-11T08:47:51.353Z',
          system_message: {
            data: {
              id: 14,
              attributes: {
                experiment_no: 'E-202311131',
                message_level: 'I',
                createdAt: '2024-03-11T08:47:51.338Z',
                updatedAt: '2024-03-11T08:47:51.338Z',
                publishedAt: '2024-03-11T08:47:51.333Z',
                message: null,
                project_no: null,
                event_type: null,
                event_data: {},
                event_level: 'experiment'
              }
            }
          }
        }
      },
      {
        id: 7,
        attributes: {
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T07:36:46.568Z',
          updatedAt: '2024-03-11T07:36:46.568Z',
          publishedAt: '2024-03-11T07:36:46.565Z',
          system_message: {
            data: {
              id: 11,
              attributes: {
                experiment_no: 'E-202311131',
                message_level: 'I',
                createdAt: '2024-03-11T07:36:46.510Z',
                updatedAt: '2024-03-11T07:36:46.510Z',
                publishedAt: '2024-03-11T07:36:46.509Z',
                message: null,
                project_no: null,
                event_type: null,
                event_data: {},
                event_level: 'experiment'
              }
            }
          }
        }
      }
    ],
    meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 9 } }
  },
  quoteInfo: {
    id: 58,
    attributes: {
      status: 'draft',
      confirmer: null,
      ratio: 1,
      purity: 80,
      createdAt: '2023-07-29T05:49:40.251Z',
      updatedAt: '2023-07-29T05:49:40.251Z',
      confirm_time: null,
      project_route_id: '50',
      target_weight: 11,
      project_compound_id: '110',
      FTE_unit_price: 398,
      other_costs: [],
      cost_detail: [
        {
          labor: 3,
          step_id: '90',
          materials: [
            {
              no: 'fe5eb89366d94964a8c0c8d64f090eb6',
              smi: 'N/C(=N\\O)C1=CC=C2C(=C1)N=NN2C1CCCC1',
              cost: 428,
              cas_no: '',
              detail: [],
              name_zh: '',
              name_en: "1-cyclopentyl-N'-hydroxy-benzotriazole-5-carboxamidine",
              equivalent: 1,
              required_quantity: 10.93986521744947
            },
            {
              no: '936f0ad19c624e46a1511b844c5bb751',
              smi: 'CC1=CC=C(S(=O)(=O)NCCC(=O)O)C=C1',
              cost: 275,
              cas_no: '',
              detail: [],
              name_zh: '',
              name_en: '3-(p-tolylsulfonylamino)propanoic acid',
              equivalent: 1,
              required_quantity: 10.850575122762724
            }
          ],
          step_info: {
            rxn: 'N/C(=N\\O)C1=CC=C2C(=C1)N=NN2C1CCCC1>>CC1=CC=C(S(=O)(=O)NCCC2=NC(C3=CC=C4C(=C3)N=NN4C3CCCC3)=NO2)C=C1',
            index: 0,
            step_id: 90,
            step_no: 'A-5',
            route_id: 50,
            procedure: {
              id: 3581527,
              rxn: 'N/C(=N\\O)C1=CC=C2C(=C1)N=NN2C1CCCC1.CC1=CC=C(S(=O)(=O)NCCC(=O)O)C=C1.CCCP1(=O)OP(=O)(CCC)OP(=O)(CCC)O1.CCN(C(C)C)C(C)C>>CC1=CC=C(S(=O)(=O)NCCC2=NC(C3=CC=C4C(=C3)N=NN4C3CCCC3)=NO2)C=C1',
              date: null,
              rank: 1,
              query: null,
              title: null,
              yields: 54.5,
              authors: null,
              assignees: null,
              patent_id: null,
              procedure:
                '100 mg (0.244 mmol) of 1-[(4-cyanopiperidin-1-yl)carbonyl]-5-[4-(trifluoromethyl)phenyl]piperidine-3-carboxylic acid (Example 100A) and 45.8 mg (0.269 mmol) of 2-chloro-N′-hydroxybenzenecarboximidamide were reacted according to the General Method 1. Yield: 72.4 mg (55% of theory).',
              created_by: '1',
              rxn_yields: '54.5%',
              similarity: null,
              reference_type: 'C12',
              transformation: null,
              last_update_time: '2023-07-26 10:48:34',
              experimental_procedure: null
            },
            reaction_ids: [3581527]
          }
        },
        {
          labor: 3,
          step_id: '91',
          materials: [
            {
              no: '092814b4bf024d39a1c6aac9681c6af9',
              smi: 'N#CC1=CC=C(NC2CCCC2)C(N)=C1',
              cost: 504,
              cas_no: '',
              detail: [],
              name_zh: '',
              name_en: '3-amino-4-(cyclopentylamino)benzonitrile',
              equivalent: 1,
              required_quantity: 14.90116382373845
            },
            {
              no: '821ed5bb468f412c87bae81032e6e481',
              smi: 'O=N[O-]',
              cost: 207,
              cas_no: '14797-65-0',
              detail: [],
              name_zh: '',
              name_en: 'nitrite',
              equivalent: 1,
              required_quantity: 3.405961265103057
            }
          ],
          step_info: {
            rxn: 'N#CC1=CC=C(NC2CCCC2)C(N)=C1>>N#CC1=CC=C2C(=C1)N=NN2C1CCCC1',
            index: 0,
            step_id: 91,
            step_no: 'A-3',
            route_id: 50,
            procedure: {
              id: 3581529,
              rxn: 'N#CC1=CC=C(NC2CCCC2)C(N)=C1.O=N[O-].CC1=CC=C(S(=O)(=O)O)C=C1.[Na+]>>N#CC1=CC=C2C(=C1)N=NN2C1CCCC1',
              date: null,
              rank: 1,
              query: null,
              title: null,
              yields: 70,
              authors: null,
              assignees: null,
              patent_id: null,
              procedure:
                'To a solution of 6-methyl-N4-(pyridin-2-yl)pyridine-3,4-diamine (550 mg, 2.7 mmol) in THE (30 mL) was added acetic acid (0.17 mL, 6.49 mmol) and tert-butyl nitrite (0.54 mL, 4.12 mmol). The reaction was then heated to reflux and stirred overnight. The reaction was concentrated and the residue was purified by chromatography on silica gel (0-10% [2M NH3 in MeOH]/CH2Cl2) to give the desired product as a yellow gum (408 mg, 70%). MS (ESI) mass calcd. C11H9N5, 211.09; m/z found, 212.1 [M+H]+. 1H NMR (500 MHz, CDCl3) S 9.44 (d, J=0.9, 1H), 8.67-8.62 (m, 1H), 8.37 (s, 1H), 8.31-8.23 m, 1H), 8.01-7.96 (m, 1H), 7.41-7.35 (m, 1H), 2.80 (s, 3H).',
              created_by: '1',
              rxn_yields: '70%',
              similarity: null,
              reference_type: 'C12',
              transformation: null,
              last_update_time: '2023-07-26 14:24:58',
              experimental_procedure: null
            },
            reaction_ids: [3581529]
          }
        },
        {
          labor: 3,
          step_id: '92',
          materials: [
            {
              no: '3d0e2300ae8b4bf482d8edceb2bc24eb',
              smi: 'N#CC1=CC=C(F)C([N+](=O)[O-])=C1',
              cost: 496,
              cas_no: '1009-35-4',
              detail: [],
              name_zh: '',
              name_en: '4-fluoro-3-nitro-benzonitrile',
              equivalent: 1,
              required_quantity: 8.054358182210613
            },
            {
              no: '89d5330069e64a7b8059a5ba17196e8b',
              smi: 'NC1CCCC1',
              cost: 98,
              cas_no: '1003-03-8',
              detail: [],
              name_zh: '',
              name_en: 'cyclopentanamine',
              equivalent: 1,
              required_quantity: 4.128736803795256
            }
          ],
          step_info: {
            rxn: 'N#CC1=CC=C(F)C([N+](=O)[O-])=C1>>N#CC1=CC=C(NC2CCCC2)C([N+](=O)[O-])=C1',
            index: 0,
            step_id: 92,
            step_no: 'A-1',
            route_id: 50,
            procedure: {
              id: 3581532,
              rxn: 'N#CC1=CC=C(F)C([N+](=O)[O-])=C1.NC1CCCC1.C1CCOC1.CCN(CC)CC>>N#CC1=CC=C(NC2CCCC2)C([N+](=O)[O-])=C1',
              date: null,
              rank: 1,
              query: null,
              title: null,
              yields: 98.1,
              authors: null,
              assignees: null,
              patent_id: null,
              procedure:
                'To a solution of 4-fluoro-3-nitro-benzonitrile (1.00 g, 6.02 mmol, 1.00 eq) and cyclopentanamine (767 mg, 9.03 mmol, 1.50 eq) in THF (20.00 mL) was added DIEA (1.95 g, 15.05 mmol, 2.63 mL, 2.50 eq) and the mixture was stirred at 10° C. for 16 hour. The mixture was evaporated to dry and diluted with H2O (50 mL), extracted with DCM (50 mL×2), dried over Na2SO4, filtered and concentrated to dry. Compound 4-(cyclopentylamino)-3-nitrobenzonitrile (1.36 g, 5.91 mmol, 98.10% yield) was obtained as a yellow solid which was used directly in next step.',
              created_by: '1',
              rxn_yields: '98.1%',
              similarity: null,
              reference_type: 'C12',
              transformation: null,
              last_update_time: '2023-07-26 14:41:46',
              experimental_procedure: null
            },
            reaction_ids: [3581532, 3581535, 3581557, 3581558]
          }
        },
        {
          labor: 3,
          step_id: '93',
          materials: [
            {
              no: 'f284662fbc4e4fd0963beb96d48e6ea2',
              smi: 'N#CC1=CC=C2C(=C1)N=NN2C1CCCC1',
              cost: 570,
              cas_no: '',
              detail: [],
              name_zh: '',
              name_en: '1-cyclopentylbenzotriazole-5-carbonitrile',
              equivalent: 1,
              required_quantity: 11.898436926689659
            },
            {
              no: '8a5beb48255247cf80c29faffdf6ea87',
              smi: 'NO',
              cost: 78,
              cas_no: '7803-49-8|11104-93-1|13408-29-2|11129-69-4',
              detail: [],
              name_zh: '',
              name_en: 'hydroxylamine',
              equivalent: 1,
              required_quantity: 1.85156307331034
            }
          ],
          step_info: {
            rxn: 'N#CC1=CC=C2C(=C1)N=NN2C1CCCC1>>N/C(=N\\O)C1=CC=C2C(=C1)N=NN2C1CCCC1',
            index: 0,
            step_id: 93,
            step_no: 'A-4',
            route_id: 50,
            procedure: {
              id: 3581528,
              rxn: 'N#CC1=CC=C2C(=C1)N=NN2C1CCCC1.NO.Cl>>N/C(=N\\O)C1=CC=C2C(=C1)N=NN2C1CCCC1',
              date: null,
              rank: 1,
              query: null,
              title: null,
              yields: 80,
              authors: null,
              assignees: null,
              patent_id: null,
              procedure:
                'Compound 5 (4.47 g, 23.89 mmol) was dissolved in ethanol (125 mL) in a 200-mL reaction flask. NH2OH (50%, 5.6 mL) was added and the mixture was stirred at room temperature for 50 h until the reaction was complete as monitored by TLC. The solvent was evaporated under reduced pressure to afford the product as a white solid (5.26 g, 100%).',
              created_by: '1',
              rxn_yields: '80%',
              similarity: null,
              reference_type: 'C12',
              transformation: null,
              last_update_time: '2023-07-26 14:20:47',
              experimental_procedure: null
            },
            reaction_ids: [3581528]
          }
        },
        {
          labor: 3,
          step_id: '94',
          materials: [
            {
              no: '8c14346fe6c54d358ba07b481f413185',
              smi: 'N#CC1=CC=C(NC2CCCC2)C([N+](=O)[O-])=C1',
              cost: 870,
              cas_no: '',
              detail: [],
              name_zh: '',
              name_en: '4-(cyclopentylamino)-3-nitro-benzonitrile',
              equivalent: 1,
              required_quantity: 22.568893634871188
            }
          ],
          step_info: {
            rxn: 'N#CC1=CC=C(NC2CCCC2)C([N+](=O)[O-])=C1>>N#CC1=CC=C(NC2CCCC2)C(N)=C1',
            index: 0,
            step_id: 94,
            step_no: 'A-2',
            route_id: 50,
            procedure: {
              id: 3581531,
              rxn: 'N#CC1=CC=C(NC2CCCC2)C([N+](=O)[O-])=C1.[Cl-].[Fe].[NH4+].CCO.O>>N#CC1=CC=C(NC2CCCC2)C(N)=C1',
              date: null,
              rank: 1,
              query: null,
              title: null,
              yields: 56,
              authors: null,
              assignees: null,
              patent_id: null,
              procedure:
                'The product from Step 1 above (735 mg, 2.80 mmol, 95% purity) was combined with iron powder (3.29 g, 59 mmol) and NH4Cl(s) (205 mg, 3.83 mmol) in 2:1 IPA/water (30 ml), heated at 70 °C overnight. The reaction mixture was allowed to cool then filtered through Celite®, rinsing with EtOAc (200 ml) and the filtrate was concentrated in vacuo. The residue was purified by chromatography on silica gel (24 g cartridge, 0-100% EtOAc/isohexane) to afford the title compound (350 mg, 1.56 mmol, 56% yield, 98% purity) as a red oil. UPLC-MS (Method 2) m/z 220.6 (M+H)+ at 1.27 min.',
              created_by: '1',
              rxn_yields: '56%',
              similarity: null,
              reference_type: 'C12',
              transformation: null,
              last_update_time: '2023-07-26 14:40:48',
              experimental_procedure: null
            },
            reaction_ids: [3581531, 3581534]
          }
        }
      ],
      quote_request_no: '9d15da6f4fe230db26831239549bc332',
      project_id: '13',
      material_cost: 3526,
      delivery_time: 15,
      labor_cost: 5970,
      cost_summary: [
        {
          name: 'material_cost',
          RMB: 3526,
          USD: 492.58
        },
        {
          name: 'labor_cost',
          RMB: 5970,
          USD: 834.01
        },
        {
          total_cost: 'total_cost',
          RMB: 9496,
          USD: 1326.59
        }
      ],
      quotation_summary: '5970 + 3526 = 9496',
      main_tree: {
        id: '0.5541291571116269',
        value:
          'CC1=CC=C(S(=O)(=O)NCCC2=NC(C3=CC=C4C(=C3)N=NN4C3CCCC3)=NO2)C=C1',
        children: [
          {
            id: '0.9002252703448317',
            value: 'N/C(=N\\O)C1=CC=C2C(=C1)N=NN2C1CCCC1',
            parent: '0.5541291571116269',
            children: [
              {
                id: '0.17096260209336944',
                value: 'N#CC1=CC=C2C(=C1)N=NN2C1CCCC1',
                parent: '0.9002252703448317',
                children: [
                  {
                    id: '0.0422771905502235',
                    value: 'N#CC1=CC=C(NC2CCCC2)C(N)=C1',
                    parent: '0.17096260209336944',
                    children: [
                      {
                        id: '0.846483942888216',
                        value: 'N#CC1=CC=C(NC2CCCC2)C([N+](=O)[O-])=C1',
                        parent: '0.0422771905502235',
                        children: [
                          {
                            id: '0.5386105009885098',
                            value: 'N#CC1=CC=C(F)C([N+](=O)[O-])=C1',
                            parent: '0.846483942888216',
                            children: []
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      quote_route: [
        {
          quote_id: 58,
          route_id: '50'
        },
        {
          quote_id: 60,
          route_id: '50'
        }
      ],
      route_index: 0
    }
  },
  quotes: [
    {
      id: 4,
      status: 'editing',
      target_weight: 1,
      FTE_unit_price: 1000,
      project_route_id: '50',
      project_compound_id: '110',
      quotation_summary: `{fte单价}*{估计FTE时间}（labor） + {原料总价} (原料)+ （
      {其他收费项名称}） = {总价}`,
      purity: '10',
      other_costs: [
        {
          name: 'test',
          amount: 500,
          remark: 'dddddd'
        },
        {
          name: 'test2',
          amount: 600,
          remark: 'dddddd'
        }
      ],
      cost_detail: null
    }
  ]
})
export { data }
