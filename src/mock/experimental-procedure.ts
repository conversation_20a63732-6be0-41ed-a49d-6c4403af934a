const Mock = require('mockjs')
const data = Mock.mock({
  newExperimentInfo: {
    material_table: [
      {
        smiles: 'CCOC(=O)COC1=CC([N+](=O)[O-])=CC=C1O',
        role: 'product',
        no: 'R0',
        equivalent: 1,
        unit: 'eq'
      },
      {
        smiles: 'CCOC(=O)COC1=CC([N+](=O)[O-])=CC=C1OC',
        role: 'main_reactant',
        no: 'R1',
        equivalent: 1,
        unit: 'eq'
      },
      {
        smiles: 'BrB(Br)Br',
        role: 'other_reagent',
        no: 'R2',
        equivalent: 1,
        unit: 'eq'
      },
      {
        smiles: 'ClCCl',
        role: 'solvent',
        no: 'R3',
        equivalent: 1,
        unit: 'eq'
      }
    ],
    projectId: 49,
    projectReactionId: 377,
    experiementDesignNo: 'experiment_design-ZZvtfAW2Lwuz9nJQ8XProX'
  }
})
export { data }
