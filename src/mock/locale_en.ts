const Mock = require('mockjs')
const data = Mock.mock(
  {
    id: 3608,
    attributes: {
      code: 'developing-agent',
      value: 'Developing Agent'
    }
  },
  {
    id: 3610,
    attributes: {
      code: 'operate-failed',
      value: 'Operation Failed'
    }
  },
  {
    id: 3612,
    attributes: {
      code: 'ratio',
      value: 'Ratio'
    }
  },
  {
    id: 3615,
    attributes: {
      code: 'show-read-messages',
      value: 'Show Read Messages'
    }
  },
  {
    id: 3617,
    attributes: {
      code: 'reaction-tab',
      value: 'Reaction'
    }
  },
  {
    id: 3531,
    attributes: {
      code: 'task-name',
      value: 'Task Name'
    }
  },
  {
    id: 3533,
    attributes: {
      code: 'task-status',
      value: 'Task Status'
    }
  },
  {
    id: 3535,
    attributes: {
      code: 'start-time',
      value: 'Start Time'
    }
  },
  {
    id: 3537,
    attributes: {
      code: 'end-time',
      value: 'End Time'
    }
  },
  {
    id: 3549,
    attributes: {
      code: 'pages.experiment.check.statusLabel.finished',
      value: 'Finished'
    }
  },
  {
    id: 3587,
    attributes: {
      code: 'app.general.message.failed ',
      value: 'Failed'
    }
  },
  {
    id: 3551,
    attributes: {
      code: 'date',
      value: 'Date'
    }
  },
  {
    id: 3552,
    attributes: {
      code: 'task-finished',
      value: '已完成'
    }
  },
  {
    id: 3554,
    attributes: {
      code: 'task-todo',
      value: '待开始'
    }
  },
  {
    id: 3556,
    attributes: {
      code: 'task-canceled',
      value: '已取消'
    }
  },
  {
    id: 3558,
    attributes: {
      code: 'task-running',
      value: '进行中'
    }
  },
  {
    id: 3560,
    attributes: {
      code: 'task-abnormalterminated',
      value: '异常终止'
    }
  },
  {
    id: 3651,
    attributes: {
      code: 'progress',
      value: 'Progress'
    }
  },
  {
    id: 3619,
    attributes: {
      code: 'by-product',
      value: 'By-product'
    }
  },
  {
    id: 3620,
    attributes: {
      code: 'other-and-impurity',
      value: 'Other'
    }
  },
  {
    id: 3623,
    attributes: {
      code: 'intermediate-detection-next-action-tip',
      value: 'Robot will '
    }
  },
  {
    id: 3669,
    attributes: {
      code: 'declare-tip',
      value: 'declare I have verified all results in this report.'
    }
  },
  {
    id: 3604,
    attributes: {
      code: 'usage-board',
      value: 'Fume Hood Top-down'
    }
  },
  {
    id: 3603,
    attributes: {
      code: 'usage-down',
      value: 'Rear'
    }
  },
  {
    id: 3670,
    attributes: {
      code: 'test-report-inference-success-message',
      value:
        '${check_type} report of ${experiment_no} has been released. You can view report <a href="${report_url}">here</a>. The robot will execute next task at ${robot_start_handle_time}.'
    }
  },
  {
    id: 3605,
    attributes: {
      code: 'intermediate-detection',
      value: 'Intermediate Detection'
    }
  },
  {
    id: 3606,
    attributes: {
      code: 'product-detection',
      value: 'Product Detection'
    }
  },
  {
    id: 3607,
    attributes: {
      code: 'test-type',
      value: 'Test Type'
    }
  },
  {
    id: 3574,
    attributes: {
      code: 'test-record',
      value: 'Test Records'
    }
  },
  {
    id: 3575,
    attributes: {
      code: 'test-sample-id',
      value: 'Test Sample ID'
    }
  },
  {
    id: 3576,
    attributes: {
      code: 'test-method',
      value: 'Test Method'
    }
  },
  {
    id: 3577,
    attributes: {
      code: 'test-launched-time',
      value: 'Test Launched Time'
    }
  },
  {
    id: 3578,
    attributes: {
      code: 'test-sponsor',
      value: 'Test Sponsor'
    }
  },
  {
    id: 3579,
    attributes: {
      code: 'test-status',
      value: 'Test Status'
    }
  },
  {
    id: 3580,
    attributes: {
      code: 'AI-inference-status',
      value: 'AI Inference Status'
    }
  },
  {
    id: 3581,
    attributes: {
      code: 'view-report',
      value: 'View Report'
    }
  },
  {
    id: 3582,
    attributes: {
      code: 'upload-report',
      value: 'Upload Report'
    }
  },
  {
    id: 3583,
    attributes: {
      code: 'new-test',
      value: 'New Test'
    }
  },
  {
    id: 3584,
    attributes: {
      code: 'read',
      value: 'read'
    }
  },
  {
    id: 3585,
    attributes: {
      code: 'unread',
      value: 'unread'
    }
  },
  {
    id: 3539,
    attributes: {
      code: 'lab',
      value: 'Lab'
    }
  },
  {
    id: 3541,
    attributes: {
      code: 'working',
      value: 'Working'
    }
  },
  {
    id: 3543,
    attributes: {
      code: 'idle',
      value: 'Idle'
    }
  },
  {
    id: 3545,
    attributes: {
      code: 'hold',
      value: 'Hold'
    }
  },
  {
    id: 3547,
    attributes: {
      code: 'unavailable',
      value: 'Unavailable'
    }
  },
  {
    id: 2160,
    attributes: {
      code: 'un-select',
      value: 'Unselect'
    }
  },
  {
    id: 2161,
    attributes: {
      code: 'last-modified-time',
      value: 'last modified time'
    }
  },
  {
    id: 3625,
    attributes: {
      code: 'yes',
      value: 'Yes'
    }
  },
  {
    id: 3486,
    attributes: {
      code: 'lab-management',
      value: 'Lab Management'
    }
  },
  {
    id: 2493,
    attributes: {
      code: 'pages.experiment.label.actualStartTime',
      value: 'Start Date'
    }
  },
  {
    id: 2494,
    attributes: {
      code: 'pages.experiment.label.actualEndTime',
      value: 'End Date'
    }
  },
  {
    id: 2495,
    attributes: {
      code: 'pages.experiment.label.owner',
      value: 'Owner'
    }
  },
  {
    id: 2496,
    attributes: {
      code: 'pages.experiment.label.personInCharge',
      value: 'Person in charge'
    }
  },
  {
    id: 2497,
    attributes: {
      code: 'pages.experiment.label.operation',
      value: 'Action'
    }
  },
  {
    id: 3342,
    attributes: {
      code: 'pages.projectTable.statusLabel.synthesizing',
      value: 'In Synthesis'
    }
  },
  {
    id: 2382,
    attributes: {
      code: 'pages.login.success',
      value: 'Login Succeed!'
    }
  },
  {
    id: 2028,
    attributes: {
      code: 'conclusion',
      value: 'Conclusion'
    }
  },
  {
    id: 2065,
    attributes: {
      code: 'choose-route-tip',
      value:
        'After selecting this route, the original route will be replaced with the chosen one and this action cannot be reversed!'
    }
  },
  {
    id: 2269,
    attributes: {
      code: 'reaction',
      value: 'Reaction'
    }
  },
  {
    id: 3394,
    attributes: {
      code: 'reaction-reliability',
      value: 'Reliability'
    }
  },
  {
    id: 2084,
    attributes: {
      code: 'estimate-work-day',
      value: 'Estimated Working Time/ Day'
    }
  },
  {
    id: 2085,
    attributes: {
      code: 'estimate-delivery',
      value: 'Estimated Delivery Time (Man-Days)'
    }
  },
  {
    id: 2087,
    attributes: {
      code: 'charge-items',
      value: 'Charge Items'
    }
  },
  {
    id: 3502,
    attributes: {
      code: 'stop',
      value: 'Stop'
    }
  },
  {
    id: 2088,
    attributes: {
      code: 'quotate-route',
      value: 'Quotation Route'
    }
  },
  {
    id: 2089,
    attributes: {
      code: 'material-list',
      value: 'Material List'
    }
  },
  {
    id: 2090,
    attributes: {
      code: 'work-time-detils',
      value: 'Working Time Details List'
    }
  },
  {
    id: 2091,
    attributes: {
      code: 'cost-empty-tip',
      value:
        'If the cost is empty it means no suitable materials were matched; please select manually or edit after inquiring'
    }
  },
  {
    id: 3627,
    attributes: {
      code: 'no',
      value: 'No'
    }
  },
  {
    id: 3632,
    attributes: {
      code: 'reference-report',
      value: 'Reference'
    }
  },
  {
    id: 2092,
    attributes: {
      code: 'quotation-sheet',
      value: 'Quotation'
    }
  },
  {
    id: 2095,
    attributes: {
      code: 'cost-summary',
      value: 'Cost Summary'
    }
  },
  {
    id: 2096,
    attributes: {
      code: 'measurement-method',
      value: 'Measurement Method'
    }
  },
  {
    id: 2097,
    attributes: {
      code: 'required-weight',
      value: 'Required weight'
    }
  },
  {
    id: 2100,
    attributes: {
      code: 'FTE-per-day-unit',
      value: 'yuan/Per Day'
    }
  },
  {
    id: 2101,
    attributes: {
      code: 'coefficient',
      value: 'Coefficient'
    }
  },
  {
    id: 2102,
    attributes: {
      code: 'quote-parmas',
      value: 'Quotation Parameters'
    }
  },
  {
    id: 2103,
    attributes: {
      code: 'quote-sum',
      value: 'Summary'
    }
  },
  {
    id: 2104,
    attributes: {
      code: 'created-at',
      value: 'Created at'
    }
  },
  {
    id: 2106,
    attributes: {
      code: 'expected-quantity',
      value: 'Expected weight'
    }
  },
  {
    id: 2107,
    attributes: {
      code: 'only-show-confirmed-routes',
      value: 'Only show molecules with confirmed routes.'
    }
  },
  {
    id: 2108,
    attributes: {
      code: 'download-selected-quatation',
      value: 'Download Selected Quotation'
    }
  },
  {
    id: 2109,
    attributes: {
      code: 'download-quatation',
      value: 'Download Quotation'
    }
  },
  {
    id: 2110,
    attributes: {
      code: 'nums-materials',
      value: 'Number of Materials'
    }
  },
  {
    id: 2113,
    attributes: {
      code: 'in-use',
      value: 'In use'
    }
  },
  {
    id: 2122,
    attributes: {
      code: 'sort',
      value: 'Sort'
    }
  },
  {
    id: 3518,
    attributes: {
      code: 'product-not-identified-materials-remaining',
      value: "Product hasn't been identified with some raw materials remaining."
    }
  },
  {
    id: 3520,
    attributes: {
      code: 'action-proposal',
      value: 'Action Proposal'
    }
  },
  {
    id: 3522,
    attributes: {
      code: 'try-another-procedure',
      value: 'Try another procedure:'
    }
  },
  {
    id: 3523,
    attributes: {
      code: 'continue',
      value: 'Continue'
    }
  },
  {
    id: 3524,
    attributes: {
      code: 'cancel-experiment',
      value: 'Cancel Experiment'
    }
  },
  {
    id: 3525,
    attributes: {
      code: 'message',
      value: 'Message'
    }
  },
  {
    id: 3526,
    attributes: {
      code: 'task-list',
      value: 'Task List'
    }
  },
  {
    id: 3527,
    attributes: {
      code: 'send-time',
      value: 'Send Time'
    }
  },
  {
    id: 2363,
    attributes: {
      code: 'other',
      value: 'Other'
    }
  },
  {
    id: 2123,
    attributes: {
      code: 'favorite-routes',
      value: 'Favorite Routes'
    }
  },
  {
    id: 2124,
    attributes: {
      code: 'only-one-added',
      value:
        'Please ensure the current item is completely edited before adding a new one.'
    }
  },
  {
    id: 2125,
    attributes: {
      code: 'only-one-edit',
      value: 'Only one line can be edited at a time'
    }
  },
  {
    id: 2555,
    attributes: {
      code: 'Reaction',
      value: 'Reaction'
    }
  },
  {
    id: 2558,
    attributes: {
      code: 'TLC',
      value: 'TLC'
    }
  },
  {
    id: 2133,
    attributes: {
      code: 'route-generate-failed',
      value:
        'Route generation failed. Please try again later or contact the administrator'
    }
  },
  {
    id: 2136,
    attributes: {
      code: 'new-molecule-input',
      value:
        'The new molecule you entered will overwrite the structure on the canvas.'
    }
  },
  {
    id: 2139,
    attributes: {
      code: 'cancel-task-confirm',
      value: 'Cancel this task?'
    }
  },
  {
    id: 2141,
    attributes: {
      code: 'apply',
      value: 'Apply'
    }
  },
  {
    id: 2142,
    attributes: {
      code: 'unfavorite',
      value: 'Unfavorite'
    }
  },
  {
    id: 2143,
    attributes: {
      code: 'success-update-status',
      value: 'Successfully update route status'
    }
  },
  {
    id: 2144,
    attributes: {
      code: 'temporary-route-tip',
      value:
        'Temporary routes cannot be added to favorites. However you can view the route details and save them from there.'
    }
  },
  {
    id: 2505,
    attributes: {
      code: 'pages.experiment.label.operation.viewConclusion',
      value: 'View Conclusion'
    }
  },
  {
    id: 2086,
    attributes: {
      code: 'steps',
      value: 'Steps'
    }
  },
  {
    id: 2038,
    attributes: {
      code: 'confirm-delete-molecule-route',
      value:
        'Are you sure you want to delete this molecule and its synthesis route?'
    }
  },
  {
    id: 2039,
    attributes: {
      code: 'confirm-delete-synthesis-route',
      value:
        'Are you sure you want to delete this molecule and its synthesis route?'
    }
  },
  {
    id: 2040,
    attributes: {
      code: 'system-update',
      value: 'The system has been updated the page will refresh automatically.'
    }
  },
  {
    id: 2415,
    attributes: {
      code: 'pages.projectTable.label.deliveryDate',
      value: 'Due Date'
    }
  },
  {
    id: 2538,
    attributes: {
      code: 'Dissolve',
      value: 'Dissolve'
    }
  },
  {
    id: 2049,
    attributes: {
      code: 'next',
      value: 'Next'
    }
  },
  {
    id: 3345,
    attributes: {
      code: 'yuan',
      value: 'yuan'
    }
  },
  {
    id: 2044,
    attributes: {
      code: 'reaction',
      value: 'Reaction'
    }
  },
  {
    id: 2126,
    attributes: {
      code: 'edit-molecule',
      value: 'Edit Molecule'
    }
  },
  {
    id: 2117,
    attributes: {
      code: 'blacklist',
      value: 'Blacklist'
    }
  },
  {
    id: 2127,
    attributes: {
      code: 'modified-time',
      value: 'Modified time'
    }
  },
  {
    id: 2111,
    attributes: {
      code: 'synthesised',
      value: 'To be synthesised'
    }
  },
  {
    id: 2112,
    attributes: {
      code: 'deleted',
      value: 'Deleted'
    }
  },
  {
    id: 2120,
    attributes: {
      code: 'updated-at',
      value: 'Updated at'
    }
  },
  {
    id: 2115,
    attributes: {
      code: 'labor-cost',
      value: 'Labor Cost'
    }
  },
  {
    id: 2116,
    attributes: {
      code: 'material-cost',
      value: 'Material Cost'
    }
  },
  {
    id: 2147,
    attributes: {
      code: 'copy-full',
      value: 'Copy full text'
    }
  },
  {
    id: 2148,
    attributes: {
      code: 'material-demand',
      value: 'Material demand'
    }
  },
  {
    id: 2149,
    attributes: {
      code: 'search-failed-I',
      value: 'Search failed.'
    }
  },
  {
    id: 2305,
    attributes: {
      code: 'to-be-proceeded',
      value: 'To be proceeded'
    }
  },
  {
    id: 2307,
    attributes: {
      code: 'create-project-tip-I',
      value: 'No projects available'
    }
  },
  {
    id: 3348,
    attributes: {
      code: 'cost-conflict-quote-double-check',
      value:
        'The following materials have different estimated costs and costs, whether to confirm the quotation?'
    }
  },
  {
    id: 3358,
    attributes: {
      code: 'project-summary',
      value: 'project summary'
    }
  },
  {
    id: 3499,
    attributes: {
      code: 'live',
      value: 'Live'
    }
  },
  {
    id: 2308,
    attributes: {
      code: 'create-project-tip-II',
      value: 'Please click'
    }
  },
  {
    id: 2057,
    attributes: {
      code: 'main-reactant',
      value: 'Main Reactant'
    }
  },
  {
    id: 2015,
    attributes: {
      code: 'quotation-settings',
      value: 'Quotation Settings'
    }
  },
  {
    id: 2463,
    attributes: {
      code: 'pages.reaction.label.stepName',
      value: 'Step Name'
    }
  },
  {
    id: 2099,
    attributes: {
      code: 'FTE-per-day',
      value: 'FTE'
    }
  },
  {
    id: 2287,
    attributes: {
      code: 'Priority',
      value: 'Priority'
    }
  },
  {
    id: 2288,
    attributes: {
      code: 'molecule-priority',
      value: 'Molecule Priority'
    }
  },
  {
    id: 2289,
    attributes: {
      code: 'molecule-owner',
      value: 'Molecule Owner'
    }
  },
  {
    id: 2290,
    attributes: {
      code: 'max-l-50',
      value: 'Maximum 50 Characters'
    }
  },
  {
    id: 2291,
    attributes: {
      code: 'max-l-30',
      value: 'Maximum 30 Characters'
    }
  },
  {
    id: 2293,
    attributes: {
      code: 'Routes',
      value: 'Routes'
    }
  },
  {
    id: 2294,
    attributes: {
      code: 'team-allocation',
      value: 'Team Configuration'
    }
  },
  {
    id: 3629,
    attributes: {
      code: 'restore-robot-operations',
      value: 'Restore Robot Operations'
    }
  },
  {
    id: 3634,
    attributes: {
      code: 'show-reference-report',
      value: 'Show Reference'
    }
  },
  {
    id: 3376,
    attributes: {
      code: 'futures',
      value: 'Futures'
    }
  },
  {
    id: 3352,
    attributes: {
      code: 'similarity',
      value: 'Similarity'
    }
  },
  {
    id: 3354,
    attributes: {
      code: 'please-quote',
      value: 'Please quote'
    }
  },
  {
    id: 3356,
    attributes: {
      code: 'please-input-positive-number',
      value: 'Please enter a positive number'
    }
  },
  {
    id: 3386,
    attributes: {
      code: 'demension-weight',
      value: 'Preference'
    }
  },
  {
    id: 3380,
    attributes: {
      code: 'route-price-score',
      value: 'Cost Score'
    }
  },
  {
    id: 2073,
    attributes: {
      code: 'save-build-block',
      value: 'Save as building block'
    }
  },
  {
    id: 2367,
    attributes: {
      code: 'material-sheet',
      value: 'Material Sheet'
    }
  },
  {
    id: 2487,
    attributes: {
      code: 'pages.experiment.label.no',
      value: 'Experiment No'
    }
  },
  {
    id: 2162,
    attributes: {
      code: 'experiment-log',
      value: 'experiment log'
    }
  },
  {
    id: 2163,
    attributes: {
      code: 'reaction-parameters',
      value: 'Reaction Parameters'
    }
  },
  {
    id: 2164,
    attributes: {
      code: 'batch-add-blacklist',
      value: 'Batch Add to Blacklist'
    }
  },
  {
    id: 2165,
    attributes: {
      code: 'reson-add-blacklist',
      value: 'Reason for Adding to Blacklist'
    }
  },
  {
    id: 2166,
    attributes: {
      code: 'actual-mass',
      value: 'Actual weight'
    }
  },
  {
    id: 2471,
    attributes: {
      code: 'pages.reaction.statusLabel.editing',
      value: 'Editing'
    }
  },
  {
    id: 2472,
    attributes: {
      code: 'pages.reaction.statusLabel.confirmed',
      value: 'Confirmed'
    }
  },
  {
    id: 2473,
    attributes: {
      code: 'pages.reaction.statusLabel.canceled',
      value: 'Canceled'
    }
  },
  {
    id: 2474,
    attributes: {
      code: 'pages.route.label.showComplete',
      value: 'Show Full Route'
    }
  },
  {
    id: 2475,
    attributes: {
      code: 'pages.route.label.hideComplete',
      value: 'Show Route Backbone'
    }
  },
  {
    id: 2476,
    attributes: {
      code: 'pages.route.label.addToMyReaction',
      value: 'Add to My reaction'
    }
  },
  {
    id: 2477,
    attributes: {
      code: 'pages.route.label.confirmToDeleteMyReaction',
      value: 'Are you sure to delete this reaction?'
    }
  },
  {
    id: 2478,
    attributes: {
      code: 'pages.route.statusLabel.editing',
      value: 'Editing'
    }
  },
  {
    id: 2479,
    attributes: {
      code: 'pages.route.statusLabel.canceled',
      value: 'Canceled'
    }
  },
  {
    id: 2480,
    attributes: {
      code: 'pages.route.statusLabel.confirmed',
      value: 'Confirmed'
    }
  },
  {
    id: 2481,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.created',
      value: 'Editing'
    }
  },
  {
    id: 2482,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.validated',
      value: 'Editing'
    }
  },
  {
    id: 2483,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.published',
      value: 'Published'
    }
  },
  {
    id: 2484,
    attributes: {
      code: 'pages.experimentDesign.statusLabel.canceled',
      value: 'Canceled'
    }
  },
  {
    id: 2485,
    attributes: {
      code: 'pages.experiment',
      value: 'Experiments'
    }
  },
  {
    id: 2486,
    attributes: {
      code: 'pages.experiment.label.name',
      value: 'Experiment Name'
    }
  },
  {
    id: 2488,
    attributes: {
      code: 'pages.experiment.label.type',
      value: 'Experiment Type'
    }
  },
  {
    id: 2489,
    attributes: {
      code: 'pages.experiment.label.procedureName',
      value: 'Procedure name'
    }
  },
  {
    id: 2490,
    attributes: {
      code: 'pages.experiment.label.experimentDesignName',
      value: 'Experiment design name'
    }
  },
  {
    id: 2491,
    attributes: {
      code: 'pages.experiment.label.status',
      value: 'Status'
    }
  },
  {
    id: 2567,
    attributes: {
      code: 'EXTENSIONS',
      value: 'Extensions'
    }
  },
  {
    id: 2492,
    attributes: {
      code: 'pages.experiment.label.priority',
      value: 'Priority'
    }
  },
  {
    id: 2498,
    attributes: {
      code: 'pages.experiment.label.operation.view',
      value: 'Experiment Workflow'
    }
  },
  {
    id: 2435,
    attributes: {
      code: 'pages.searchTable.ruleName',
      value: 'Rule name is required'
    }
  },
  {
    id: 2501,
    attributes: {
      code: 'pages.experiment.label.operation.conclution',
      value: 'Conclution'
    }
  },
  {
    id: 2502,
    attributes: {
      code: 'pages.experiment.label.operation.stop',
      value: 'Stop'
    }
  },
  {
    id: 2503,
    attributes: {
      code: 'pages.experiment.label.operation.pause',
      value: 'Pause'
    }
  },
  {
    id: 2504,
    attributes: {
      code: 'pages.experiment.label.operation.resume',
      value: 'Resume'
    }
  },
  {
    id: 2506,
    attributes: {
      code: 'pages.experiment.statusLabel.created',
      value: 'Created'
    }
  },
  {
    id: 2507,
    attributes: {
      code: 'pages.experiment.statusLabel.running',
      value: 'Running'
    }
  },
  {
    id: 2394,
    attributes: {
      code: 'pages.login.forgotPassword',
      value: 'Forgot Password ?'
    }
  },
  {
    id: 2395,
    attributes: {
      code: 'pages.login.loginWith',
      value: 'Login with'
    }
  },
  {
    id: 2396,
    attributes: {
      code: 'pages.login.registerAccount',
      value: 'Register Account'
    }
  },
  {
    id: 3341,
    attributes: {
      code: 'default-material-sources-for-retrosynthesis',
      value: 'Default Material Sources for Retrosynthesis'
    }
  },
  {
    id: 2381,
    attributes: {
      code: 'pages.login.failure',
      value: 'Login failed please try again!'
    }
  },
  {
    id: 3349,
    attributes: {
      code: 'calculate-quotation',
      value: 'Calculate quotation'
    }
  },
  {
    id: 3630,
    attributes: {
      code: 'pages.experiment.label.operation.detectRecord',
      value: 'Test Record'
    }
  },
  {
    id: 3636,
    attributes: {
      code: 'publish',
      value: 'Publish'
    }
  },
  {
    id: 2584,
    attributes: {
      code: 'menu.login',
      value: 'Login'
    }
  },
  {
    id: 3347,
    attributes: {
      code: 'min-cost',
      value: 'estimated minimum cost'
    }
  },
  {
    id: 3335,
    attributes: {
      code: 'cas-not-found',
      value: 'CAS not found'
    }
  },
  {
    id: 3337,
    attributes: {
      code: 'click-to-copy-cas',
      value: 'Click to copy CAS'
    }
  },
  {
    id: 2383,
    attributes: {
      code: 'pages.login.username.required',
      value: 'Please input your username'
    }
  },
  {
    id: 3382,
    attributes: {
      code: 'route-safety-score',
      value: 'Safety Score'
    }
  },
  {
    id: 3388,
    attributes: {
      code: 'poisonous',
      value: 'Acute Toxicity'
    }
  },
  {
    id: 3392,
    attributes: {
      code: 'danger-reaction',
      value: 'Danger'
    }
  },
  {
    id: 3372,
    attributes: {
      code: 'spot-or-futures',
      value: 'Spot/Futures'
    }
  },
  {
    id: 3374,
    attributes: {
      code: 'spot',
      value: 'Spot'
    }
  },
  {
    id: 3378,
    attributes: {
      code: 'route-novelty-score',
      value: 'Novelty Score'
    }
  },
  {
    id: 3396,
    attributes: {
      code: 'has-procedure-only',
      value: 'Only show reactions with procedure'
    }
  },
  {
    id: 2011,
    attributes: {
      code: 'WH-calculation-method',
      value: 'Working Hour Calculation Method'
    }
  },
  {
    id: 3528,
    attributes: {
      code: 'send-from',
      value: 'Send From'
    }
  },
  {
    id: 3519,
    attributes: {
      code: 'product-not-identified-no-materials-remaining',
      value: "Product hasn't been identified with no raw materials remaining."
    }
  },
  {
    id: 3336,
    attributes: {
      code: 'click-to-find-cas',
      value: 'Click to view CAS'
    }
  },
  {
    id: 3600,
    attributes: {
      code: 'left-eye',
      value: 'Left Eye'
    }
  },
  {
    id: 3601,
    attributes: {
      code: 'right-eye',
      value: 'Right Eye'
    }
  },
  {
    id: 3653,
    attributes: {
      code: 'estimated-completed-date',
      value: 'Estimated Completed Date'
    }
  },
  {
    id: 3655,
    attributes: {
      code: 'experiment-actual-start-time',
      value: 'Experiment Actual Start Time'
    }
  },
  {
    id: 2384,
    attributes: {
      code: 'pages.login.password.required',
      value: 'Please input your password'
    }
  },
  {
    id: 2385,
    attributes: {
      code: 'pages.login.phoneLogin.tab',
      value: 'Phone Login'
    }
  },
  {
    id: 2386,
    attributes: {
      code: 'pages.login.phoneLogin.errorMessage',
      value: 'Verification Code Error'
    }
  },
  {
    id: 2388,
    attributes: {
      code: 'pages.login.phoneNumber.required',
      value: 'Please input your phone number!'
    }
  },
  {
    id: 2389,
    attributes: {
      code: 'pages.login.phoneNumber.invalid',
      value: 'Phone number is invalid!'
    }
  },
  {
    id: 2390,
    attributes: {
      code: 'pages.login.captcha.placeholder',
      value: 'Verification Code'
    }
  },
  {
    id: 2391,
    attributes: {
      code: 'pages.login.captcha.required',
      value: 'Please input verification code!'
    }
  },
  {
    id: 2392,
    attributes: {
      code: 'pages.login.phoneLogin.getVerificationCode',
      value: 'Get Code'
    }
  },
  {
    id: 3339,
    attributes: {
      code: 'default-material-sources-for-quotation',
      value: 'Default Material Sources for Quotation'
    }
  },
  {
    id: 2003,
    attributes: {
      code: 'apply',
      value: 'Apply'
    }
  },
  {
    id: 3657,
    attributes: {
      code: 'experiment-actual-end-time',
      value: 'Experiment Actual End Time'
    }
  },
  {
    id: 3599,
    attributes: {
      code: 'usage-right',
      value: 'Right Hand'
    }
  },
  {
    id: 3598,
    attributes: {
      code: 'usage-left',
      value: 'Left Hand'
    }
  },
  {
    id: 2132,
    attributes: {
      code: 'error-detail',
      value: 'Error Detail'
    }
  },
  {
    id: 2135,
    attributes: {
      code: 'confirm-to-replace',
      value: 'Please confirm to replace'
    }
  },
  {
    id: 2196,
    attributes: {
      code: 'comment-object',
      value: 'Comment Object'
    }
  },
  {
    id: 2448,
    attributes: {
      code: 'pages.searchTable.new',
      value: 'New'
    }
  },
  {
    id: 3496,
    attributes: {
      code: 'robot-details',
      value: 'Robot Details'
    }
  },
  {
    id: 3497,
    attributes: {
      code: 'pages.experiment.label.operation',
      value: 'Action'
    }
  },
  {
    id: 3498,
    attributes: {
      code: 'details',
      value: 'Details'
    }
  },
  {
    id: 3500,
    attributes: {
      code: 'playback',
      value: 'Playback'
    }
  },
  {
    id: 3501,
    attributes: {
      code: 'call-robot',
      value: 'Call Robot'
    }
  },
  {
    id: 3503,
    attributes: {
      code: 'operation-details',
      value: 'Operation Details'
    }
  },
  {
    id: 3360,
    attributes: {
      code: 'experiment-completed',
      value: 'completed'
    }
  },
  {
    id: 3403,
    attributes: {
      code: 'cancel-reason',
      value: 'Reason'
    }
  },
  {
    id: 3402,
    attributes: {
      code: 'cancel-projects-note',
      value: 'The operation of cancelling a project can not be reversed.'
    }
  },
  {
    id: 2137,
    attributes: {
      code: 'compound-not-exist',
      value: 'No Matched Molecule'
    }
  },
  {
    id: 3504,
    attributes: {
      code: 'chat-with',
      value: 'Chat with'
    }
  },
  {
    id: 3507,
    attributes: {
      code: 'substance-inference',
      value: 'Substance Inference'
    }
  },
  {
    id: 3508,
    attributes: {
      code: 'structural',
      value: 'Structure'
    }
  },
  {
    id: 3509,
    attributes: {
      code: 'exact-mass',
      value: 'Exact Mass'
    }
  },
  {
    id: 3510,
    attributes: {
      code: 'ultra-spectra',
      value: 'Ultra Spectra'
    }
  },
  {
    id: 3511,
    attributes: {
      code: 'mass-spectra',
      value: 'Mass Spectra'
    }
  },
  {
    id: 3513,
    attributes: {
      code: 'proportion-254',
      value: 'Proportion(254nm)'
    }
  },
  {
    id: 3514,
    attributes: {
      code: 'proportion-214',
      value: 'Proportion(214nm)'
    }
  },
  {
    id: 2021,
    attributes: {
      code: 'search-setting',
      value: 'AI Retrosynthesis Settings'
    }
  },
  {
    id: 2017,
    attributes: {
      code: 'default-intermediates',
      value: 'By default, only show intermediates and key starting materials'
    }
  },
  {
    id: 2018,
    attributes: {
      code: 'default-all-route',
      value: 'By default, display the complete route'
    }
  },
  {
    id: 3362,
    attributes: {
      code: 'experiment-ongoing',
      value: 'ongoing'
    }
  },
  {
    id: 3515,
    attributes: {
      code: 'ai-inference',
      value: 'AI Inference'
    }
  },
  {
    id: 2172,
    attributes: {
      code: 'reason',
      value: 'reason'
    }
  },
  {
    id: 3516,
    attributes: {
      code: 'product-identified-materials-remaining',
      value: 'Product has been identified with some raw materials remaining.'
    }
  },
  {
    id: 3517,
    attributes: {
      code: 'product-identified-no-materials-remaining',
      value: 'Product has been identified with no raw materials remaining.'
    }
  },
  {
    id: 2058,
    attributes: {
      code: 'quantity',
      value: 'quantity'
    }
  },
  {
    id: 3602,
    attributes: {
      code: 'usage-up',
      value: 'Front'
    }
  },
  {
    id: 2059,
    attributes: {
      code: 'unit-price',
      value: 'Unit Price'
    }
  },
  {
    id: 2274,
    attributes: {
      code: 'molecules-no',
      value: 'Molecule ID'
    }
  },
  {
    id: 2353,
    attributes: {
      code: 'undo',
      value: 'Undo'
    }
  },
  {
    id: 2352,
    attributes: {
      code: 'collapse-route',
      value: 'Collapse Route'
    }
  },
  {
    id: 2364,
    attributes: {
      code: 'searches',
      value: 'Searches'
    }
  },
  {
    id: 2027,
    attributes: {
      code: 'experiment-history',
      value: 'Experiment history'
    }
  },
  {
    id: 2029,
    attributes: {
      code: 'min-cost',
      value: 'Estimated minimum cost'
    }
  },
  {
    id: 2030,
    attributes: {
      code: 'pattern-positive-number',
      value: 'Invalid cost input. Please enter a number greater than 0.'
    }
  },
  {
    id: 2031,
    attributes: {
      code: 'add-molecule-tip',
      value: 'Please Add a Molecule First'
    }
  },
  {
    id: 3364,
    attributes: {
      code: 'experiment-prepared',
      value: 'ready'
    }
  },
  {
    id: 3366,
    attributes: {
      code: 'experiment-to-be-ready',
      value: 'unready'
    }
  },
  {
    id: 3368,
    attributes: {
      code: 'experiment-exception',
      value: 'abnormal'
    }
  },
  {
    id: 3370,
    attributes: {
      code: 'experiment-pending',
      value: 'pending'
    }
  },
  {
    id: 2006,
    attributes: {
      code: 'route-list-display',
      value: 'Route List Display Style'
    }
  },
  {
    id: 2036,
    attributes: {
      code: 'confirm-to-del',
      value: 'Confirm to Delete'
    }
  },
  {
    id: 2033,
    attributes: {
      code: 'edit-molecules',
      value: 'Edit Molecules'
    }
  },
  {
    id: 2035,
    attributes: {
      code: 'new-my-reaction',
      value: 'New Reaction'
    }
  },
  {
    id: 2053,
    attributes: {
      code: 'solvent',
      value: 'Solvent'
    }
  },
  {
    id: 2054,
    attributes: {
      code: 'product',
      value: 'Product'
    }
  },
  {
    id: 2012,
    attributes: {
      code: 'FTE-unit',
      value: 'FTE Unit'
    }
  },
  {
    id: 2007,
    attributes: {
      code: 'default-yield',
      value: 'Default Yield'
    }
  },
  {
    id: 2051,
    attributes: {
      code: 'chiral-separation',
      value: 'Chiral Separation'
    }
  },
  {
    id: 2010,
    attributes: {
      code: 'Premium-coefficient',
      value: 'Premium Coefficient'
    }
  },
  {
    id: 2016,
    attributes: {
      code: 'show-yield',
      value: 'Show Yield on the Route'
    }
  },
  {
    id: 2020,
    attributes: {
      code: 'max-search-time',
      value: 'Max Design Time(min)'
    }
  },
  {
    id: 2060,
    attributes: {
      code: 'quotate-price-tip',
      value:
        'The validity of the raw material prices in the table corresponds to the marked date listed by the supplier and not the real-time prices on the suppliers website. Please visit the suppliers purchase page via the provided link to verify the prices!'
    }
  },
  {
    id: 2061,
    attributes: {
      code: 'other-weight-quotate',
      value: 'Other Weight Quotations'
    }
  },
  {
    id: 2062,
    attributes: {
      code: 'continue-edit',
      value: 'Continue editing'
    }
  },
  {
    id: 2063,
    attributes: {
      code: 'replaced-route-success',
      value: 'Successfully replaced with the chosen route'
    }
  },
  {
    id: 2233,
    attributes: {
      code: 'member',
      value: 'Member'
    }
  },
  {
    id: 2064,
    attributes: {
      code: 'try-replace-img',
      value: 'Please try to use a different image or edit on the canvas.'
    }
  },
  {
    id: 2067,
    attributes: {
      code: 'fail-convert',
      value: 'Failed to convert'
    }
  },
  {
    id: 2068,
    attributes: {
      code: 'upload-img',
      value: 'Please upload an image file in PNG or JPG format'
    }
  },
  {
    id: 2069,
    attributes: {
      code: 'select-an-img',
      value: 'Please select an image'
    }
  },
  {
    id: 2071,
    attributes: {
      code: 'choose',
      value: 'Choose'
    }
  },
  {
    id: 2072,
    attributes: {
      code: 'molecules-resynthesized',
      value:
        'Routes of the molecule marked with a dashed outline are currently being designed by AI.'
    }
  },
  {
    id: 2074,
    attributes: {
      code: 'copy-molecule-route',
      value: 'copy this molecule and its route'
    }
  },
  {
    id: 3638,
    attributes: {
      code: 'switch-to-workflow',
      value: 'Switch to Workflow'
    }
  },
  {
    id: 2076,
    attributes: {
      code: 'success-confirm-quotate',
      value: 'Successfully Confirmed the Quotation'
    }
  },
  {
    id: 2048,
    attributes: {
      code: 'route-confirm',
      value: 'Confirm submission of this route?'
    }
  },
  {
    id: 2077,
    attributes: {
      code: 'view-results',
      value: 'View Results'
    }
  },
  {
    id: 2078,
    attributes: {
      code: 'expand-route',
      value: 'Expand Route'
    }
  },
  {
    id: 2079,
    attributes: {
      code: 'confirm-quote',
      value: 'Confirm'
    }
  },
  {
    id: 2080,
    attributes: {
      code: 'man-days',
      value: 'Man-days'
    }
  },
  {
    id: 2081,
    attributes: {
      code: 'add-new-charge',
      value: 'Add a New Charge Item'
    }
  },
  {
    id: 2082,
    attributes: {
      code: 'same-quatation',
      value: 'Same Quotation'
    }
  },
  {
    id: 2083,
    attributes: {
      code: 'show-all',
      value: 'Show All'
    }
  },
  {
    id: 2070,
    attributes: {
      code: 'regioselectivity',
      value: 'Regioselectivity'
    }
  },
  {
    id: 2400,
    attributes: {
      code: 'pages.projectTable.label.name',
      value: 'Project Name'
    }
  },
  {
    id: 2401,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleConfig',
      value: 'Rule configuration'
    }
  },
  {
    id: 2402,
    attributes: {
      code: 'pages.projectTable.title',
      value: 'Project List'
    }
  },
  {
    id: 2403,
    attributes: {
      code: 'pages.searchTable.updateForm.basicConfig',
      value: 'Basic Information'
    }
  },
  {
    id: 2404,
    attributes: {
      code: 'pages.projectTable.label.status',
      value: 'Status'
    }
  },
  {
    id: 2023,
    attributes: {
      code: 'generating-tip',
      value: 'AI routes are currently being generated. Please wait a moment~'
    }
  },
  {
    id: 2005,
    attributes: {
      code: 'route-detail-display-style',
      value: 'Route Detail Display Style'
    }
  },
  {
    id: 2014,
    attributes: {
      code: 'retro_params-settings',
      value: 'Retrosynthesis Settings'
    }
  },
  {
    id: 2342,
    attributes: {
      code: 'data-loading',
      value: 'Loading...'
    }
  },
  {
    id: 2008,
    attributes: {
      code: 'Estimate-reaction',
      value: 'Estimate based on the difficulty of the reaction'
    }
  },
  {
    id: 2009,
    attributes: {
      code: 'Estimate-procedure',
      value: 'Estimate based on the procedure'
    }
  },
  {
    id: 2013,
    attributes: {
      code: 'rmb-unit',
      value: 'yuan/day'
    }
  },
  {
    id: 2024,
    attributes: {
      code: 'max-30-l',
      value: 'Limit input to 30 characters.'
    }
  },
  {
    id: 2026,
    attributes: {
      code: 'enter-select-tip',
      value: 'Please enter and select'
    }
  },
  {
    id: 2032,
    attributes: {
      code: 'creator',
      value: 'Creator'
    }
  },
  {
    id: 2034,
    attributes: {
      code: 'all-my-reactions',
      value: 'All my reactions'
    }
  },
  {
    id: 2037,
    attributes: {
      code: 'molecule-exist',
      value: 'The molecule already exists.'
    }
  },
  {
    id: 2042,
    attributes: {
      code: 'no-permission-tip',
      value:
        'You do not have permission to perform this operation. Please contact the administrator to grant access!'
    }
  },
  {
    id: 2043,
    attributes: {
      code: 'delete-quotation',
      value: 'Do you want to delete this quotation?'
    }
  },
  {
    id: 2045,
    attributes: {
      code: 'filter-routes-group',
      value: 'Click to filter routes belong to this group'
    }
  },
  {
    id: 3640,
    attributes: {
      code: 'switching',
      value: 'Switching'
    }
  },
  {
    id: 2046,
    attributes: {
      code: 'unsaved-changes-tip',
      value: 'Unsaved changes will be lost.'
    }
  },
  {
    id: 2047,
    attributes: {
      code: 'confirm-the-route',
      value: 'Confirm this route'
    }
  },
  {
    id: 2050,
    attributes: {
      code: 'previous',
      value: 'Previous'
    }
  },
  {
    id: 2052,
    attributes: {
      code: 'cant-input-negative-number',
      value: "Can't input negative number"
    }
  },
  {
    id: 2055,
    attributes: {
      code: 'other-reagent',
      value: 'Other Reagent'
    }
  },
  {
    id: 2056,
    attributes: {
      code: 'reactant',
      value: 'Reactant'
    }
  },
  {
    id: 2167,
    attributes: {
      code: 'expected-mass',
      value: 'Expected weight'
    }
  },
  {
    id: 2168,
    attributes: {
      code: 'no-routes-returned',
      value: 'No Routes Returned.'
    }
  },
  {
    id: 2169,
    attributes: {
      code: 'not-grouped',
      value: 'Not Grouped'
    }
  },
  {
    id: 2170,
    attributes: {
      code: 'algorithm-cluster',
      value: 'Algorithm Cluster'
    }
  },
  {
    id: 3521,
    attributes: {
      code: 'action-proposal-tip',
      value: 'Please select a proposal within the time limit'
    }
  },
  {
    id: 2171,
    attributes: {
      code: 'same-key-material',
      value: 'Same Key Starting Material'
    }
  },
  {
    id: 2174,
    attributes: {
      code: 'start',
      value: 'Start'
    }
  },
  {
    id: 2175,
    attributes: {
      code: 'cancel-reason',
      value: 'cancel reason'
    }
  },
  {
    id: 2176,
    attributes: {
      code: 'calculate-materials',
      value: 'calculate the Mass of Other Materials'
    }
  },
  {
    id: 2041,
    attributes: {
      code: 'success-update',
      value: 'Update Successful'
    }
  },
  {
    id: 2178,
    attributes: {
      code: 'start-time',
      value: 'start time'
    }
  },
  {
    id: 2179,
    attributes: {
      code: 'Mass',
      value: 'Weight'
    }
  },
  {
    id: 2180,
    attributes: {
      code: 'no-auth-tip',
      value:
        'You do not have permission for this project. Please contact the project administrator to add you to the project members.'
    }
  },
  {
    id: 3420,
    attributes: {
      code: 'complete-molecule',
      value: 'Complete Molecule'
    }
  },
  {
    id: 2181,
    attributes: {
      code: 'modify-reaction-tip',
      value:
        'If modifications are made to the reaction it will be necessary to recalculate the quote for the entire route.'
    }
  },
  {
    id: 2182,
    attributes: {
      code: 'open-reaction-details-tip',
      value: 'Please open Reaction Details if you want to add a reaction'
    }
  },
  {
    id: 2183,
    attributes: {
      code: 'successfully-added',
      value: 'Successfully Added'
    }
  },
  {
    id: 2184,
    attributes: {
      code: 'add-blacklist-material',
      value: 'Add Blacklist Material'
    }
  },
  {
    id: 2185,
    attributes: {
      code: 'failed-add-material-blacklist',
      value: 'Failed to add to Material Blacklist'
    }
  },
  {
    id: 2186,
    attributes: {
      code: 'add-raw-material-lib',
      value: 'Add Raw material source'
    }
  },
  {
    id: 2187,
    attributes: {
      code: 'exists-tip',
      value: 'already exists'
    }
  },
  {
    id: 2188,
    attributes: {
      code: 'at-least-add-one',
      value: 'Please at least add one'
    }
  },
  {
    id: 2189,
    attributes: {
      code: 'add-routes',
      value: 'Add Routes'
    }
  },
  {
    id: 2190,
    attributes: {
      code: 'add-to-route',
      value: 'Add to route'
    }
  },
  {
    id: 2191,
    attributes: {
      code: 'added-to-route',
      value: 'Added to route'
    }
  },
  {
    id: 2192,
    attributes: {
      code: 'add-raw-materials',
      value: 'Add Raw Materials'
    }
  },
  {
    id: 2193,
    attributes: {
      code: 'molecular-mass',
      value: 'Molecular mass'
    }
  },
  {
    id: 2194,
    attributes: {
      code: 'EWR',
      value: 'Equivalence/Weight Ratio'
    }
  },
  {
    id: 2195,
    attributes: {
      code: 'project-update-success',
      value: 'Project Status Updated Successfully'
    }
  },
  {
    id: 2140,
    attributes: {
      code: 'estimate-start',
      value: 'Estimated Start'
    }
  },
  {
    id: 2138,
    attributes: {
      code: 'try-diff-search',
      value: "We couldn't find a matching molecule, please try other methods."
    }
  },
  {
    id: 2130,
    attributes: {
      code: 'search-again',
      value: 'Submit Again'
    }
  },
  {
    id: 2131,
    attributes: {
      code: 'no-search-log',
      value: 'No task log returned.'
    }
  },
  {
    id: 2134,
    attributes: {
      code: 'search-been-created',
      value: 'The design task has been created!'
    }
  },
  {
    id: 2153,
    attributes: {
      code: 'no-AI-returned-create-II',
      value: 'to initiate a design'
    }
  },
  {
    id: 2156,
    attributes: {
      code: 'search-done',
      value: 'The design is done.'
    }
  },
  {
    id: 2157,
    attributes: {
      code: 'search-failed',
      value:
        'The design failed. Please try a different mode or increase the maximum number of steps in next search.'
    }
  },
  {
    id: 2128,
    attributes: {
      code: 'cancel-warning',
      value: 'If cancelled this operation cannnot be reversed.'
    }
  },
  {
    id: 2199,
    attributes: {
      code: 'project-name',
      value: 'Project Name'
    }
  },
  {
    id: 2200,
    attributes: {
      code: 'project-create-failed',
      value: 'Project Creation Failed'
    }
  },
  {
    id: 2201,
    attributes: {
      code: 'project-ID-duplicated',
      value: 'Project ID Duplicated!'
    }
  },
  {
    id: 2203,
    attributes: {
      code: 'experiment-ID',
      value: 'experiment ID'
    }
  },
  {
    id: 2205,
    attributes: {
      code: 'last-comment-date',
      value: 'last comment date'
    }
  },
  {
    id: 2206,
    attributes: {
      code: 'reaction-step-ID',
      value: 'reaction step ID'
    }
  },
  {
    id: 2207,
    attributes: {
      code: 'task-type',
      value: 'task type'
    }
  },
  {
    id: 2208,
    attributes: {
      code: 'experiment-design-status',
      value: 'experiment design status'
    }
  },
  {
    id: 2209,
    attributes: {
      code: 'project-status',
      value: 'Project Status'
    }
  },
  {
    id: 2210,
    attributes: {
      code: 'project-type',
      value: 'Project Type'
    }
  },
  {
    id: 2211,
    attributes: {
      code: 'project-details',
      value: 'Project Details'
    }
  },
  {
    id: 2212,
    attributes: {
      code: 'project-delivery-date',
      value: 'Due Date'
    }
  },
  {
    id: 2213,
    attributes: {
      code: 'enter-molecule',
      value: 'Please enter a molecule'
    }
  },
  {
    id: 2214,
    attributes: {
      code: 'experimental-procedure-name',
      value: 'Please enter experimental procedure name'
    }
  },
  {
    id: 2215,
    attributes: {
      code: 'positive-integer-tip',
      value: 'Please enter a positive integer.'
    }
  },
  {
    id: 2216,
    attributes: {
      code: 'project-num',
      value: 'Project Number'
    }
  },
  {
    id: 2217,
    attributes: {
      code: 'project-ID',
      value: 'Project ID'
    }
  },
  {
    id: 3642,
    attributes: {
      code: 'formulated-procedure',
      value: 'Formulated Procedure'
    }
  },
  {
    id: 2218,
    attributes: {
      code: 'enter-ex-name',
      value: 'Please enter the experiment name.'
    }
  },
  {
    id: 2219,
    attributes: {
      code: 'input-cancel-reason',
      value: 'Please enter the reason for canceling.'
    }
  },
  {
    id: 2220,
    attributes: {
      code: 'enter-reason',
      value: 'Please enter a reason'
    }
  },
  {
    id: 2221,
    attributes: {
      code: 'enter-two-decimal',
      value: 'Please enter a positive number with up to two decimal places.'
    }
  },
  {
    id: 2222,
    attributes: {
      code: 'valid-molecule-enter',
      value: 'Please enter a valid molecule.'
    }
  },
  {
    id: 2223,
    attributes: {
      code: 'no-molecule-tip',
      value: 'No molecule entered! Please resubmit after entering.'
    }
  },
  {
    id: 2224,
    attributes: {
      code: 'purity',
      value: 'Purity'
    }
  },
  {
    id: 2226,
    attributes: {
      code: 'modification-reason',
      value: 'Modification reason'
    }
  },
  {
    id: 2227,
    attributes: {
      code: 'chinese-name',
      value: 'Chinese Name'
    }
  },
  {
    id: 2228,
    attributes: {
      code: 'english-name',
      value: 'English Name'
    }
  },
  {
    id: 2229,
    attributes: {
      code: 'modification-time',
      value: 'Modification Time'
    }
  },
  {
    id: 2230,
    attributes: {
      code: 'modifier',
      value: 'Modifier'
    }
  },
  {
    id: 2155,
    attributes: {
      code: 'no-routes-create-tip',
      value:
        'No Routes Returned. Please save or confirm the existing AI routes or click "New Route" to initiate a design.'
    }
  },
  {
    id: 2606,
    attributes: {
      code: 'menu.list.project-list.detail.addMolecule',
      value: 'New Molecule'
    }
  },
  {
    id: 2105,
    attributes: {
      code: 'created-by',
      value: 'Created by'
    }
  },
  {
    id: 3384,
    attributes: {
      code: 'route-sort-dimension-weight-setting',
      value: 'Route Preference Setting'
    }
  },
  {
    id: 3422,
    attributes: {
      code: 'complete-molecule-tip',
      value: 'Confirm to complete this molecule?'
    }
  },
  {
    id: 2237,
    attributes: {
      code: 'No',
      value: 'No'
    }
  },
  {
    id: 2238,
    attributes: {
      code: 'branched-chain',
      value: 'branched chain'
    }
  },
  {
    id: 2239,
    attributes: {
      code: 'source',
      value: 'Source'
    }
  },
  {
    id: 2240,
    attributes: {
      code: 'label',
      value: 'Label'
    }
  },
  {
    id: 2241,
    attributes: {
      code: 'material-available',
      value: 'In Stock'
    }
  },
  {
    id: 2242,
    attributes: {
      code: 'material-not-available',
      value: 'Need Quotation'
    }
  },
  {
    id: 2243,
    attributes: {
      code: 'availability',
      value: 'Availability'
    }
  },
  {
    id: 2275,
    attributes: {
      code: 'molecules-status',
      value: 'Molecule Status'
    }
  },
  {
    id: 2244,
    attributes: {
      code: 'price',
      value: 'Price'
    }
  },
  {
    id: 2245,
    attributes: {
      code: 'min-price',
      value: 'Price (Minimum)'
    }
  },
  {
    id: 2246,
    attributes: {
      code: 'max-price',
      value: 'Price (Maximum)'
    }
  },
  {
    id: 2325,
    attributes: {
      code: 'raw-material-lib',
      value: 'Raw Material Source'
    }
  },
  {
    id: 2247,
    attributes: {
      code: 'unit',
      value: 'Unit'
    }
  },
  {
    id: 2248,
    attributes: {
      code: 'min-amount',
      value: 'Amount(Minimum)'
    }
  },
  {
    id: 2249,
    attributes: {
      code: 'max-amount',
      value: 'Amount(Maximum)'
    }
  },
  {
    id: 2250,
    attributes: {
      code: 'entire-material-lib',
      value: 'All Material Sources'
    }
  },
  {
    id: 2252,
    attributes: {
      code: 'save-draft',
      value: 'Save as Draft'
    }
  },
  {
    id: 2253,
    attributes: {
      code: 'yield',
      value: 'Yield'
    }
  },
  {
    id: 2254,
    attributes: {
      code: 'open-reaction-details',
      value: 'Open reaction details'
    }
  },
  {
    id: 2255,
    attributes: {
      code: 'open-route-reaction',
      value: 'View route and reaction'
    }
  },
  {
    id: 2260,
    attributes: {
      code: 'my-reaction-experimental',
      value: 'My Reactions - Experimental Design'
    }
  },
  {
    id: 2262,
    attributes: {
      code: 'copyright',
      value: 'Copyright'
    }
  },
  {
    id: 2263,
    attributes: {
      code: 'beian',
      value: 'Shanghai ICP No. 2022035008-1'
    }
  },
  {
    id: 2264,
    attributes: {
      code: 'logout',
      value: 'Sign Out'
    }
  },
  {
    id: 2265,
    attributes: {
      code: 'noticeIcon.empty',
      value: 'No data available'
    }
  },
  {
    id: 2266,
    attributes: {
      code: 'name',
      value: 'Name'
    }
  },
  {
    id: 2267,
    attributes: {
      code: 'reset',
      value: 'Reset'
    }
  },
  {
    id: 2268,
    attributes: {
      code: 'Search',
      value: 'Search'
    }
  },
  {
    id: 2270,
    attributes: {
      code: 'input-tip',
      value: 'Please enter...'
    }
  },
  {
    id: 2309,
    attributes: {
      code: 'create-project-tip-target',
      value: '[New Project]'
    }
  },
  {
    id: 2310,
    attributes: {
      code: 'create-project-tip-III',
      value: 'to start'
    }
  },
  {
    id: 2311,
    attributes: {
      code: 'gnerate-routes',
      value: 'Retro With AI'
    }
  },
  {
    id: 2315,
    attributes: {
      code: 'number-of-routes',
      value: 'Route Number'
    }
  },
  {
    id: 2317,
    attributes: {
      code: 'complete-time',
      value: 'Job End'
    }
  },
  {
    id: 2318,
    attributes: {
      code: 'parameters',
      value: 'Parameters'
    }
  },
  {
    id: 2320,
    attributes: {
      code: 'mode-selection',
      value: 'Mode Selection'
    }
  },
  {
    id: 2321,
    attributes: {
      code: 'fast-mode',
      value: 'Fast Mode'
    }
  },
  {
    id: 2322,
    attributes: {
      code: 'complex-mode',
      value: 'Optimal Mode'
    }
  },
  {
    id: 2323,
    attributes: {
      code: 'recommend-to-me',
      value: 'Recommend'
    }
  },
  {
    id: 2327,
    attributes: {
      code: 'confirm',
      value: 'Confirm'
    }
  },
  {
    id: 2328,
    attributes: {
      code: 'route-length',
      value: 'Total Steps'
    }
  },
  {
    id: 2251,
    attributes: {
      code: 'operate-success',
      value: 'Operation Successful'
    }
  },
  {
    id: 2329,
    attributes: {
      code: 'route-id',
      value: 'Route ID'
    }
  },
  {
    id: 2332,
    attributes: {
      code: 'new-group',
      value: 'Group'
    }
  },
  {
    id: 2376,
    attributes: {
      code: 'pages.Notification.clear-tasks',
      value: 'Clear Completed Tasks'
    }
  },
  {
    id: 2333,
    attributes: {
      code: 'group',
      value: 'Group'
    }
  },
  {
    id: 2334,
    attributes: {
      code: 'favorite',
      value: 'Favorite'
    }
  },
  {
    id: 2151,
    attributes: {
      code: 'experiment-design',
      value: 'Experiment Design'
    }
  },
  {
    id: 2152,
    attributes: {
      code: 'no-AI-returned-create-I',
      value: 'No Routes Returned. Please click'
    }
  },
  {
    id: 2335,
    attributes: {
      code: 'comment',
      value: 'Comment'
    }
  },
  {
    id: 2336,
    attributes: {
      code: 'multi-steps-tip',
      value:
        'The total number of steps in the route with the shortest branched chain steps'
    }
  },
  {
    id: 2306,
    attributes: {
      code: 'proceeded',
      value: 'proceeded'
    }
  },
  {
    id: 2337,
    attributes: {
      code: 'intel-recommend',
      value: 'Intelligent Recommendation'
    }
  },
  {
    id: 2338,
    attributes: {
      code: 'all-routes',
      value: 'All Routes'
    }
  },
  {
    id: 2177,
    attributes: {
      code: 'start-time-limit',
      value: 'start time limit'
    }
  },
  {
    id: 2339,
    attributes: {
      code: 'default-route',
      value: 'Default Route'
    }
  },
  {
    id: 2340,
    attributes: {
      code: 'default-route-set',
      value: 'Default Route Set Successfully'
    }
  },
  {
    id: 2341,
    attributes: {
      code: 'new-route',
      value: 'New Route'
    }
  },
  {
    id: 2343,
    attributes: {
      code: 'copy-to-clipboard',
      value: 'Copied to Clipboard'
    }
  },
  {
    id: 2344,
    attributes: {
      code: 'export',
      value: 'Export'
    }
  },
  {
    id: 2349,
    attributes: {
      code: 'fetch-target-molecule',
      value: 'Retro with AI using this molecule as the target'
    }
  },
  {
    id: 2350,
    attributes: {
      code: 'del-molecule-routes',
      value: 'Delete this molecule and its synthetic route'
    }
  },
  {
    id: 2298,
    attributes: {
      code: 'select-a-document',
      value: 'Select a File'
    }
  },
  {
    id: 2312,
    attributes: {
      code: 'results',
      value: 'Design Records'
    }
  },
  {
    id: 2313,
    attributes: {
      code: 'success-results',
      value: 'Only Show Successful Results'
    }
  },
  {
    id: 2354,
    attributes: {
      code: 'redo',
      value: 'Redo'
    }
  },
  {
    id: 2357,
    attributes: {
      code: 'view-materials',
      value: 'View Materials'
    }
  },
  {
    id: 3412,
    attributes: {
      code: 'start-project',
      value: 'Start Project'
    }
  },
  {
    id: 3414,
    attributes: {
      code: 'start-project-tip',
      value: 'Confirm to start this project?'
    }
  },
  {
    id: 3416,
    attributes: {
      code: 'cancel-molecule',
      value: 'Cancel Molecule'
    }
  },
  {
    id: 3418,
    attributes: {
      code: 'cancel-molecule-tip',
      value: 'The operation of cancelling a molecule can not be reversed.'
    }
  },
  {
    id: 2114,
    attributes: {
      code: 'draft',
      value: 'Draft'
    }
  },
  {
    id: 2358,
    attributes: {
      code: 'reaction-lib',
      value: 'Reaction Library'
    }
  },
  {
    id: 2359,
    attributes: {
      code: 'prev-step',
      value: 'Previous Step'
    }
  },
  {
    id: 2360,
    attributes: {
      code: 'next-step',
      value: 'Next Step'
    }
  },
  {
    id: 2361,
    attributes: {
      code: 'from',
      value: 'From'
    }
  },
  {
    id: 2405,
    attributes: {
      code: 'pages.projectTable.label.no',
      value: 'Project ID'
    }
  },
  {
    id: 2406,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleName.nameLabel',
      value: 'Rule Name'
    }
  },
  {
    id: 2407,
    attributes: {
      code: 'pages.project.settings',
      value: 'Settings'
    }
  },
  {
    id: 2408,
    attributes: {
      code: 'pages.projectTable.label.customer',
      value: 'Client'
    }
  },
  {
    id: 2409,
    attributes: {
      code: 'pages.projectTable.label.type',
      value: 'Type'
    }
  },
  {
    id: 2410,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleName.nameRules',
      value: 'Please enter the rule name!'
    }
  },
  {
    id: 2411,
    attributes: {
      code: 'pages.projectTable.label.compoundNumber',
      value: 'Molecule Count'
    }
  },
  {
    id: 2412,
    attributes: {
      code: 'pages.projectTable.label.pm',
      value: 'Project Manager'
    }
  },
  {
    id: 2413,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleDesc.descLabel',
      value: 'Rule Description'
    }
  },
  {
    id: 2173,
    attributes: {
      code: 'reason-type',
      value: 'Reason Type'
    }
  },
  {
    id: 2414,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleDesc.descPlaceholder',
      value: 'Please enter at least five characters'
    }
  },
  {
    id: 2416,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleDesc.descRules',
      value: 'Please enter a rule description of at least five characters!'
    }
  },
  {
    id: 2417,
    attributes: {
      code: 'pages.projectTable.typeValue.fte',
      value: 'FTE'
    }
  },
  {
    id: 2418,
    attributes: {
      code: 'pages.projectTable.typeValue.ffs',
      value: 'FFS'
    }
  },
  {
    id: 2419,
    attributes: {
      code: 'pages.projectTable.statusLabel.created',
      value: 'To be designed'
    }
  },
  {
    id: 2420,
    attributes: {
      code: 'pages.projectTable.statusLabel.started',
      value: 'In progress'
    }
  },
  {
    id: 3644,
    attributes: {
      code: 'traditional-mode',
      value: 'Traditional Mode'
    }
  },
  {
    id: 3646,
    attributes: {
      code: 'auto-mode',
      value: 'Auto Mode'
    }
  },
  {
    id: 3647,
    attributes: {
      code: 'generate',
      value: 'Generate'
    }
  },
  {
    id: 2356,
    attributes: {
      code: 'recommended-reactions',
      value: 'AI-designed Reactions'
    }
  },
  {
    id: 2421,
    attributes: {
      code: 'pages.projectTable.statusLabel.holding',
      value: 'Paused'
    }
  },
  {
    id: 2422,
    attributes: {
      code: 'pages.projectTable.statusLabel.cancelled',
      value: 'Cancelled'
    }
  },
  {
    id: 2423,
    attributes: {
      code: 'pages.projectTable.statusLabel.finished',
      value: 'Finshied'
    }
  },
  {
    id: 2424,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleProps.title',
      value: 'Configure Properties'
    }
  },
  {
    id: 2425,
    attributes: {
      code: 'pages.searchTable.updateForm.object',
      value: 'Monitoring Object'
    }
  },
  {
    id: 2426,
    attributes: {
      code: 'pages.projectTable.actionLabel.config',
      value: 'Settings'
    }
  },
  {
    id: 2427,
    attributes: {
      code: 'pages.projectTable.actionLabel.viewDetail',
      value: 'View'
    }
  },
  {
    id: 2428,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleProps.templateLabel',
      value: 'Rule Template'
    }
  },
  {
    id: 2429,
    attributes: {
      code: 'pages.searchTable.updateForm.ruleProps.typeLabel',
      value: 'Rule Type'
    }
  },
  {
    id: 2430,
    attributes: {
      code: 'pages.projectTable.modelLabel.newProject',
      value: 'New Project'
    }
  },
  {
    id: 2431,
    attributes: {
      code: 'pages.searchTable.updateForm.schedulingPeriod.title',
      value: 'Set Scheduling Period'
    }
  },
  {
    id: 2432,
    attributes: {
      code: 'pages.searchTable.updateForm.schedulingPeriod.timeLabel',
      value: 'Job Start'
    }
  },
  {
    id: 2433,
    attributes: {
      code: 'pages.searchTable.updateForm.schedulingPeriod.timeRules',
      value: 'Please choose a start time!'
    }
  },
  {
    id: 2434,
    attributes: {
      code: 'pages.searchTable.titleDesc',
      value: 'Description'
    }
  },
  {
    id: 2436,
    attributes: {
      code: 'pages.searchTable.titleCallNo',
      value: 'Number of Service Calls'
    }
  },
  {
    id: 2439,
    attributes: {
      code: 'pages.projectTable.typeValue.personal',
      value: 'Personal'
    }
  },
  {
    id: 2440,
    attributes: {
      code: 'pages.searchTable.nameStatus.running',
      value: 'running'
    }
  },
  {
    id: 2441,
    attributes: {
      code: 'pages.searchTable.nameStatus.online',
      value: 'online'
    }
  },
  {
    id: 2442,
    attributes: {
      code: 'pages.searchTable.nameStatus.abnormal',
      value: 'abnormal'
    }
  },
  {
    id: 2443,
    attributes: {
      code: 'pages.searchTable.titleUpdatedAt',
      value: 'Last Scheduled at'
    }
  },
  {
    id: 2444,
    attributes: {
      code: 'pages.searchTable.exception',
      value: 'Please enter the reason for the exception!'
    }
  },
  {
    id: 2445,
    attributes: {
      code: 'pages.searchTable.config',
      value: 'Configuration'
    }
  },
  {
    id: 2446,
    attributes: {
      code: 'pages.searchTable.subscribeAlert',
      value: 'Subscribe to alerts'
    }
  },
  {
    id: 2447,
    attributes: {
      code: 'pages.searchTable.title',
      value: 'Enquiry Form'
    }
  },
  {
    id: 2450,
    attributes: {
      code: 'pages.searchTable.item',
      value: 'item'
    }
  },
  {
    id: 2508,
    attributes: {
      code: 'pages.experiment.statusLabel.hold',
      value: 'Hold'
    }
  },
  {
    id: 2509,
    attributes: {
      code: 'pages.experiment.statusLabel.canceled',
      value: 'Canceled'
    }
  },
  {
    id: 2510,
    attributes: {
      code: 'pages.experiment.statusLabel.failed',
      value: 'Failed'
    }
  },
  {
    id: 2511,
    attributes: {
      code: 'pages.experiment.statusLabel.success',
      value: 'Success'
    }
  },
  {
    id: 2512,
    attributes: {
      code: 'pages.experiment.statusLabel.completed',
      value: 'Finshied'
    }
  },
  {
    id: 2514,
    attributes: {
      code: 'pages.experiment.check.statusLabel.checking',
      value: 'Checking'
    }
  },
  {
    id: 2515,
    attributes: {
      code: 'pages.experiment.check.statusLabel.canceled',
      value: 'Canceled'
    }
  },
  {
    id: 2519,
    attributes: {
      code: 'pages.experimentDesign.label.scale_up',
      value: 'Scale Up'
    }
  },
  {
    id: 2520,
    attributes: {
      code: 'pages.experimentDesign.label.auto',
      value: 'Auto'
    }
  },
  {
    id: 2521,
    attributes: {
      code: 'pages.experimentDesign.label.asap',
      value: 'Asap'
    }
  },
  {
    id: 2522,
    attributes: {
      code: 'pages.experimentDesign.label.custom',
      value: 'Custom'
    }
  },
  {
    id: 2523,
    attributes: {
      code: 'pages.route.edit.label.route',
      value: 'Route'
    }
  },
  {
    id: 2524,
    attributes: {
      code: 'pages.route.edit.label.save',
      value: 'Save'
    }
  },
  {
    id: 2525,
    attributes: {
      code: 'pages.route.edit.label.confirm',
      value: 'Confirm'
    }
  },
  {
    id: 2526,
    attributes: {
      code: 'pages.experiment.conclusion.label.phase.solid',
      value: 'Solid'
    }
  },
  {
    id: 2527,
    attributes: {
      code: 'pages.experiment.conclusion.label.phase.liquid',
      value: 'Liquid'
    }
  },
  {
    id: 2529,
    attributes: {
      code: 'ID-contains-spaces',
      value: 'ID Contains Spaces'
    }
  },
  {
    id: 2530,
    attributes: {
      code: 'ID-empty-tip',
      value: 'ID is Empty'
    }
  },
  {
    id: 2531,
    attributes: {
      code: 'ID-exists-tip',
      value: 'ID Already Exists Current Changes Have Not Taken Effect'
    }
  },
  {
    id: 2532,
    attributes: {
      code: 'enter-ID-tip',
      value: 'Please enter the ID'
    }
  },
  {
    id: 2533,
    attributes: {
      code: 'experiment-procedure-ID',
      value: 'Experiment Procedure ID'
    }
  },
  {
    id: 2535,
    attributes: {
      code: 'ENDEVENT',
      value: 'Create EndEvent'
    }
  },
  {
    id: 2536,
    attributes: {
      code: 'Dilute',
      value: 'Dilute'
    }
  },
  {
    id: 2537,
    attributes: {
      code: 'Crystal',
      value: 'Crystal'
    }
  },
  {
    id: 2541,
    attributes: {
      code: 'DrySolution',
      value: 'DrySolution'
    }
  },
  {
    id: 2542,
    attributes: {
      code: 'Exsolution',
      value: 'Exsolution'
    }
  },
  {
    id: 2543,
    attributes: {
      code: 'Extract',
      value: 'Extract'
    }
  },
  {
    id: 2544,
    attributes: {
      code: 'Filtration',
      value: 'Filtration'
    }
  },
  {
    id: 2545,
    attributes: {
      code: 'GetMaterial',
      value: 'GetMaterial'
    }
  },
  {
    id: 2546,
    attributes: {
      code: 'ParallelReaction',
      value: 'ParallelReaction'
    }
  },
  {
    id: 2547,
    attributes: {
      code: 'AutoMix',
      value: 'AutoMix'
    }
  },
  {
    id: 2548,
    attributes: {
      code: 'MakeIngredients',
      value: 'MakeIngredients'
    }
  },
  {
    id: 2549,
    attributes: {
      code: 'MicroWaveReaction',
      value: 'MicroWaveReaction'
    }
  },
  {
    id: 2550,
    attributes: {
      code: 'Mix',
      value: 'Mix'
    }
  },
  {
    id: 2551,
    attributes: {
      code: 'PhaseSeparate',
      value: 'PhaseSeparate'
    }
  },
  {
    id: 2552,
    attributes: {
      code: 'Purify',
      value: 'Purify'
    }
  },
  {
    id: 3649,
    attributes: {
      code: 'substance-name',
      value: 'Substance Name'
    }
  },
  {
    id: 2204,
    attributes: {
      code: 'material-ID',
      value: 'Material ID'
    }
  },
  {
    id: 2553,
    attributes: {
      code: 'Quench',
      value: 'Quench'
    }
  },
  {
    id: 2554,
    attributes: {
      code: 'Recrystal',
      value: 'Recrystal'
    }
  },
  {
    id: 2556,
    attributes: {
      code: 'ReturnMaterial',
      value: 'ReturnMaterial'
    }
  },
  {
    id: 2557,
    attributes: {
      code: 'SetupReaction',
      value: 'SetupReaction'
    }
  },
  {
    id: 2559,
    attributes: {
      code: 'Triturate',
      value: 'Triturate'
    }
  },
  {
    id: 2560,
    attributes: {
      code: 'WeighProduct',
      value: 'WeighProduct'
    }
  },
  {
    id: 2561,
    attributes: {
      code: 'MASTER_TOOL',
      value: 'Master tool'
    }
  },
  {
    id: 2562,
    attributes: {
      code: 'TASK_ITEM',
      value: 'Task frame'
    }
  },
  {
    id: 2563,
    attributes: {
      code: 'ACTIVATE_HAND',
      value: 'Activate the hand tool'
    }
  },
  {
    id: 2564,
    attributes: {
      code: 'ACTIVATE_LASSO',
      value: 'Activate the lasso tool'
    }
  },
  {
    id: 2565,
    attributes: {
      code: 'AGCT',
      value: 'Activate the global connect tool'
    }
  },
  {
    id: 2566,
    attributes: {
      code: 'TOOLS',
      value: 'Tools'
    }
  },
  {
    id: 2568,
    attributes: {
      code: 'ACRST',
      value: 'Activate the create/remove space tool'
    }
  },
  {
    id: 2569,
    attributes: {
      code: 'CUDIA',
      value: 'Connect using DataInputAssociation'
    }
  },
  {
    id: 2570,
    attributes: {
      code: 'REMOVE',
      value: 'Remove'
    }
  },
  {
    id: 2580,
    attributes: {
      code: 'component.notification.statusValue.pending',
      value: 'Queuing'
    }
  },
  {
    id: 2581,
    attributes: {
      code: 'component.notification.statusValue.success',
      value: 'Completed'
    }
  },
  {
    id: 2582,
    attributes: {
      code: 'component.notification.statusValue.failed',
      value: 'Failed'
    }
  },
  {
    id: 2586,
    attributes: {
      code: 'menu.exception.403',
      value: '403'
    }
  },
  {
    id: 2587,
    attributes: {
      code: 'menu.exception.404',
      value: '404'
    }
  },
  {
    id: 2588,
    attributes: {
      code: 'menu.exception.500',
      value: '500'
    }
  },
  {
    id: 2590,
    attributes: {
      code: 'menu.list.playground.commend',
      value: 'Comments Review'
    }
  },
  {
    id: 2591,
    attributes: {
      code: 'menu.list.playground.viewByBackbone',
      value: 'Routes Review'
    }
  },
  {
    id: 2594,
    attributes: {
      code: 'menu.list.workspace.myWorkbench',
      value: 'My workbench'
    }
  },
  {
    id: 2595,
    attributes: {
      code: 'menu.list.workspace.myCompound',
      value: 'My Molecules'
    }
  },
  {
    id: 2596,
    attributes: {
      code: 'menu.list.workspace.myReaction',
      value: 'My reactions'
    }
  },
  {
    id: 2600,
    attributes: {
      code: 'menu.list.project-list.detail.reaction',
      value: 'Reaction Details'
    }
  },
  {
    id: 2601,
    attributes: {
      code: 'menu.list.project-list.detail.reaction.execute.detail',
      value: 'Experiment Details'
    }
  },
  {
    id: 2602,
    attributes: {
      code: 'menu.list.project-list.detail.experimentalProcedure',
      value: 'Experiment Design Details'
    }
  },
  {
    id: 2603,
    attributes: {
      code: 'menu.list.project-list.detail.experiment-conclusion',
      value: 'Experiment Conclusions'
    }
  },
  {
    id: 2583,
    attributes: {
      code: 'pages.projectTable.statusLabel.cancelled',
      value: 'Canceled'
    }
  },
  {
    id: 2585,
    attributes: {
      code: 'menu.dashboard',
      value: 'Dashboard'
    }
  },
  {
    id: 2607,
    attributes: {
      code: 'add-molecule-in-route-tip',
      value: 'Add a Molecule'
    }
  },
  {
    id: 2608,
    attributes: {
      code: 'menu.list.project-list.detail.addMolecule.tip',
      value: 'Please Add a Molecule First'
    }
  },
  {
    id: 2609,
    attributes: {
      code: 'menu.list.project-list.detail.quoteDetail',
      value: 'Quotation Details'
    }
  },
  {
    id: 2610,
    attributes: {
      code: 'menu.list.project-list.detail.addQuote',
      value: 'Create a Quotation'
    }
  },
  {
    id: 2611,
    attributes: {
      code: 'menu.list.project-list.detail.compound',
      value: 'Molecule Details'
    }
  },
  {
    id: 2612,
    attributes: {
      code: 'menu.list.project-list.detail.compound.detail',
      value: 'Molecule Details'
    }
  },
  {
    id: 2613,
    attributes: {
      code: 'menu.list.project-list.detail.compound.detail.create',
      value: 'Create route'
    }
  },
  {
    id: 2614,
    attributes: {
      code: 'menu.list.project-list.detail.compound.viewByBackbone',
      value: 'Routes Review'
    }
  },
  {
    id: 2615,
    attributes: {
      code: 'menu.list.project-list.detail.compound.edit',
      value: 'Edit Route'
    }
  },
  {
    id: 2616,
    attributes: {
      code: 'menu.list.reaction',
      value: 'Reaction'
    }
  },
  {
    id: 2617,
    attributes: {
      code: 'menu.list.reaction.detail',
      value: 'Reaction Detail'
    }
  },
  {
    id: 2618,
    attributes: {
      code: 'menu.list.route',
      value: 'Route'
    }
  },
  {
    id: 2619,
    attributes: {
      code: 'menu.list.route.view',
      value: 'Routes Review'
    }
  },
  {
    id: 2620,
    attributes: {
      code: 'menu.list.route.viewByBackbone',
      value: 'Routes Review'
    }
  },
  {
    id: 2280,
    attributes: {
      code: 'target-molecules',
      value: 'Target'
    }
  },
  {
    id: 2094,
    attributes: {
      code: 'cost',
      value: 'Cost'
    }
  },
  {
    id: 2232,
    attributes: {
      code: 'project-molecule-id',
      value: 'Project Molecule ID'
    }
  },
  {
    id: 2235,
    attributes: {
      code: 'has',
      value: 'With'
    }
  },
  {
    id: 2236,
    attributes: {
      code: 'multiple',
      value: 'Multiple'
    }
  },
  {
    id: 2355,
    attributes: {
      code: 'reaction-search',
      value: 'Reaction Records'
    }
  },
  {
    id: 2314,
    attributes: {
      code: 'owner',
      value: 'Owner'
    }
  },
  {
    id: 3659,
    attributes: {
      code: 'summary',
      value: 'Summary'
    }
  },
  {
    id: 3661,
    attributes: {
      code: 'product-color',
      value: 'Product Color'
    }
  },
  {
    id: 3663,
    attributes: {
      code: 'declaration',
      value: 'Declaration'
    }
  },
  {
    id: 3664,
    attributes: {
      code: 'property',
      value: 'Property'
    }
  },
  {
    id: 2387,
    attributes: {
      code: 'pages.login.phoneNumber.placeholder',
      value: 'Phone Number'
    }
  },
  {
    id: 2449,
    attributes: {
      code: 'pages.searchTable.chosen',
      value: 'chosen'
    }
  },
  {
    id: 2589,
    attributes: {
      code: 'menu.list.playground',
      value: 'Internal Functions'
    }
  },
  {
    id: 2661,
    attributes: {
      code: 'app.pwa.offline',
      value: 'You are offline now.'
    }
  },
  {
    id: 2098,
    attributes: {
      code: 'weight',
      value: 'Weight'
    }
  },
  {
    id: 3487,
    attributes: {
      code: 'lab-site',
      value: 'Lab Site'
    }
  },
  {
    id: 2539,
    attributes: {
      code: 'Dry',
      value: 'Dry'
    }
  },
  {
    id: 2625,
    attributes: {
      code: 'menu.list.material-manage.storage',
      value: 'Raw Materials Management'
    }
  },
  {
    id: 2093,
    attributes: {
      code: 'quotation',
      value: 'Quotation'
    }
  },
  {
    id: 2197,
    attributes: {
      code: 'created-succeed',
      value: ' was created successfully'
    }
  },
  {
    id: 2271,
    attributes: {
      code: 'select-tip',
      value: 'Please select...'
    }
  },
  {
    id: 2272,
    attributes: {
      code: 'is-required',
      value: 'is required!'
    }
  },
  {
    id: 2273,
    attributes: {
      code: 'molecules',
      value: 'Molecules'
    }
  },
  {
    id: 2540,
    attributes: {
      code: 'PH',
      value: 'PH'
    }
  },
  {
    id: 2234,
    attributes: {
      code: 'confirmed-route',
      value: 'Confirmed Route'
    }
  },
  {
    id: 2276,
    attributes: {
      code: 'molecules-status.created',
      value: 'To be designed'
    }
  },
  {
    id: 2277,
    attributes: {
      code: 'molecules-status.designing',
      value: 'Designing'
    }
  },
  {
    id: 2281,
    attributes: {
      code: 'last-update-time',
      value: 'Last Update Time'
    }
  },
  {
    id: 2282,
    attributes: {
      code: 'key-intermediate',
      value: 'Key Intermediate'
    }
  },
  {
    id: 2285,
    attributes: {
      code: 'smiles-recognize',
      value:
        'Structural Formula Recognition (Supports jpg/png Image Size Not Exceeding 1MB)'
    }
  },
  {
    id: 2301,
    attributes: {
      code: 'reaction-no',
      value: 'Reaction ID'
    }
  },
  {
    id: 2302,
    attributes: {
      code: 'nums-of-my-reactions',
      value: 'Number of My Reactions'
    }
  },
  {
    id: 2303,
    attributes: {
      code: 'nums-of-my-experiments',
      value: 'Number of My Experiments'
    }
  },
  {
    id: 3667,
    attributes: {
      code: 'i',
      value: 'I'
    }
  },
  {
    id: 2118,
    attributes: {
      code: 'total',
      value: 'Total'
    }
  },
  {
    id: 2119,
    attributes: {
      code: 'commend',
      value: 'Commend'
    }
  },
  {
    id: 2121,
    attributes: {
      code: 'Filter',
      value: 'Filter'
    }
  },
  {
    id: 3529,
    attributes: {
      code: 'countdown',
      value: 'Countdown:'
    }
  },
  {
    id: 2365,
    attributes: {
      code: 'reported',
      value: 'Reported'
    }
  },
  {
    id: 2366,
    attributes: {
      code: 'deselected',
      value: 'Deselected'
    }
  },
  {
    id: 3512,
    attributes: {
      code: 'role',
      value: 'Role'
    }
  },
  {
    id: 3505,
    attributes: {
      code: 'video-list',
      value: 'Video List'
    }
  },
  {
    id: 3506,
    attributes: {
      code: 'report',
      value: 'Report'
    }
  },
  {
    id: 2154,
    attributes: {
      code: 'no-AI-returned',
      value: 'No AI Routes Returned.'
    }
  },
  {
    id: 2159,
    attributes: {
      code: 'select-all',
      value: 'Select All'
    }
  },
  {
    id: 2019,
    attributes: {
      code: 'route-display-style',
      value: 'Route Preference and Display'
    }
  },
  {
    id: 2022,
    attributes: {
      code: 'update-confirm-tip',
      value: 'Confirm to Update'
    }
  },
  {
    id: 2198,
    attributes: {
      code: 'project',
      value: 'Project'
    }
  },
  {
    id: 2319,
    attributes: {
      code: 'log',
      value: 'Log'
    }
  },
  {
    id: 2261,
    attributes: {
      code: 'company',
      value: 'C12.ai'
    }
  },
  {
    id: 2286,
    attributes: {
      code: 'CAS-name-tip',
      value: 'CAS/Name'
    }
  },
  {
    id: 2380,
    attributes: {
      code: 'pages.login.accountLogin.errorMessage',
      value: 'Incorrect username/password(admin/ant.design)'
    }
  },
  {
    id: 2393,
    attributes: {
      code: 'pages.getCaptchaSecondText',
      value: 'sec(s)'
    }
  },
  {
    id: 2256,
    attributes: {
      code: 'reaction-details',
      value: 'Reaction Details'
    }
  },
  {
    id: 2257,
    attributes: {
      code: 'cite',
      value: 'Cite'
    }
  },
  {
    id: 2258,
    attributes: {
      code: 'same-reaction',
      value: 'Same Reaction'
    }
  },
  {
    id: 2259,
    attributes: {
      code: 'generated-reaction',
      value: 'Generated Reaction'
    }
  },
  {
    id: 2397,
    attributes: {
      code: 'pages.welcome.link',
      value: 'Welcome'
    }
  },
  {
    id: 2513,
    attributes: {
      code: 'pages.experiment.check.statusLabel.todo',
      value: 'To do'
    }
  },
  {
    id: 2451,
    attributes: {
      code: 'pages.searchTable.totalServiceCalls',
      value: 'Total Number of Service Calls'
    }
  },
  {
    id: 2592,
    attributes: {
      code: 'menu.list.playground.AISandbox',
      value: 'AI Sandbox'
    }
  },
  {
    id: 2633,
    attributes: {
      code: 'menu.list.experiment.execute.conclusion',
      value: 'Experiment Conclusion'
    }
  },
  {
    id: 2634,
    attributes: {
      code: 'menu.list.table-list',
      value: 'Search Table'
    }
  },
  {
    id: 2635,
    attributes: {
      code: 'menu.list.basic-list',
      value: 'Basic List'
    }
  },
  {
    id: 2636,
    attributes: {
      code: 'menu.list.card-list',
      value: 'Card List'
    }
  },
  {
    id: 2637,
    attributes: {
      code: 'menu.list.search-list',
      value: 'Search List'
    }
  },
  {
    id: 2638,
    attributes: {
      code: 'menu.list.search-list.articles',
      value: 'Search List(articles)'
    }
  },
  {
    id: 2639,
    attributes: {
      code: 'menu.list.search-list.projects',
      value: 'Search List(projects)'
    }
  },
  {
    id: 2640,
    attributes: {
      code: 'menu.list.search-list.applications',
      value: 'Search List(applications)'
    }
  },
  {
    id: 2641,
    attributes: {
      code: 'menu.list.dashboard',
      value: 'Project Tracking'
    }
  },
  {
    id: 2642,
    attributes: {
      code: 'menu.list.experimental-zone',
      value: 'Playground'
    }
  },
  {
    id: 2643,
    attributes: {
      code: 'menu.list.experimental-zone.search',
      value: 'Retro with AI'
    }
  },
  {
    id: 2644,
    attributes: {
      code: 'menu.profile',
      value: 'Profile'
    }
  },
  {
    id: 2225,
    attributes: {
      code: 'ID',
      value: 'id'
    }
  },
  {
    id: 2648,
    attributes: {
      code: 'menu.result.success',
      value: 'successfully'
    }
  },
  {
    id: 2304,
    attributes: {
      code: 'associated-routes',
      value: 'Associated Routes'
    }
  },
  {
    id: 2231,
    attributes: {
      code: 'show-materials',
      value: 'Intermediate'
    }
  },
  {
    id: 2284,
    attributes: {
      code: 'myRoutes',
      value: 'My Routes'
    }
  },
  {
    id: 2316,
    attributes: {
      code: 'creation-time',
      value: 'Job Submit'
    }
  },
  {
    id: 2326,
    attributes: {
      code: 'advanced-settings',
      value: 'Advanced Settings'
    }
  },
  {
    id: 2368,
    attributes: {
      code: 'structural',
      value: 'Structure'
    }
  },
  {
    id: 2663,
    attributes: {
      code: 'app.pwa.serviceworker.updated.hint',
      value: 'Please press the "Refresh" button to reload current page'
    }
  },
  {
    id: 2664,
    attributes: {
      code: 'app.pwa.serviceworker.updated.ok',
      value: 'Refresh'
    }
  },
  {
    id: 2660,
    attributes: {
      code: 'menu.editor.koni',
      value: 'Koni Editor'
    }
  },
  {
    id: 2651,
    attributes: {
      code: 'menu.exception.not-permission',
      value: '403'
    }
  },
  {
    id: 2652,
    attributes: {
      code: 'menu.exception.not-find',
      value: '404'
    }
  },
  {
    id: 2653,
    attributes: {
      code: 'menu.exception.server-error',
      value: '500'
    }
  },
  {
    id: 2654,
    attributes: {
      code: 'menu.exception.trigger',
      value: 'Trigger'
    }
  },
  {
    id: 2655,
    attributes: {
      code: 'menu.account',
      value: 'Account'
    }
  },
  {
    id: 2656,
    attributes: {
      code: 'menu.account.center',
      value: 'Account Center'
    }
  },
  {
    id: 2657,
    attributes: {
      code: 'menu.account.settings',
      value: 'Settings'
    }
  },
  {
    id: 2658,
    attributes: {
      code: 'menu.account.trigger',
      value: 'Trigger Error'
    }
  },
  {
    id: 2659,
    attributes: {
      code: 'menu.editor.mind',
      value: 'Mind Editor'
    }
  },
  {
    id: 2662,
    attributes: {
      code: 'app.pwa.serviceworker.updated',
      value: 'New content is available'
    }
  },
  {
    id: 2645,
    attributes: {
      code: 'menu.profile.basic',
      value: 'Basic Profile'
    }
  },
  {
    id: 2646,
    attributes: {
      code: 'menu.profile.advanced',
      value: 'Advanced Profile'
    }
  },
  {
    id: 2647,
    attributes: {
      code: 'menu.result',
      value: 'Result'
    }
  },
  {
    id: 2649,
    attributes: {
      code: 'menu.result.fail',
      value: 'Fail'
    }
  },
  {
    id: 2650,
    attributes: {
      code: 'menu.exception',
      value: 'Exception'
    }
  },
  {
    id: 2369,
    attributes: {
      code: 'amount',
      value: 'Amount'
    }
  },
  {
    id: 2370,
    attributes: {
      code: 'supplier',
      value: 'Supplier'
    }
  },
  {
    id: 2371,
    attributes: {
      code: 'version',
      value: 'Version'
    }
  },
  {
    id: 2372,
    attributes: {
      code: 'reference',
      value: 'Reference'
    }
  },
  {
    id: 2373,
    attributes: {
      code: 'materials-not-available',
      value: 'Materials Not Available or Too Expensive'
    }
  },
  {
    id: 2374,
    attributes: {
      code: 'routes-citing',
      value: 'Routes Citing This Reaction'
    }
  },
  {
    id: 2375,
    attributes: {
      code: 'pages.Notification.task',
      value: 'Task'
    }
  },
  {
    id: 2377,
    attributes: {
      code: 'pages.Notification.task-no',
      value: 'Task No'
    }
  },
  {
    id: 2378,
    attributes: {
      code: 'pages.my-workbench.todo-list',
      value: 'To Do List'
    }
  },
  {
    id: 2379,
    attributes: {
      code: 'pages.layouts.userLayout.title',
      value: 'Build AI-Native R&D Labs'
    }
  },
  {
    id: 2351,
    attributes: {
      code: 'add-intermediate',
      value: 'Add Intermediate'
    }
  },
  {
    id: 2330,
    attributes: {
      code: 'algorithmic-score',
      value: 'Smart Scoring:'
    }
  },
  {
    id: 2324,
    attributes: {
      code: 'longest-chain-l',
      value: 'Length of Longest Chain:'
    }
  },
  {
    id: 2331,
    attributes: {
      code: 'known-reaction-proportion',
      value: 'Proportion of Known Reactions:'
    }
  },
  {
    id: 2346,
    attributes: {
      code: 'copy-reaction',
      value: 'Copy reaction smiles'
    }
  },
  {
    id: 2345,
    attributes: {
      code: 'view-reaction-lib',
      value: 'View reaction details'
    }
  },
  {
    id: 2348,
    attributes: {
      code: 'copy-molecule',
      value: 'Copy molecule smiles'
    }
  },
  {
    id: 2129,
    attributes: {
      code: 'tasks-in-queue',
      value: 'Tasks in Queue'
    }
  },
  {
    id: 3495,
    attributes: {
      code: 'task-in-progress',
      value: 'Task In Progress'
    }
  },
  {
    id: 2066,
    attributes: {
      code: 'choose-route',
      value: 'Choose Route'
    }
  },
  {
    id: 2202,
    attributes: {
      code: 'reaction-ID',
      value: 'reaction ID'
    }
  },
  {
    id: 2437,
    attributes: {
      code: 'pages.searchTable.titleStatus',
      value: 'Status'
    }
  },
  {
    id: 2438,
    attributes: {
      code: 'pages.searchTable.nameStatus.default',
      value: 'default'
    }
  },
  {
    id: 2622,
    attributes: {
      code: 'menu.list.experiment.plan',
      value: 'Experiment Plan'
    }
  },
  {
    id: 2623,
    attributes: {
      code: 'menu.list.experiment',
      value: 'Experiment'
    }
  },
  {
    id: 2624,
    attributes: {
      code: 'menu.list.material-manage',
      value: 'Materials'
    }
  },
  {
    id: 2577,
    attributes: {
      code: 'component.notification.typeValue.retro',
      value: 'Retro-synthesis Design Task'
    }
  },
  {
    id: 2626,
    attributes: {
      code: 'menu.list.material-manage.search-molecule',
      value: 'Structural Formula Search'
    }
  },
  {
    id: 2627,
    attributes: {
      code: 'menu.list.material-manage.black-list',
      value: 'Material Black List'
    }
  },
  {
    id: 2628,
    attributes: {
      code: 'menu.list.material-manage.black-list.add',
      value: 'Add Material Black List'
    }
  },
  {
    id: 2629,
    attributes: {
      code: 'menu.list.procedure',
      value: 'Experiment Procedure List'
    }
  },
  {
    id: 2630,
    attributes: {
      code: 'menu.list.procedure.detail',
      value: 'Experiment Procedure Detail'
    }
  },
  {
    id: 2631,
    attributes: {
      code: 'menu.list.experiment.execute',
      value: 'Experiment List'
    }
  },
  {
    id: 2632,
    attributes: {
      code: 'menu.list.experiment.execute.detail',
      value: 'Experiment Detail'
    }
  },
  {
    id: 2604,
    attributes: {
      code: 'menu.list.project-list.detail.experiment-conclusion.knowledgeBase',
      value: 'Add To Knowledge Base'
    }
  },
  {
    id: 3488,
    attributes: {
      code: 'lab-name',
      value: 'Lab Name'
    }
  },
  {
    id: 3489,
    attributes: {
      code: 'lab-supervisor',
      value: 'Lab Supervisor'
    }
  },
  {
    id: 3490,
    attributes: {
      code: 'robot-count',
      value: 'Robot Count'
    }
  },
  {
    id: 3491,
    attributes: {
      code: 'lab-monitor',
      value: 'Lab Monitor'
    }
  },
  {
    id: 3492,
    attributes: {
      code: 'robot-name',
      value: 'Robot Name'
    }
  },
  {
    id: 3493,
    attributes: {
      code: 'robot-model',
      value: 'Robot Model'
    }
  },
  {
    id: 3494,
    attributes: {
      code: 'robot-status',
      value: 'Robot Status'
    }
  },
  {
    id: 2075,
    attributes: {
      code: 'success-save-draft',
      value: 'Successfully Saved the Quotation'
    }
  },
  {
    id: 2145,
    attributes: {
      code: 'set-default-route',
      value: 'Set as Default route'
    }
  },
  {
    id: 2146,
    attributes: {
      code: 'copy-route',
      value: 'Copy route'
    }
  },
  {
    id: 2150,
    attributes: {
      code: 'search',
      value: 'Search'
    }
  },
  {
    id: 2398,
    attributes: {
      code: 'pages.admin.subPage.title',
      value: 'This page can only be viewed by Admin'
    }
  },
  {
    id: 2399,
    attributes: {
      code: 'pages.searchTable.createForm.newRule',
      value: 'New Rule'
    }
  },
  {
    id: 3390,
    attributes: {
      code: 'explosive',
      value: 'Flammable'
    }
  },
  {
    id: 2158,
    attributes: {
      code: 'wait-tip',
      value: 'You are currently in line. Please wait patiently.'
    }
  },
  {
    id: 2025,
    attributes: {
      code: 'experimental-zone-empty-tip',
      value: "Please click on 'Retro with AI' on the top right corner."
    }
  },
  {
    id: 3409,
    attributes: {
      code: 'submit',
      value: 'Submit'
    }
  },
  {
    id: 2299,
    attributes: {
      code: 'transfer-to-structure',
      value: 'Transfer to Structure'
    }
  },
  {
    id: 2516,
    attributes: {
      code: 'pages.experimentDesign.label.newExperiment',
      value: 'New Experiment'
    }
  },
  {
    id: 2517,
    attributes: {
      code: 'pages.experimentDesign.label.updateExperiment',
      value: 'Update Experiment'
    }
  },
  {
    id: 2518,
    attributes: {
      code: 'pages.experimentDesign.label.test',
      value: 'Test'
    }
  },
  {
    id: 2597,
    attributes: {
      code: 'menu.list.workspace.myExperiment',
      value: 'My Experiments'
    }
  },
  {
    id: 2598,
    attributes: {
      code: 'menu.list.project-list',
      value: 'Projects'
    }
  },
  {
    id: 2599,
    attributes: {
      code: 'menu.list.project-list.detail',
      value: 'Project Details'
    }
  },
  {
    id: 2278,
    attributes: {
      code: 'molecules-status.synthesizing',
      value: 'Synthesizing'
    }
  },
  {
    id: 2279,
    attributes: {
      code: 'molecules-type',
      value: 'Molecule Type'
    }
  },
  {
    id: 2283,
    attributes: {
      code: 'aiGenerated',
      value: 'AI Routes'
    }
  },
  {
    id: 2621,
    attributes: {
      code: 'menu.list.route.edit',
      value: 'Edit Route'
    }
  },
  {
    id: 2605,
    attributes: {
      code: 'menu.list.project-list.quotation',
      value: 'Quotation'
    }
  },
  {
    id: 2347,
    attributes: {
      code: 'enlarge-molecule',
      value: 'Zoom-in'
    }
  },
  {
    id: 2453,
    attributes: {
      code: 'pages.searchTable.batchApproval',
      value: 'batch approval'
    }
  },
  {
    id: 2454,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.created',
      value: 'Created'
    }
  },
  {
    id: 2455,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.started',
      value: 'Started'
    }
  },
  {
    id: 2456,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.finished',
      value: 'Finished'
    }
  },
  {
    id: 2457,
    attributes: {
      code: 'supplier.edit.finished',
      value: 'Calculate cost'
    }
  },
  {
    id: 2458,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.holding',
      value: 'Holding'
    }
  },
  {
    id: 2459,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.cancelled',
      value: 'Cancelled'
    }
  },
  {
    id: 2528,
    attributes: {
      code: 'status',
      value: 'Status'
    }
  },
  {
    id: 2295,
    attributes: {
      code: 'role',
      value: 'Role'
    }
  },
  {
    id: 2296,
    attributes: {
      code: 'edit',
      value: 'Edit'
    }
  },
  {
    id: 2297,
    attributes: {
      code: 'add-member',
      value: 'Add Member'
    }
  },
  {
    id: 2300,
    attributes: {
      code: 'del',
      value: 'Delete'
    }
  },
  {
    id: 2499,
    attributes: {
      code: 'pages.experiment.label.operation.edit',
      value: 'Edit'
    }
  },
  {
    id: 2500,
    attributes: {
      code: 'pages.experiment.label.operation.cancel',
      value: 'Cancel'
    }
  },
  {
    id: 2571,
    attributes: {
      code: 'GENERAL',
      value: 'General'
    }
  },
  {
    id: 2572,
    attributes: {
      code: 'VERSION_TAG',
      value: 'Version Tag'
    }
  },
  {
    id: 2573,
    attributes: {
      code: 'Name',
      value: 'Name'
    }
  },
  {
    id: 2574,
    attributes: {
      code: 'component.tagSelect.expand',
      value: 'Expand'
    }
  },
  {
    id: 2575,
    attributes: {
      code: 'component.tagSelect.collapse',
      value: 'Collapse'
    }
  },
  {
    id: 2576,
    attributes: {
      code: 'component.tagSelect.all',
      value: 'All'
    }
  },
  {
    id: 2578,
    attributes: {
      code: 'component.notification.statusValue.limited',
      value: 'Preparing'
    }
  },
  {
    id: 2579,
    attributes: {
      code: 'component.notification.statusValue.running',
      value: 'Running'
    }
  },
  {
    id: 2665,
    attributes: {
      code: 'app.general.message.success',
      value: '  Successfully'
    }
  },
  {
    id: 2534,
    attributes: {
      code: 'STARTEVENT',
      value: 'Create StartEvent'
    }
  },
  {
    id: 3404,
    attributes: {
      code: 'please-enter',
      value: 'Please enter...'
    }
  },
  {
    id: 3401,
    attributes: {
      code: 'cancel-projects',
      value: 'Cancel Project'
    }
  },
  {
    id: 3406,
    attributes: {
      code: 'from-this-search',
      value: 'from this task'
    }
  },
  {
    id: 3408,
    attributes: {
      code: 'from-other-search',
      value: 'from other task'
    }
  },
  {
    id: 2593,
    attributes: {
      code: 'menu.list.workspace',
      value: 'Home'
    }
  },
  {
    id: 2452,
    attributes: {
      code: 'pages.searchTable.batchDeletion',
      value: 'batch deletion'
    }
  },
  {
    id: 2460,
    attributes: {
      code: 'pages.projectTable.statusChangeLabel.canceled',
      value: 'Canceled'
    }
  },
  {
    id: 2461,
    attributes: {
      code: 'pages.reaction.label.name',
      value: 'Route ID'
    }
  },
  {
    id: 2462,
    attributes: {
      code: 'pages.reaction.label.status',
      value: 'Route Status'
    }
  },
  {
    id: 2464,
    attributes: {
      code: 'pages.reaction.label.material-sheet',
      value: 'Material Sheet'
    }
  },
  {
    id: 2465,
    attributes: {
      code: 'pages.reaction.label.referenceReaction',
      value: 'Add to My reaction'
    }
  },
  {
    id: 2466,
    attributes: {
      code: 'pages.reaction.label.createReaction',
      value: 'Add Reaction'
    }
  },
  {
    id: 2467,
    attributes: {
      code: 'pages.reaction.label.editReaction',
      value: 'Edit Reaction'
    }
  },
  {
    id: 2468,
    attributes: {
      code: 'pages.reaction.label.createMaterialTableFromRxn',
      value: 'Extract materials through reaction Smiles'
    }
  },
  {
    id: 2469,
    attributes: {
      code: 'pages.reaction.label.warn.productDiff',
      value: 'The product does not match the reaction. Please re-enter.'
    }
  },
  {
    id: 2470,
    attributes: {
      code: 'pages.reaction.label.warn.materialDiff',
      value: 'The main reactant does not match the reaction. Please re-enter.'
    }
  },
  {
    id: 2292,
    attributes: {
      code: 'search-molecule',
      value: 'Search Molecule'
    }
  },
  {
    id: 2004,
    attributes: {
      code: 'min',
      value: 'min'
    }
  },
  {
    id: 2362,
    attributes: {
      code: 'this',
      value: 'This'
    }
  }
)
export { data }
