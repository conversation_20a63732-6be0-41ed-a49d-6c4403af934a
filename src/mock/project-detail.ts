const Mock = require('mockjs')
const data = Mock.mock({
  projectInfo: {
    blocked: false,
    confirmed: true,
    createdAt: '2023-07-10T11:21:12.539Z',
    email: '<EMAIL>',
    id: 1,
    provider: 'keycloak',
    role: {
      id: 1,
      name: 'Authenticated',
      description: 'Default role given to authenticated user.',
      type: 'authenticated',
      createdAt: '2023-06-20T10:54:36.570Z'
    },
    updatedAt: '2023-07-10T11:21:12.539Z',
    username: 'test3',
    name: 'Valen Test Project',
    no: 'project_2023',
    customer: 'Valen',
    type: 'personal',
    delivery_date: '2023-12-12:24:07.182Z',
    status: 'created',
    start_datetime: null,
    end_datetime: null,
    project_status_audits: [],
    project_members: [
      {
        id: 1,
        user_id: '1',
        project: {
          id: 1,
          name: 'Valen Test Project',
          no: 'test 0',
          customer: 'Valen',
          type: 'personal',
          delivery_date: '2023-06-02',
          status: 'created',
          start_datetime: null,
          end_datetime: null,
          createdAt: '2023-06-28T07:24:07.182Z',
          updatedAt: '2023-06-28T07:28:14.764Z'
        },
        role: {
          id: 1,
          name: '负责人',
          value: 'leader',
          createdAt: '2023-06-28T07:22:41.257Z',
          updatedAt: '2023-06-28T07:22:41.257Z'
        }
      },
      {
        id: 2,
        user_id: '1',
        project: {
          id: 1,
          name: 'Valen Test Project',
          no: 'test 0',
          customer: 'Valen',
          type: 'personal',
          delivery_date: '2023-06-02',
          status: 'created',
          start_datetime: null,
          end_datetime: null,
          createdAt: '2023-06-28T07:24:07.182Z',
          updatedAt: '2023-06-28T07:28:14.764Z'
        },
        role: {
          id: 2,
          name: '项目经理',
          value: 'manager',
          createdAt: '2023-06-28T07:23:12.521Z',
          updatedAt: '2023-06-28T07:23:12.521Z'
        }
      }
    ],
    leaders: [
      {
        id: 1,
        user_id: '1',
        project: {
          id: 1,
          name: 'Valen Test Project',
          no: 'test 0',
          customer: 'Valen',
          type: 'personal',
          delivery_date: '2023-06-02',
          status: 'created',
          start_datetime: null,
          end_datetime: null,
          createdAt: '2023-06-28T07:24:07.182Z',
          updatedAt: '2023-06-28T07:28:14.764Z'
        },
        role: {
          id: 1,
          name: '负责人',
          value: 'leader',
          createdAt: '2023-06-28T07:22:41.257Z',
          updatedAt: '2023-06-28T07:22:41.257Z'
        }
      }
    ],
    managers: [
      {
        id: 2,
        user_id: '1',
        project: {
          id: 1,
          name: 'Valen Test Project',
          no: 'test 0',
          customer: 'Valen',
          type: 'personal',
          delivery_date: '2023-06-02',
          status: 'created',
          start_datetime: null,
          end_datetime: null,
          createdAt: '2023-06-28T07:24:07.182Z',
          updatedAt: '2023-06-28T07:28:14.764Z'
        },
        role: {
          id: 2,
          name: '项目经理',
          value: 'manager',
          createdAt: '2023-06-28T07:23:12.521Z',
          updatedAt: '2023-06-28T07:23:12.521Z'
        }
      }
    ]
  },
  cardData: [
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P2',
      type: 'created'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P0',
      type: 'designing'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P0',
      type: 'synthsizing'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P0',
      type: 'created'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P0',
      type: 'created'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P0',
      type: 'created'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P1',
      type: 'created'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P2',
      type: 'created'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P3',
      type: 'created'
    },
    {
      a: 'TWW-20230505_1',
      smiles: 'OC1=C(OCC(O)=O)C=C([N+]([O-])=O)C=C1>>C1C=CC=C1',
      c: '中间体',
      d: 'Jessie',
      e: '44',
      f: '0',
      g: 'P0',
      type: 'created'
    }
  ]
})
export { data }
