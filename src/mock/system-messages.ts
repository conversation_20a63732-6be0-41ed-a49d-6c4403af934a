const Mock = require('mockjs')
const data = Mock.mock({
  systemsMock: [
    {
      data: [
        {
          id: 1,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-06T09:21:07.119Z',
          updatedAt: '2024-03-06T09:21:12.353Z',
          publishedAt: '2024-03-06T09:21:12.190Z',
          system_message: null
        },
        {
          id: 3,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-06T09:28:50.746Z',
          updatedAt: '2024-03-06T09:28:57.867Z',
          publishedAt: '2024-03-06T09:28:57.716Z',
          system_message: null
        },
        {
          id: 9,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:45:48.928Z',
          updatedAt: '2024-03-11T08:45:48.928Z',
          publishedAt: '2024-03-11T08:45:48.925Z',
          system_message: {
            id: 13,
            experiment_no: 'E-202311131',
            message_level: 'I',
            createdAt: '2024-03-11T08:45:48.874Z',
            updatedAt: '2024-03-11T08:45:48.874Z',
            publishedAt: '2024-03-11T08:45:48.871Z',
            message: null,
            project_no: null,
            event_type: null,
            event_data: {},
            event_level: 'experiment'
          }
        },
        {
          id: 13,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T09:01:14.343Z',
          updatedAt: '2024-03-11T09:01:14.343Z',
          publishedAt: '2024-03-11T09:01:14.339Z',
          system_message: {
            id: 17,
            experiment_no: 'E-202311131',
            message_level: 'I',
            createdAt: '2024-03-11T09:01:14.285Z',
            updatedAt: '2024-03-11T09:01:14.285Z',
            publishedAt: '2024-03-11T09:01:14.283Z',
            message: null,
            project_no: null,
            event_type: null,
            event_data: {},
            event_level: 'experiment'
          }
        },
        {
          id: 11,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:48:49.811Z',
          updatedAt: '2024-03-11T08:48:49.811Z',
          publishedAt: '2024-03-11T08:48:49.806Z',
          system_message: {
            id: 15,
            experiment_no: 'E-202311131',
            message_level: 'I',
            createdAt: '2024-03-11T08:48:49.787Z',
            updatedAt: '2024-03-11T08:48:49.787Z',
            publishedAt: '2024-03-11T08:48:49.782Z',
            message: null,
            project_no: null,
            event_type: null,
            event_data: {},
            event_level: 'experiment'
          }
        },
        {
          id: 8,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:06:50.033Z',
          updatedAt: '2024-03-11T08:06:50.033Z',
          publishedAt: '2024-03-11T08:06:50.027Z',
          system_message: {
            id: 12,
            experiment_no: 'E-202311131',
            message_level: 'I',
            createdAt: '2024-03-11T08:06:50.001Z',
            updatedAt: '2024-03-11T08:06:50.001Z',
            publishedAt: '2024-03-11T08:06:49.995Z',
            message: null,
            project_no: null,
            event_type: null,
            event_data: {},
            event_level: 'experiment'
          }
        },
        {
          id: 12,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:49:27.115Z',
          updatedAt: '2024-03-11T08:49:27.115Z',
          publishedAt: '2024-03-11T08:49:27.112Z',
          system_message: {
            id: 16,
            experiment_no: 'E-202311131',
            message_level: 'I',
            createdAt: '2024-03-11T08:49:27.092Z',
            updatedAt: '2024-03-11T08:49:27.092Z',
            publishedAt: '2024-03-11T08:49:27.085Z',
            message: null,
            project_no: null,
            event_type: null,
            event_data: {},
            event_level: 'experiment'
          }
        },
        {
          id: 10,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T08:47:51.357Z',
          updatedAt: '2024-03-11T08:47:51.357Z',
          publishedAt: '2024-03-11T08:47:51.353Z',
          system_message: {
            id: 14,
            experiment_no: 'E-202311131',
            message_level: 'I',
            createdAt: '2024-03-11T08:47:51.338Z',
            updatedAt: '2024-03-11T08:47:51.338Z',
            publishedAt: '2024-03-11T08:47:51.333Z',
            message:
              '<p>【E-202311131】产物检测报告已出,请查看<a href="/projects/XXX/reaction/XXX/experimental-procedure/conclusion/E-202311131/reportDetail/F231113-1">AI解读报告</a>,机器人自动处理时间：2024-03-12 15:13:53</p>',
            project_no: null,
            event_type: null,
            event_data: {},
            event_level: 'experiment'
          }
        },
        {
          id: 7,
          user_id: '1',
          readed: false,
          createdAt: '2024-03-11T07:36:46.568Z',
          updatedAt: '2024-03-11T07:36:46.568Z',
          publishedAt: '2024-03-11T07:36:46.565Z',
          system_message: {
            id: 11,
            experiment_no: 'E-202311131',
            message_level: 'I',
            createdAt: '2024-03-11T07:36:46.510Z',
            updatedAt: '2024-03-11T07:36:46.510Z',
            publishedAt: '2024-03-11T07:36:46.509Z',
            message: null,
            project_no: null,
            event_type: null,
            event_data: {},
            event_level: 'experiment'
          }
        }
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 25,
          pageCount: 1,
          total: 9
        }
      }
    }
  ]
})
export { systemsMock }
