const Mock = require('mockjs')
const data = Mock.mock({
  toBeDesignedCompound: 4,
  hechegnzhong: 2,
  待推进的反应: 3,
  待开始的实验: 6,
  进行中的实验: 5,
  待结论的实验: 9,
  compoundListData: {
    data: [
      {
        id: 256,
        no: '02352fc3',
        input_smiles: 'C1C=CC2C=CC=CC=2C=1',
        type: 'target',
        priority: 'P0',
        director_id: '1',
        status: 'created',
        searching: false,
        createdAt: '2023-09-04T21:54:37.348Z',
        updatedAt: '2023-09-06T03:28:10.326Z',
        project_routes: [],
        compound: {
          id: 157,
          smiles: 'C1=CC=C2C=CC=CC2=C1'
        },
        project: {
          id: 43,
          no: 'P-230905-2008'
        },
        retro_processes: [
          {
            id: 529,
            retro_backbones: [
              {
                id: 14291
              },
              {
                id: 14292
              },
              {
                id: 14293
              }
            ]
          },
          {
            id: 541,
            retro_backbones: [
              {
                id: 14906
              },
              {
                id: 14907
              },
              {
                id: 14908
              }
            ]
          }
        ],
        user_info: {
          id: 1,
          username: 'test3',
          email: '<EMAIL>'
        },
        project_routes_number: 0,
        retro_backbones_number: 6
      },
      {
        id: 267,
        no: '027f72e9',
        input_smiles:
          'CC([C@H]1C(=O)NC=Cc2ccc(cc2)O[C@@H](C(C)C)[C@H](NC([C@@H](N(C)C)Cc2ccccc2)=O)C(=O)N1)C |c:6|',
        type: 'target',
        priority: 'P0',
        director_id: '1',
        status: 'canceled',
        searching: false,
        createdAt: '2023-09-11T12:01:24.022Z',
        updatedAt: '2023-11-10T03:10:20.185Z',
        project_routes: [],
        compound: {
          id: 164,
          smiles:
            'CC(C)[C@@H]1NC(=O)[C@@H](NC(=O)[C@H](CC2=CC=CC=C2)N(C)C)[C@H](C(C)C)OC2=CC=C(C=CNC1=O)C=C2'
        },
        project: {
          id: 44,
          no: 'P-230905-2015'
        },
        retro_processes: [],
        user_info: {
          id: 1,
          username: 'test3',
          email: '<EMAIL>'
        },
        project_routes_number: 0,
        retro_backbones_number: 0
      },
      {
        id: 210,
        no: '02a0b0d4',
        input_smiles: 'CC(OC(NC1C(F)=C(C(CC2N=C(Cl)N=CC=2)=O)C=CC=1)=O)(C)C',
        type: 'target',
        priority: 'P0',
        director_id: '1',
        status: 'finished',
        searching: false,
        createdAt: '2023-08-06T22:45:40.520Z',
        updatedAt: '2023-11-10T03:13:26.877Z',
        project_routes: [],
        compound: {
          id: 126,
          smiles: 'CC(C)(C)OC(=O)NC1=C(F)C(C(=O)CC2=NC(Cl)=NC=C2)=CC=C1'
        },
        project: {
          id: 30,
          no: 'P-230728-664'
        },
        retro_processes: [],
        user_info: {
          id: 1,
          username: 'test3',
          email: '<EMAIL>'
        },
        project_routes_number: 0,
        retro_backbones_number: 0
      }
    ],
    meta: {
      pagination: {
        page: 1,
        pageSize: 25,
        pageCount: 1,
        total: 1
      }
    }
  },
  反应数据: [
    {
      id: 299,
      updatedAt: '2023-11-04T05:58:19.537Z',
      reaction:
        'COC1C=CC([N+](=O)[O-])=CC=1O[Na]>>COC1C=CC([N+](=O)[O-])=CC=1OCC(=O)OC(C)(C)C',
      effective_procedures: [
        5908675, 5908678, 5908679, 5908734, 5908735, 5908736, 5908737, 5908738,
        5908739
      ],
      history_procedures: [
        1634155, 3581799, 3581793, 5908538, 3581800, 2008625
      ],
      createdAt: '2023-09-15T05:54:18.207Z',
      project: {
        id: 48,
        no: 'P-230915-2554'
      },
      project_routes: [
        {
          id: 84,
          name: 'c566dd53-1',
          project_compound: {
            id: 281
          }
        },
        {
          id: 112,
          name: 'c566dd53-3',
          project_compound: {
            id: 281
          }
        }
      ]
    },
    {
      id: 357,
      updatedAt: '2023-10-31T03:53:19.875Z',
      reaction: 'COC(=O)C=[N+]=[N-]>>COC(=O)C1CC1C1=CC=CC(Br)=C1',
      effective_procedures: [5908618, 5908640, 5908686],
      history_procedures: [2640636, 2642151, 2640638],
      createdAt: '2023-10-17T01:43:28.039Z',
      project: {
        id: 70,
        no: 'P-231016-3547'
      },
      project_routes: [
        {
          id: 107,
          name: 'leo_01-1',
          project_compound: {
            id: 1337
          }
        }
      ]
    },
    {
      id: 356,
      updatedAt: '2023-10-20T02:05:10.451Z',
      reaction: 'COC(=O)C1CC1C1=CC=CC(Br)=C1>>O=C(O)C1CC1C1=CC=CC(Br)=C1',
      effective_procedures: [5908627],
      history_procedures: [3039051],
      createdAt: '2023-10-17T01:43:28.037Z',
      project: {
        id: 70,
        no: 'P-231016-3547'
      },
      project_routes: [
        {
          id: 107,
          name: 'leo_01-1',
          project_compound: {
            id: 1337
          }
        }
      ]
    }
  ]
})
export { data }
