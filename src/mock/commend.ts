const Mock = require('mockjs')
const data = Mock.mock({
  commendStartInfo: {
    id: 21,
    collection_subject: "daadafds'",
    createdAt: '2023-07-07T02:21:28.960Z',
    updatedAt: '2023-07-07T02:21:28.960Z',
    publishedAt: '2023-07-07T02:21:28.960Z',
    content_count: 0,
    collection_class: 'project',
    collection_id: '4'
  },
  commentDetail: [
    {
      id: 12,
      point: 0,
      commentor: '笑迎',
      value: 'testfasdf',
      comment_id: '2',
      createdAt: '2023-07-07T09:49:49.015Z',
      updatedAt: '2023-07-07T09:49:49.015Z',
      publishedAt: '2023-07-07T09:49:49.010Z'
    },
    {
      id: 13,
      point: 0,
      commentor: '笑迎',
      value: '325123fsdafsadf',
      comment_id: '2',
      createdAt: '2023-07-07T09:50:50.047Z',
      updatedAt: '2023-07-07T09:50:50.047Z',
      publishedAt: '2023-07-07T09:50:50.045Z'
    }
  ]
})
export { data }
