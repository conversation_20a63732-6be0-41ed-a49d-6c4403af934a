const Mock = require('mockjs')
const data = Mock.mock({
  searchHistoryData: [
    {
      id: 2,
      createdAt: '2023-07-03T05:48:45.118Z',
      updatedAt: '2023-07-03T05:48:45.118Z',
      retro_id: 'Aest_retro_2',
      status: 'completed',
      params: {},
      response: null,
      creator_id: '1',
      retro_backbones: [{ a: 1 }, { a: 2 }]
    },
    {
      id: 1,
      createdAt: '2023-07-03T05:47:50.054Z',
      updatedAt: '2023-07-03T05:47:50.054Z',
      retro_id: 'test_retro_1',
      status: 'running',
      params: {},
      response: null,
      creator_id: '1',
      retro_backbones: []
    }
  ],
  detail: [
    {
      id: 554,
      smiles: 'CN1C(=O)C2C(NC3C(F)=CC(I)=CC=3)=C(CC)C=NC=2N(C)C1=O',
      status: 'designing',
      createdAt: '2023-06-26T01:59:24.748Z',
      updatedAt: '2023-06-28T06:32:54.637Z',
      publishedAt: '2023-06-26T01:59:24.747Z',
      no: 'bid-22110092-Compound H',
      normalizedSmiles: 'CCc1cnc2c(c1Nc1ccc(I)cc1F)c(=O)n(C)c(=O)n2C',
      routes: [
        {
          id: 1137,
          stepCount: 7,
          status: 'edited',
          createdAt: '2023-06-26T06:35:17.061Z',
          updatedAt: '2023-06-26T06:35:17.061Z',
          publishedAt: '2023-06-26T06:35:17.059Z',
          routeTree: {
            value: 'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
            children: [
              {
                value: 'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C',
                children: [
                  {
                    value: 'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
                    children: [
                      {
                        value: 'CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O',
                        children: [
                          {
                            value: 'CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O',
                            children: [
                              {
                                value: 'COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C',
                                children: [
                                  {
                                    value: 'COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2',
                                    children: [
                                      {
                                        value: 'COC(=O)C1=C(N)N=C(Cl)C=C1OC',
                                        children: []
                                      }
                                    ]
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },
          targetToRxns: {
            'COC(=O)C1=C(N)N=C(Cl)C=C1OC': [
              'CO.COC(=O)C1=C(Cl)C=C(Cl)N=C1N.C[O-].[Na+]>>COC(=O)C1=C(N)N=C(Cl)C=C1OC',
              'COC(=O)C1=C(Cl)C=C(Cl)N=C1N.C[O-].[Na+]>>COC(=O)C1=C(N)N=C(Cl)C=C1OC'
            ],
            'COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2': [
              'N=C(N)O.COC(=O)C1=C(N)N=C(Cl)C=C1OC>>COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2'
            ],
            'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O.[HH].[Pd]>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C': [
              'O=C([O-])[O-].[K+].[K+].CI.COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2>>COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O': [
              'BrB(Br)Br.CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O>>CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O'
            ],
            'CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O': [
              'CC(C)[N-]C(C)C.[Li+].CCI.COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C>>CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C': [
              'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C': [
              'CCN(CC)CC.CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.O=S(=O)(N(C1=CC=CC=C1)S(=O)(=O)C(F)(F)F)C(F)(F)F>>CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C'
            ]
          }
        },
        {
          id: 1138,
          stepCount: 6,
          status: 'edited',
          createdAt: '2023-06-26T06:38:47.868Z',
          updatedAt: '2023-06-26T06:38:47.868Z',
          publishedAt: '2023-06-26T06:38:47.866Z',
          routeTree: {
            value: 'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
            children: [
              {
                value: 'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C',
                children: [
                  {
                    value: 'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
                    children: [
                      {
                        value: 'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
                        children: [
                          {
                            value: 'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C',
                            children: [
                              {
                                value: 'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
                                children: [
                                  {
                                    value: 'CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
                                    children: []
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },
          targetToRxns: {
            'N=C(O)C1=C(Cl)C=CNC1=N': [
              'N.O.N=C(O)C1=C(Cl)C=CN=C1Cl>>N=C(O)C1=C(Cl)C=CNC1=N',
              'O=S(Cl)Cl.N.O.N=C1NC=CC(Cl)=C1C(=O)O>>N=C(O)C1=C(Cl)C=CNC1=N',
              'O=S(=O)(O)O.N#CC1=C(Cl)C=CNC1=N>>N=C(O)C1=C(Cl)C=CNC1=N',
              'O=C([O-])[O-].[K+].[K+].OO.N#CC1=C(Cl)C=CNC1=N>>N=C(O)C1=C(Cl)C=CNC1=N'
            ],
            'N=C(O)C1=C(Cl)C(Br)=CNC1=N': [
              'O=S(Cl)Cl.N.O.N=C1NC=C(Br)C(Cl)=C1C(=O)O>>N=C(O)C1=C(Cl)C(Br)=CNC1=N',
              'O=C1CCC(=O)N1Br.N=C(O)C1=C(Cl)C=CNC1=N>>N=C(O)C1=C(Cl)C(Br)=CNC1=N',
              'COC(=O)C1=C(Cl)C(Br)=CNC1=N.N>>N=C(O)C1=C(Cl)C(Br)=CNC1=N'
            ],
            'N=C1NC=C(Br)C(Cl)=C1C(=O)O': [
              'O=C1CCC(=O)N1Br.N=C1NC=CC(Cl)=C1C(=O)O>>N=C1NC=C(Br)C(Cl)=C1C(=O)O',
              'O=C=O.CC(C)[N-]C(C)C.[Li+].N=C1C=C(Cl)C(Br)=CN1>>N=C1NC=C(Br)C(Cl)=C1C(=O)O'
            ],
            'OC1=NC2=NC=CC(I)=C2C(O)=N1': [
              'N=C(N)O.COC(=O)C1=C(I)C=CNC1=N>>OC1=NC2=NC=CC(I)=C2C(O)=N1'
            ],
            'COC(=O)C1=C(Cl)C(Br)=CNC1=N': [
              'CCN(CC)CC.[C-]#[O+].N=C1NC=C(Br)C(Cl)=C1I.C1=CC(P(C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC(P(C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Cl-].[Cl-].[Fe].[Pd+2]>>COC(=O)C1=C(Cl)C(Br)=CNC1=N'
            ],
            'OC1=NC2=NC=CC(Cl)=C2C(O)=N1': [
              'N=C(N)O.N=C1NC=CC(Cl)=C1C(=O)O>>OC1=NC2=NC=CC(Cl)=C2C(O)=N1',
              '[Na+].[H-].N=C(O)C1=C(Cl)C=CNC1=N.O=C(N1C=CN=C1)N1C=CN=C1>>OC1=NC2=NC=CC(Cl)=C2C(O)=N1'
            ],
            'CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O': [
              'CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.[I-].[Na+]>>CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.CI.OC1=NC2=NC=CC(I)=C2C(O)=N1>>CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC=C2Br)N(C)C1=O': [
              'Br.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O>>CN1C(=O)C2=C(N=CC=C2Br)N(C)C1=O'
            ],
            'COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2': [
              'CO.[Na+].[H-].OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1>>COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2'
            ],
            'OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1': [
              'O=C1CCC(=O)N1Br.OC1=NC2=NC=CC(Cl)=C2C(O)=N1>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1',
              'COC(=O)C1=C(Cl)C(Br)=CN=C1N=C(O)N=C(O)C(Cl)(Cl)Cl.N.O>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1',
              'N=C(O)C1=C(Cl)C(Br)=CNC1=N.O=C(N1C=CN=C1)N1C=CN=C1>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1',
              'COC(=O)C1=C(Cl)C(Br)=CN=C1NC(=N)O.[Na+].[OH-]>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1'
            ],
            'COC(=O)C1=C(Cl)C(Br)=CN=C1NC(=N)O': [
              'COC(=O)C1=C(Cl)C(Br)=CNC1=N.C[Si](C)(C)N=C=O>>COC(=O)C1=C(Cl)C(Br)=CN=C1NC(=N)O'
            ],
            'OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1': [
              'N=C(N)O.NC1=C(C(=O)O)C(Cl)=CC(Cl)=N1>>OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1'
            ],
            'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'Br.CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
              'C=CC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
              'CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O.[HH].[Pd]>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O': [
              'BrBr.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CC#N.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.CC(=O)O.CC(=O)[O-].[Na+].CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.ClC(Cl)Cl.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'ClCCl.O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.ClCCl.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN(C)C=O.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O': [
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN(C)C=O.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.ClCCl.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.ClC(Cl)Cl.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.CC(=O)O.CC(=O)[O-].[Na+].CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O'
            ],
            'C=CC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O.[CH2]CCC.[CH2]CCC.[CH2]CCC.[CH]=C.[Sn].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C.[CH2]C.[CH2]C.[Zn]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
              'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.[Na]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.C[O-].[Na+]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O': [
              'O=P(Cl)(Cl)Cl.CN1C(=O)C2=C(N=C(O)C=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.CI.OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1>>CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O',
              'CCN(CC)CC.CN1C(=O)C2=C(N=C(O)C=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O': [
              'O=P(Cl)(Cl)Cl.CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'Cl.CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O.O=N[O-].[Na+].O.[Cl-].[Cu+]>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O'
            ],
            'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C': [
              'CO.CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O>>COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C',
              'O=C([O-])[O-].[K+].[K+].CI.CN(C)C=O.CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O>>COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C'
            ],
            'COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C': [
              'CO.CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O>>COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C',
              'O=C([O-])[O-].[K+].[K+].CI.COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2>>COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C'
            ],
            'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C.[CH2]CCC.[CH2]CCC.[CH2]CCC.[CH]=C.[Sn].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O': [
              'BrB(Br)Br.CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O>>CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O'
            ],
            'CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O': [
              'CC(C)[N-]C(C)C.[Li+].CCI.COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C>>CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O'
            ],
            'CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1': [
              'CC(C)(C)O.[K+].CN1C(=O)N=C(O)C2=C1N=CC=C2Cl.OCC1=CC=CC=C1>>CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1',
              'CN1C(=O)N=C(O)C2=C1N=CC=C2Cl.[Na+].[H-].OCC1=CC=CC=C1>>CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1'
            ],
            'OC1=NC2=C(C(OCC3=CC=CC=C3)=CC=N2)C(O)=N1': [
              '[Na].OCC1=CC=CC=C1.OC1=NC2=NC=CC(Cl)=C2C(O)=N1>>OC1=NC2=C(C(OCC3=CC=CC=C3)=CC=N2)C(O)=N1'
            ],
            'CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O': [
              'O=C([O-])[O-].[K+].[K+].CC(C)(C)O.CC(C)C1=CC(C(C)C)=C(C2=CC=CC=C2P(C2CCCCC2)C2CCCCC2)C(C(C)C)=C1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.[Pd].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'C1CCOC1.C[Si](C)(C)[N-][Si](C)(C)C.[Li+].CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CC(C)(C)[O-].[Na+].CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=C(Cl)C=C2NC2=CC=CC=C2F)N(C)C1=O.[HH].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C([O-])[O-].[Cs+].[Cs+].C1COCCO1.CC1(C)C2=C(OC3=C(P(C4=CC=CC=C4)C4=CC=CC=C4)C=CC=C31)C(P(C1=CC=CC=C1)C1=CC=CC=C1)=CC=C2.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.[Pd].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C([O-])[O-].[Cs+].[Cs+].C1COCCO1.CC(=O)[O-].CC(=O)[O-].[Pd+2].CC1(C)C2=C(OC3=C(P(C4=CC=CC=C4)C4=CC=CC=C4)C=CC=C31)C(P(C1=CC=CC=C1)C1=CC=CC=C1)=CC=C2.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CCN(C(C)C)C(C)C.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'C1COCCO1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2Br)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C([O-])[O-].[Cs+].[Cs+].CC(C)C1=CC(C(C)C)=C(C2=CC=CC=C2P(C2CCCCC2)C2CCCCC2)C(C(C)C)=C1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F.O=C(C=CC1=CC=CC=C1)C=CC1=CC=CC=C1.O=C(C=CC1=CC=CC=C1)C=CC1=CC=CC=C1.O=C(C=CC1=CC=CC=C1)C=CC1=CC=CC=C1.[Pd].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CC(C)O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O': [
              'CN(C)C=O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.[Na+].[H-].OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.OC1=NC2=C(C(OCC3=CC=CC=C3)=CC=N2)C(O)=N1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O.BrCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'C1CCOC1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.O.[Na+].[H-].OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O'
            ],
            'CN1C(=O)N=C(O)C2=C1N=CC(Br)=C2OCC1=CC=CC=C1': [
              'O=C1CCC(=O)N1Br.CC#N.CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1>>CN1C(=O)N=C(O)C2=C1N=CC(Br)=C2OCC1=CC=CC=C1'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C': [
              'C=CC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'CCN(CC)CC.CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'C1COCCO1.CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'CCO.CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C': [
              'C1COCCO1.CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O.[CH2]C.[CH2]C.[Zn].C1=CC=C(P(C2=CC=CC=C2)C2=CC=C[CH-]2)C=C1.C1=CC=C(P(C2=CC=CC=C2)C2=CC=C[CH-]2)C=C1.[Cl-].[Cl-].[Fe+2].[Pd+2]>>CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.[Na+].[H-].OCC1=CC=CC=C1>>CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=C(Cl)C=C2NC2=CC=CC=C2F)N(C)C1=O': [
              'C[Si](C)(C)[N-][Si](C)(C)C.[Li+].CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=C(Cl)C=C2NC2=CC=CC=C2F)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O': [
              'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O': [
              'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O.OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O.BrCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.CN(C)C=O.CN1C(=O)N=C(O)C2=C1N=CC(Br)=C2OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O'
            ],
            'C=CC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C': [
              'O=C([O-])[O-].[Na+].[Na+].C=C[B-](F)(F)F.CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O.[K+].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C': [
              'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.C[Si](C)(C)[N-][Si](C)(C)C.[Li+].NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'O=C1CCC(=O)N1I.CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.CC1=CC=C(S(=O)(=O)O)C=C1.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'COC(=O)C1=C(Cl)C(Br)=CN=C1N=C(O)N=C(O)C(Cl)(Cl)Cl': [
              'O=C=NC(=O)C(Cl)(Cl)Cl.COC(=O)C1=C(Cl)C(Br)=CNC1=N>>COC(=O)C1=C(Cl)C(Br)=CN=C1N=C(O)N=C(O)C(Cl)(Cl)Cl'
            ],
            'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C': [
              'CCN(CC)CC.CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.O=S(=O)(N(C1=CC=CC=C1)S(=O)(=O)C(F)(F)F)C(F)(F)F>>CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C'
            ]
          }
        },
        {
          id: 1243,
          stepCount: 6,
          status: 'edited',
          createdAt: '2023-06-29T10:36:50.867Z',
          updatedAt: '2023-06-29T10:36:50.867Z',
          publishedAt: '2023-06-29T10:36:50.864Z',
          routeTree: {
            value: 'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
            children: [
              {
                value: 'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C',
                children: [
                  {
                    value: 'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
                    children: [
                      {
                        value: 'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
                        children: [
                          {
                            value: 'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C',
                            children: [
                              {
                                value: 'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
                                children: [
                                  {
                                    value: 'CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
                                    children: []
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },
          targetToRxns: {
            'N=C(O)C1=C(Cl)C=CNC1=N': [
              'N.O.N=C(O)C1=C(Cl)C=CN=C1Cl>>N=C(O)C1=C(Cl)C=CNC1=N',
              'O=S(Cl)Cl.N.O.N=C1NC=CC(Cl)=C1C(=O)O>>N=C(O)C1=C(Cl)C=CNC1=N',
              'O=S(=O)(O)O.N#CC1=C(Cl)C=CNC1=N>>N=C(O)C1=C(Cl)C=CNC1=N',
              'O=C([O-])[O-].[K+].[K+].OO.N#CC1=C(Cl)C=CNC1=N>>N=C(O)C1=C(Cl)C=CNC1=N'
            ],
            'N=C(O)C1=C(Cl)C(Br)=CNC1=N': [
              'O=S(Cl)Cl.N.O.N=C1NC=C(Br)C(Cl)=C1C(=O)O>>N=C(O)C1=C(Cl)C(Br)=CNC1=N',
              'O=C1CCC(=O)N1Br.N=C(O)C1=C(Cl)C=CNC1=N>>N=C(O)C1=C(Cl)C(Br)=CNC1=N',
              'COC(=O)C1=C(Cl)C(Br)=CNC1=N.N>>N=C(O)C1=C(Cl)C(Br)=CNC1=N'
            ],
            'N=C1NC=C(Br)C(Cl)=C1C(=O)O': [
              'O=C1CCC(=O)N1Br.N=C1NC=CC(Cl)=C1C(=O)O>>N=C1NC=C(Br)C(Cl)=C1C(=O)O',
              'O=C=O.CC(C)[N-]C(C)C.[Li+].N=C1C=C(Cl)C(Br)=CN1>>N=C1NC=C(Br)C(Cl)=C1C(=O)O'
            ],
            'OC1=NC2=NC=CC(I)=C2C(O)=N1': [
              'N=C(N)O.COC(=O)C1=C(I)C=CNC1=N>>OC1=NC2=NC=CC(I)=C2C(O)=N1'
            ],
            'COC(=O)C1=C(Cl)C(Br)=CNC1=N': [
              'CCN(CC)CC.[C-]#[O+].N=C1NC=C(Br)C(Cl)=C1I.C1=CC(P(C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC(P(C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Cl-].[Cl-].[Fe].[Pd+2]>>COC(=O)C1=C(Cl)C(Br)=CNC1=N'
            ],
            'OC1=NC2=NC=CC(Cl)=C2C(O)=N1': [
              'N=C(N)O.N=C1NC=CC(Cl)=C1C(=O)O>>OC1=NC2=NC=CC(Cl)=C2C(O)=N1',
              '[Na+].[H-].N=C(O)C1=C(Cl)C=CNC1=N.O=C(N1C=CN=C1)N1C=CN=C1>>OC1=NC2=NC=CC(Cl)=C2C(O)=N1'
            ],
            'CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O': [
              'CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.[I-].[Na+]>>CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.CI.OC1=NC2=NC=CC(I)=C2C(O)=N1>>CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC=C2Br)N(C)C1=O': [
              'Br.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O>>CN1C(=O)C2=C(N=CC=C2Br)N(C)C1=O'
            ],
            'COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2': [
              'CO.[Na+].[H-].OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1>>COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2'
            ],
            'OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1': [
              'O=C1CCC(=O)N1Br.OC1=NC2=NC=CC(Cl)=C2C(O)=N1>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1',
              'COC(=O)C1=C(Cl)C(Br)=CN=C1N=C(O)N=C(O)C(Cl)(Cl)Cl.N.O>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1',
              'N=C(O)C1=C(Cl)C(Br)=CNC1=N.O=C(N1C=CN=C1)N1C=CN=C1>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1',
              'COC(=O)C1=C(Cl)C(Br)=CN=C1NC(=N)O.[Na+].[OH-]>>OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1'
            ],
            'COC(=O)C1=C(Cl)C(Br)=CN=C1NC(=N)O': [
              'COC(=O)C1=C(Cl)C(Br)=CNC1=N.C[Si](C)(C)N=C=O>>COC(=O)C1=C(Cl)C(Br)=CN=C1NC(=N)O'
            ],
            'OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1': [
              'N=C(N)O.NC1=C(C(=O)O)C(Cl)=CC(Cl)=N1>>OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1'
            ],
            'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'Br.CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
              'C=CC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
              'CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O.[HH].[Pd]>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O': [
              'BrBr.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CC#N.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.CC(=O)O.CC(=O)[O-].[Na+].CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.ClC(Cl)Cl.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'ClCCl.O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'BrBr.ClCCl.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN(C)C=O.CN1C(=O)C2=C(N=CC=C2N)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O': [
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN(C)C=O.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.ClCCl.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.ClC(Cl)Cl.CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O',
              'BrBr.CC(=O)O.CC(=O)[O-].[Na+].CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O'
            ],
            'C=CC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O.[CH2]CCC.[CH2]CCC.[CH2]CCC.[CH]=C.[Sn].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C.[CH2]C.[CH2]C.[Zn]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
              'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.[Na]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.C[O-].[Na+]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O': [
              'O=P(Cl)(Cl)Cl.CN1C(=O)C2=C(N=C(O)C=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.CI.OC1=NC2=C(C(Cl)=CC(Cl)=N2)C(O)=N1>>CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O',
              'CCN(CC)CC.CN1C(=O)C2=C(N=C(O)C=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O': [
              'O=P(Cl)(Cl)Cl.CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'Cl.CN1C(=O)C2=C(N=CC(Br)=C2N)N(C)C1=O.O=N[O-].[Na+].O.[Cl-].[Cu+]>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.OC1=NC2=NC=C(Br)C(Cl)=C2C(O)=N1>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O'
            ],
            'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C': [
              'CO.CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O>>COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C',
              'O=C([O-])[O-].[K+].[K+].CI.CN(C)C=O.CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O>>COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C'
            ],
            'COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C': [
              'CO.CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O>>COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C',
              'O=C([O-])[O-].[K+].[K+].CI.COC1=CC(Cl)=NC2=C1C(O)=NC(O)=N2>>COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C'
            ],
            'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C.[CH2]CCC.[CH2]CCC.[CH2]CCC.[CH]=C.[Sn].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O': [
              'BrB(Br)Br.CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O>>CCC1=C(O)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O'
            ],
            'CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O': [
              'CC(C)[N-]C(C)C.[Li+].CCI.COC1=CC(Cl)=NC2=C1C(=O)N(C)C(=O)N2C>>CCC1=C(OC)C2=C(N=C1Cl)N(C)C(=O)N(C)C2=O'
            ],
            'CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1': [
              'CC(C)(C)O.[K+].CN1C(=O)N=C(O)C2=C1N=CC=C2Cl.OCC1=CC=CC=C1>>CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1',
              'CN1C(=O)N=C(O)C2=C1N=CC=C2Cl.[Na+].[H-].OCC1=CC=CC=C1>>CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1'
            ],
            'OC1=NC2=C(C(OCC3=CC=CC=C3)=CC=N2)C(O)=N1': [
              '[Na].OCC1=CC=CC=C1.OC1=NC2=NC=CC(Cl)=C2C(O)=N1>>OC1=NC2=C(C(OCC3=CC=CC=C3)=CC=N2)C(O)=N1'
            ],
            'CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O': [
              'O=C([O-])[O-].[K+].[K+].CC(C)(C)O.CC(C)C1=CC(C(C)C)=C(C2=CC=CC=C2P(C2CCCCC2)C2CCCCC2)C(C(C)C)=C1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.[Pd].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'C1CCOC1.C[Si](C)(C)[N-][Si](C)(C)C.[Li+].CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CC(C)(C)[O-].[Na+].CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=C(Cl)C=C2NC2=CC=CC=C2F)N(C)C1=O.[HH].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2I)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C([O-])[O-].[Cs+].[Cs+].C1COCCO1.CC1(C)C2=C(OC3=C(P(C4=CC=CC=C4)C4=CC=CC=C4)C=CC=C31)C(P(C1=CC=CC=C1)C1=CC=CC=C1)=CC=C2.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.O=C(/C=C/C1=CC=CC=C1)/C=C/C1=CC=CC=C1.[Pd].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C([O-])[O-].[Cs+].[Cs+].C1COCCO1.CC(=O)[O-].CC(=O)[O-].[Pd+2].CC1(C)C2=C(OC3=C(P(C4=CC=CC=C4)C4=CC=CC=C4)C=CC=C31)C(P(C1=CC=CC=C1)C1=CC=CC=C1)=CC=C2.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CCN(C(C)C)C(C)C.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'C1COCCO1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2Br)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C([O-])[O-].[Cs+].[Cs+].CC(C)C1=CC(C(C)C)=C(C2=CC=CC=C2P(C2CCCCC2)C2CCCCC2)C(C(C)C)=C1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F.O=C(C=CC1=CC=CC=C1)C=CC1=CC=CC=C1.O=C(C=CC1=CC=CC=C1)C=CC1=CC=CC=C1.O=C(C=CC1=CC=CC=C1)C=CC1=CC=CC=C1.[Pd].[Pd]>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O',
              'CC(C)O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O': [
              'CN(C)C=O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.[Na+].[H-].OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.OC1=NC2=C(C(OCC3=CC=CC=C3)=CC=N2)C(O)=N1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CN1C(=O)C2=C(N=CC=C2O)N(C)C1=O.BrCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O',
              'C1CCOC1.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.O.[Na+].[H-].OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O'
            ],
            'CN1C(=O)N=C(O)C2=C1N=CC(Br)=C2OCC1=CC=CC=C1': [
              'O=C1CCC(=O)N1Br.CC#N.CN1C(=O)N=C(O)C2=C1N=CC=C2OCC1=CC=CC=C1>>CN1C(=O)N=C(O)C2=C1N=CC(Br)=C2OCC1=CC=CC=C1'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C': [
              'C=CC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'CCN(CC)CC.CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'C1COCCO1.CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'CCO.CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=CC=CC=C1F>>CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C': [
              'C1COCCO1.CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O.[CH2]C.[CH2]C.[Zn].C1=CC=C(P(C2=CC=CC=C2)C2=CC=C[CH-]2)C=C1.C1=CC=C(P(C2=CC=CC=C2)C2=CC=C[CH-]2)C=C1.[Cl-].[Cl-].[Fe+2].[Pd+2]>>CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.[Na+].[H-].OCC1=CC=CC=C1>>CCC1=CN=C2C(=C1OCC1=CC=CC=C1)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=C(Cl)C=C2NC2=CC=CC=C2F)N(C)C1=O': [
              'C[Si](C)(C)[N-][Si](C)(C)C.[Li+].CN1C(=O)C2=C(N=C(Cl)C=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=C(Cl)C=C2NC2=CC=CC=C2F)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O': [
              'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O.NC1=CC=CC=C1F>>CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O',
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2NC2=CC=CC=C2F)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O': [
              'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O.OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CN1C(=O)C2=C(N=CC(Br)=C2O)N(C)C1=O.BrCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2OCC2=CC=CC=C2)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O',
              'O=C([O-])[O-].[K+].[K+].CI.CN(C)C=O.CN1C(=O)N=C(O)C2=C1N=CC(Br)=C2OCC1=CC=CC=C1>>CN1C(=O)C2=C(N=CC(Br)=C2OCC2=CC=CC=C2)N(C)C1=O'
            ],
            'C=CC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C': [
              'O=C([O-])[O-].[Na+].[Na+].C=C[B-](F)(F)F.CN1C(=O)C2=C(N=CC(Br)=C2NC2=CC=CC=C2F)N(C)C1=O.[K+].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C': [
              'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.C[Si](C)(C)[N-][Si](C)(C)C.[Li+].NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'O=C1CCC(=O)N1I.CCC1=CN=C2C(=C1NC1=CC=CC=C1F)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.CC1=CC=C(S(=O)(=O)O)C=C1.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'COC(=O)C1=C(Cl)C(Br)=CN=C1N=C(O)N=C(O)C(Cl)(Cl)Cl': [
              'O=C=NC(=O)C(Cl)(Cl)Cl.COC(=O)C1=C(Cl)C(Br)=CNC1=N>>COC(=O)C1=C(Cl)C(Br)=CN=C1N=C(O)N=C(O)C(Cl)(Cl)Cl'
            ],
            'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C': [
              'CCN(CC)CC.CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.O=S(=O)(N(C1=CC=CC=C1)S(=O)(=O)C(F)(F)F)C(F)(F)F>>CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C'
            ]
          }
        },
        {
          id: 1280,
          stepCount: 0,
          status: 'edited',
          createdAt: '2023-07-03T01:50:38.772Z',
          updatedAt: '2023-07-03T01:50:38.772Z',
          publishedAt: '2023-07-03T01:50:38.769Z',
          routeTree: {
            value: 'CCc1cnc2c(c1Nc1ccc(I)cc1F)c(=O)n(C)c(=O)n2C'
          },
          targetToRxns: null
        },
        {
          id: 1313,
          stepCount: 0,
          status: 'edited',
          createdAt: '2023-07-03T03:42:17.843Z',
          updatedAt: '2023-07-03T03:42:17.843Z',
          publishedAt: '2023-07-03T03:42:17.839Z',
          routeTree: {
            value: 'CCc1cnc2c(c1Nc1ccc(I)cc1F)c(=O)n(C)c(=O)n2C'
          },
          targetToRxns: null
        },
        {
          id: 1315,
          stepCount: 0,
          status: 'edited',
          createdAt: '2023-07-03T03:58:37.965Z',
          updatedAt: '2023-07-03T03:58:37.965Z',
          publishedAt: '2023-07-03T03:58:37.963Z',
          routeTree: {
            value: 'CCc1cnc2c(c1Nc1ccc(I)cc1F)c(=O)n(C)c(=O)n2C'
          },
          targetToRxns: null
        },
        {
          id: 1325,
          stepCount: 3,
          status: 'edited',
          createdAt: '2023-07-03T09:35:03.804Z',
          updatedAt: '2023-07-03T09:35:03.804Z',
          publishedAt: '2023-07-03T09:35:03.800Z',
          routeTree: {
            value: 'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
            children: [
              {
                value: 'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C',
                children: [
                  {
                    value: 'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
                    children: [
                      {
                        value: 'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
                        children: []
                      }
                    ]
                  }
                ]
              }
            ]
          },
          targetToRxns: {
            'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'Br.CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.C[O-].[Na+]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
              'CCC1=CN=C2C(=C1Cl)C(=O)N(C)C(=O)N2C.[Na]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C': [
              'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C': [
              'CCN(CC)CC.CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.O=S(=O)(N(C1=CC=CC=C1)S(=O)(=O)C(F)(F)F)C(F)(F)F>>CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C'
            ]
          }
        },
        {
          id: 1326,
          stepCount: 6,
          status: 'edited',
          createdAt: '2023-07-03T09:35:27.826Z',
          updatedAt: '2023-07-03T09:35:27.826Z',
          publishedAt: '2023-07-03T09:35:27.824Z',
          routeTree: {
            value: 'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
            children: [
              {
                value: 'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C',
                children: [
                  {
                    value: 'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
                    children: [
                      {
                        value: 'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
                        children: [
                          {
                            value: 'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
                            children: [
                              {
                                value: 'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C',
                                children: [
                                  {
                                    value:
                                      'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
                                    children: []
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },
          targetToRxns: {
            'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'Br.CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O': [
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O'
            ],
            'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C': [
              'CO.CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O>>COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C'
            ],
            'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C.[CH2]CCC.[CH2]CCC.[CH2]CCC.[CH]=C.[Sn].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C': [
              'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C': [
              'CCN(CC)CC.CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.O=S(=O)(N(C1=CC=CC=C1)S(=O)(=O)C(F)(F)F)C(F)(F)F>>CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C'
            ]
          }
        },
        {
          id: 1327,
          stepCount: 6,
          status: 'edited',
          createdAt: '2023-07-03T09:37:54.664Z',
          updatedAt: '2023-07-03T09:37:54.664Z',
          publishedAt: '2023-07-03T09:37:54.661Z',
          routeTree: {
            value: 'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C',
            children: [
              {
                value: 'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C',
                children: [
                  {
                    value: 'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C',
                    children: [
                      {
                        value: 'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
                        children: [
                          {
                            value: 'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C',
                            children: [
                              {
                                value: 'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C',
                                children: [
                                  {
                                    value:
                                      'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
                                    children: []
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },
          targetToRxns: {
            'CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C': [
              'Br.CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C>>CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C.[Pd]>>CCC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O': [
              'O=C1CCC(=O)N1Br.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O',
              'BrBr.CC(=O)O.CN1C(=O)C2=C(N=CC=C2Cl)N(C)C1=O.O>>CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O'
            ],
            'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C': [
              'CO.CN1C(=O)C2=C(N=CC(Br)=C2Cl)N(C)C1=O>>COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C'
            ],
            'C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C': [
              'COC1=C(Br)C=NC2=C1C(=O)N(C)C(=O)N2C.[CH2]CCC.[CH2]CCC.[CH2]CCC.[CH]=C.[Sn].C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.C1=CC=C([PH+](C2=CC=CC=C2)C2=CC=CC=C2)C=C1.[Pd-4]>>C=CC1=CN=C2C(=C1OC)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C': [
              'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C.NC1=C(F)C=C(I)C=C1>>CCC1=CN=C2C(=C1NC1=CC=C(I)C=C1F)C(=O)N(C)C(=O)N2C'
            ],
            'CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C': [
              'CCN(CC)CC.CCC1=CN=C2C(=C1O)C(=O)N(C)C(=O)N2C.O=S(=O)(N(C1=CC=CC=C1)S(=O)(=O)C(F)(F)F)C(F)(F)F>>CCC1=CN=C2C(=C1OS(=O)(=O)C(F)(F)F)C(=O)N(C)C(=O)N2C'
            ]
          }
        },
        {
          id: 1328,
          stepCount: 0,
          status: 'edited',
          createdAt: '2023-07-04T01:09:16.849Z',
          updatedAt: '2023-07-04T01:09:16.849Z',
          publishedAt: '2023-07-04T01:09:16.846Z',
          routeTree: {
            value: 'CCc1cnc2c(c1Nc1ccc(I)cc1F)c(=O)n(C)c(=O)n2C'
          },
          targetToRxns: null
        }
      ],
      project: {
        id: 25,
        name: '睿智报价',
        createdAt: '2023-06-13T06:24:59.259Z',
        updatedAt: '2023-06-13T06:24:59.259Z',
        publishedAt: '2023-06-13T06:24:59.257Z'
      }
    }
  ]
})
export { data }
