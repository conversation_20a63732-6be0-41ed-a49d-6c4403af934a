FROM node:18-buster-slim AS build
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV} \
  MOCK=none
WORKDIR /opt/app

RUN set -e
ENV PATH /opt/app/node_modules/.bin:$PATH
COPY ./ .
RUN yarn install --production && NODE_ENV=production yarn build

FROM 432084094746.dkr.ecr.cn-northwest-1.amazonaws.com.cn/my-nginx-with-header-more

COPY ./deploy/nginx.conf /etc/nginx/nginx.conf
COPY ./deploy/mime.types /etc/nginx/mime.types
COPY ./certs /ssl/
COPY --from=build /opt/app/dist /usr/share/nginx/html
# COPY --from=build /opt/app/dist /usr/share/nginx/html/help
