worker_processes  1;
load_module modules/ngx_http_headers_more_filter_module.so;
load_module modules/ndk_http_module.so;
load_module modules/ngx_http_lua_module.so;
events {
  worker_connections  1024;
}

http {
  include       mime.types;
  default_type  application/octet-stream;
  resolver 127.0.0.11 valid=30s;

  sendfile        on;

  keepalive_timeout  65;

  upstream brain-server-stream {
    server tasks.brain-server:1337; # Target all Strapi replicas
    ip_hash;                 # Sticky sessions based on client IP
  }

  server {
    listen 80;
    server_name _;

    access_log /var/log/nginx/data-access.log combined;
    proxy_busy_buffers_size   512k;
    proxy_buffers   4 512k;
    proxy_buffer_size   256k;
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    location / {
      try_files $uri $uri/ /index.html;
       # Ensure ETag is enabled
      etag on;
    }

    location /api/smiles/to-image {
        rewrite ^/api/smiles/to-image/(.*)$ /$1 break;
        proxy_pass http://host.docker.internal:3333;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    location /api/smiles/image-file {
        proxy_pass http://host.docker.internal:9000/smiles-svgs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    location /api/smiles/inchi {
        proxy_pass http://************:8000/smiles/inchi;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/ {
      proxy_pass http://brain-server:1337/api/;

      proxy_redirect off;
      proxy_buffering off;

      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
    }

    location ~* \.io {
      proxy_pass http://brain-server-stream;

      add_header Access-Control-Allow-Origin $http_origin;
      add_header Access-Control-Allow-Methods *;
      add_header Access-Control-Allow-Headers $http_access_control_request_headers;
      add_header Access-Control-Max-Age 60000;
      add_header Access-Control-Allow-Credentials true;

      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "Upgrade";
      proxy_read_timeout 60s;
    }

    location ~* ^.+\.(?!html$|svg$)[^.]+$ {
      # Remove Last-Modified header
      more_clear_headers 'Last-Modified';

      # Set Cache-Control for 1 year
      add_header Cache-Control "public, max-age=31536000, immutable";
      # Use Lua to compute the MD5 hash of the file name
      set $etag_from_filename '';
      rewrite_by_lua_block {
        local file_name = ngx.var.uri
        local etag = ngx.md5(file_name)
        ngx.var.etag_from_filename = etag
      }
      add_header ETag '"$etag_from_filename"';
    }

    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
      text/plain
      text/css
      text/xml
      text/javascript
      application/javascript
      application/json
      application/xhtml+xml;
  }
}
