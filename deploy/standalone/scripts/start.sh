#!/bin/bash

# Start Script for Standalone labwise-web-2 Deployment
# This script starts the nginx web server and related services

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
RUNTIME_DIR="$DEPLOY_DIR/runtime"
NGINX_DIR="$RUNTIME_DIR/nginx"
LOGS_DIR="$RUNTIME_DIR/logs"
PID_DIR="$RUNTIME_DIR/pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service is already running
check_running() {
    local service_name="$1"
    local pid_file="$PID_DIR/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # Running
        else
            # PID file exists but process is dead
            rm -f "$pid_file"
            return 1  # Not running
        fi
    fi
    return 1  # Not running
}

# Start nginx service
start_nginx() {
    log_info "Starting nginx web server..."
    
    local nginx_binary="$NGINX_DIR/bin/nginx"
    local nginx_conf="$NGINX_DIR/conf/nginx.conf"
    local nginx_pid="$PID_DIR/nginx.pid"
    
    # Check if nginx is already running
    if check_running "nginx"; then
        log_warning "Nginx is already running (PID: $(cat "$PID_DIR/nginx.pid"))"
        return 0
    fi
    
    # Verify nginx binary exists
    if [[ ! -f "$nginx_binary" ]]; then
        log_error "Nginx binary not found: $nginx_binary"
        log_error "Please run the deployment script first: ./deploy.sh"
        exit 1
    fi
    
    # Verify configuration file exists
    if [[ ! -f "$nginx_conf" ]]; then
        log_error "Nginx configuration not found: $nginx_conf"
        log_error "Please run the deployment script first: ./deploy.sh"
        exit 1
    fi
    
    # Test nginx configuration
    log_info "Testing nginx configuration..."
    if ! "$nginx_binary" -t -c "$nginx_conf" -p "$NGINX_DIR" 2>/dev/null; then
        log_error "Nginx configuration test failed"
        log_error "Please check the configuration file: $nginx_conf"
        exit 1
    fi
    
    # Create log files if they don't exist
    mkdir -p "$LOGS_DIR"
    touch "$LOGS_DIR/nginx.log" "$LOGS_DIR/nginx-error.log"
    
    # Start nginx
    log_info "Starting nginx process..."
    if "$nginx_binary" -c "$nginx_conf" -p "$NGINX_DIR"; then
        # Wait a moment for nginx to start
        sleep 2
        
        # Check if nginx started successfully
        if pgrep -f "nginx.*$nginx_conf" > /dev/null; then
            local nginx_pid
            nginx_pid=$(pgrep -f "nginx.*$nginx_conf" | head -1)
            echo "$nginx_pid" > "$nginx_pid"
            log_success "Nginx started successfully (PID: $nginx_pid)"
            
            # Get the port from configuration
            local port
            port=$(grep -o 'listen [0-9]*' "$nginx_conf" | head -1 | awk '{print $2}')
            log_info "Web server is accessible at: http://localhost:${port:-1555}"
        else
            log_error "Nginx failed to start"
            exit 1
        fi
    else
        log_error "Failed to start nginx"
        exit 1
    fi
}

# Perform health check
health_check() {
    log_info "Performing health check..."
    
    # Get port from nginx configuration
    local nginx_conf="$NGINX_DIR/conf/nginx.conf"
    local port
    port=$(grep -o 'listen [0-9]*' "$nginx_conf" | head -1 | awk '{print $2}')
    port=${port:-1555}
    
    # Wait for service to be ready
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if command -v curl &> /dev/null; then
            if curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
                log_success "Health check passed - service is responding"
                return 0
            fi
        elif command -v wget &> /dev/null; then
            if wget -q --spider "http://localhost:$port" 2>/dev/null; then
                log_success "Health check passed - service is responding"
                return 0
            fi
        else
            # Fallback: check if port is listening
            if command -v netstat &> /dev/null; then
                if netstat -tuln | grep -q ":$port "; then
                    log_success "Health check passed - port is listening"
                    return 0
                fi
            fi
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 2
        ((attempt++))
    done
    
    log_warning "Health check failed after $max_attempts attempts"
    log_warning "Service may still be starting up or there may be an issue"
    return 1
}

# Show service status
show_status() {
    log_info "Service Status:"
    
    if check_running "nginx"; then
        local pid
        pid=$(cat "$PID_DIR/nginx.pid")
        log_success "  Nginx: Running (PID: $pid)"
        
        # Show memory usage if possible
        if command -v ps &> /dev/null; then
            local memory
            memory=$(ps -o rss= -p "$pid" 2>/dev/null | awk '{print int($1/1024)"MB"}' || echo "unknown")
            log_info "    Memory usage: $memory"
        fi
    else
        log_warning "  Nginx: Not running"
    fi
    
    # Show log file locations
    log_info "Log files:"
    log_info "  Access log: $LOGS_DIR/nginx.log"
    log_info "  Error log: $LOGS_DIR/nginx-error.log"
}

# Main function
main() {
    log_info "Starting labwise-web-2 services..."
    
    # Ensure runtime directories exist
    mkdir -p "$LOGS_DIR" "$PID_DIR"
    
    # Start services
    start_nginx
    
    # Perform health check
    health_check
    
    # Show status
    show_status
    
    log_success "All services started successfully!"
    log_info ""
    log_info "Management commands:"
    log_info "  Check status: ./status.sh"
    log_info "  Stop services: ./stop.sh"
    log_info "  View logs: tail -f $LOGS_DIR/nginx.log"
}

# Execute main function
main "$@"
