#!/bin/bash

# Build and Package Script for Standalone Deployment
# This script builds the React application and creates a self-contained deployment package

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
STANDALONE_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/dist"
PACKAGE_DIR="$PROJECT_ROOT/labwise-web-standalone"
NGINX_VERSION="1.24.0"
NGINX_MODULES_VERSION="0.37"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_ROOT/package.json" ]]; then
        log_error "package.json not found. Please run this script from the project root or ensure PROJECT_ROOT is correct."
        exit 1
    fi
    
    # Check required tools
    local required_tools=("node" "yarn" "curl" "tar")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is required but not installed."
            exit 1
        fi
    done
    
    log_success "Prerequisites check passed"
}

# Build React application
build_react_app() {
    log_info "Building React application..."
    
    cd "$PROJECT_ROOT"
    
    # Install dependencies if node_modules doesn't exist
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing dependencies..."
        yarn install --production
    fi
    
    # Build the application
    log_info "Building production bundle..."
    NODE_ENV=production yarn build
    
    if [[ ! -d "$BUILD_DIR" ]]; then
        log_error "Build failed - dist directory not found"
        exit 1
    fi
    
    log_success "React application built successfully"
}

# Download and prepare portable nginx
prepare_nginx() {
    log_info "Preparing portable nginx runtime..."

    local nginx_dir="$PACKAGE_DIR/runtime/nginx"
    mkdir -p "$nginx_dir"

    # Use the nginx installer utility
    log_info "Installing portable nginx with required modules..."
    if ! "$STANDALONE_DIR/scripts/utils/nginx-installer.sh" "$nginx_dir"; then
        log_error "Failed to install nginx"
        exit 1
    fi

    log_info "Setting up nginx configuration..."

    # Copy nginx configuration template
    cp "$STANDALONE_DIR/templates/nginx.conf.template" "$nginx_dir/conf/"
    cp "$PROJECT_ROOT/deploy/mime.types" "$nginx_dir/conf/"

    log_success "Nginx runtime prepared"
}

# Create deployment package
create_package() {
    log_info "Creating deployment package..."
    
    # Clean up any existing package directory
    if [[ -d "$PACKAGE_DIR" ]]; then
        rm -rf "$PACKAGE_DIR"
    fi
    
    mkdir -p "$PACKAGE_DIR"
    
    # Copy built application
    log_info "Copying built application..."
    cp -r "$BUILD_DIR" "$PACKAGE_DIR/"
    
    # Copy standalone deployment files
    log_info "Copying deployment scripts..."
    cp -r "$STANDALONE_DIR/scripts" "$PACKAGE_DIR/"
    cp -r "$STANDALONE_DIR/templates" "$PACKAGE_DIR/"
    cp "$STANDALONE_DIR/README.md" "$PACKAGE_DIR/"
    
    # Create runtime directory structure
    mkdir -p "$PACKAGE_DIR/runtime"/{logs,pid,tmp}
    
    # Prepare nginx
    prepare_nginx
    
    # Create version info
    cat > "$PACKAGE_DIR/VERSION" << EOF
Package: labwise-web-standalone
Version: $(date +%Y%m%d-%H%M%S)
Build Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
Git Commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
Node Version: $(node --version)
EOF
    
    log_success "Deployment package created at $PACKAGE_DIR"
}

# Create compressed archive
create_archive() {
    log_info "Creating compressed archive..."
    
    cd "$PROJECT_ROOT"
    local archive_name="labwise-web-standalone-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    tar -czf "$archive_name" -C "$(dirname "$PACKAGE_DIR")" "$(basename "$PACKAGE_DIR")"
    
    log_success "Archive created: $archive_name"
    log_info "Archive size: $(du -h "$archive_name" | cut -f1)"
}

# Main execution
main() {
    log_info "Starting build and package process..."
    
    check_prerequisites
    build_react_app
    create_package
    create_archive
    
    log_success "Build and package process completed successfully!"
    log_info "Next steps:"
    log_info "1. Copy the archive to your target environment"
    log_info "2. Extract: tar -xzf labwise-web-standalone-*.tar.gz"
    log_info "3. Deploy: cd labwise-web-standalone && ./scripts/deploy.sh"
}

# Execute main function
main "$@"
