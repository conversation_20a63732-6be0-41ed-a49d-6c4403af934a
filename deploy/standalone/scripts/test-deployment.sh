#!/bin/bash

# Test Script for Standalone labwise-web-2 Deployment
# This script performs comprehensive testing of the deployment

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
PROJECT_ROOT="$(cd "$DEPLOY_DIR/../.." && pwd)"

# Test configuration
TEST_PORT=18555  # Use different port for testing
TEST_ENV="development"
TEST_TIMEOUT=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
TEST_RESULTS=()

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test result functions
test_pass() {
    local test_name="$1"
    local message="${2:-}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
    TEST_RESULTS+=("PASS: $test_name${message:+ - $message}")
    log_success "✓ $test_name${message:+ - $message}"
}

test_fail() {
    local test_name="$1"
    local message="${2:-}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
    TEST_RESULTS+=("FAIL: $test_name${message:+ - $message}")
    log_error "✗ $test_name${message:+ - $message}"
}

# Show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Test the labwise-web-2 standalone deployment.

OPTIONS:
    --port PORT            Test port (default: $TEST_PORT)
    --env ENVIRONMENT      Test environment (default: $TEST_ENV)
    --timeout SECONDS      Test timeout (default: $TEST_TIMEOUT)
    --skip-build          Skip build test
    --skip-deploy         Skip deployment test
    --skip-service        Skip service test
    --help                Show this help message

EXAMPLES:
    # Run all tests
    $0

    # Run tests with custom port
    $0 --port 19555

    # Skip build test (if already built)
    $0 --skip-build

EOF
}

# Parse command line arguments
parse_arguments() {
    local skip_build=false
    local skip_deploy=false
    local skip_service=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --port)
                TEST_PORT="$2"
                shift 2
                ;;
            --env)
                TEST_ENV="$2"
                shift 2
                ;;
            --timeout)
                TEST_TIMEOUT="$2"
                shift 2
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-deploy)
                skip_deploy=true
                shift
                ;;
            --skip-service)
                skip_service=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Export skip flags for use in test functions
    export SKIP_BUILD="$skip_build"
    export SKIP_DEPLOY="$skip_deploy"
    export SKIP_SERVICE="$skip_service"
}

# Test prerequisites
test_prerequisites() {
    log_info "Testing prerequisites..."
    
    # Check required tools
    local required_tools=("curl" "tar" "bash" "jq")
    for tool in "${required_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            test_pass "Tool available: $tool"
        else
            test_fail "Tool missing: $tool"
        fi
    done
    
    # Check Node.js and yarn (for build test)
    if [[ "$SKIP_BUILD" != "true" ]]; then
        if command -v node &> /dev/null; then
            local node_version
            node_version=$(node --version)
            test_pass "Node.js available" "$node_version"
        else
            test_fail "Node.js not available"
        fi
        
        if command -v yarn &> /dev/null; then
            local yarn_version
            yarn_version=$(yarn --version)
            test_pass "Yarn available" "$yarn_version"
        else
            test_fail "Yarn not available"
        fi
    fi
    
    # Check disk space
    local available_space
    available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    if [[ "$available_space" -gt 1048576 ]]; then  # 1GB in KB
        test_pass "Sufficient disk space" "$(($available_space / 1024 / 1024))GB available"
    else
        test_fail "Insufficient disk space" "$(($available_space / 1024 / 1024))GB available"
    fi
    
    # Check port availability
    if ! netstat -tuln 2>/dev/null | grep -q ":$TEST_PORT "; then
        test_pass "Test port available" "Port $TEST_PORT is free"
    else
        test_fail "Test port in use" "Port $TEST_PORT is already in use"
    fi
}

# Test build process
test_build() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log_info "Skipping build test"
        return 0
    fi
    
    log_info "Testing build process..."
    
    cd "$PROJECT_ROOT"
    
    # Test package.json exists
    if [[ -f "package.json" ]]; then
        test_pass "Package.json exists"
    else
        test_fail "Package.json not found"
        return 1
    fi
    
    # Test yarn install
    if yarn install --production --silent; then
        test_pass "Dependencies installed"
    else
        test_fail "Failed to install dependencies"
        return 1
    fi
    
    # Test build
    if NODE_ENV=production yarn build; then
        test_pass "Build completed"
    else
        test_fail "Build failed"
        return 1
    fi
    
    # Test build output
    if [[ -d "dist" ]] && [[ -f "dist/index.html" ]]; then
        test_pass "Build output exists"
    else
        test_fail "Build output missing"
        return 1
    fi
    
    # Test build package creation
    if "$DEPLOY_DIR/scripts/build-package.sh"; then
        test_pass "Deployment package created"
    else
        test_fail "Failed to create deployment package"
        return 1
    fi
    
    # Test package contents
    local package_dir="$PROJECT_ROOT/labwise-web-standalone"
    if [[ -d "$package_dir" ]]; then
        test_pass "Package directory exists"
        
        # Check required files
        local required_files=(
            "dist/index.html"
            "scripts/deploy.sh"
            "scripts/start.sh"
            "scripts/stop.sh"
            "scripts/status.sh"
            "templates/nginx.conf.template"
        )
        
        for file in "${required_files[@]}"; do
            if [[ -f "$package_dir/$file" ]]; then
                test_pass "Package file exists: $file"
            else
                test_fail "Package file missing: $file"
            fi
        done
    else
        test_fail "Package directory not created"
    fi
}

# Test deployment process
test_deployment() {
    if [[ "$SKIP_DEPLOY" == "true" ]]; then
        log_info "Skipping deployment test"
        return 0
    fi
    
    log_info "Testing deployment process..."
    
    local package_dir="$PROJECT_ROOT/labwise-web-standalone"
    
    if [[ ! -d "$package_dir" ]]; then
        test_fail "Package directory not found for deployment test"
        return 1
    fi
    
    cd "$package_dir"
    
    # Test deployment script exists and is executable
    if [[ -x "scripts/deploy.sh" ]]; then
        test_pass "Deployment script is executable"
    else
        test_fail "Deployment script not executable"
        return 1
    fi
    
    # Test deployment
    if ./scripts/deploy.sh --port "$TEST_PORT" --env "$TEST_ENV"; then
        test_pass "Deployment completed"
    else
        test_fail "Deployment failed"
        return 1
    fi
    
    # Test deployment artifacts
    local required_dirs=(
        "runtime/nginx/bin"
        "runtime/nginx/conf"
        "runtime/logs"
        "runtime/pid"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            test_pass "Runtime directory exists: $dir"
        else
            test_fail "Runtime directory missing: $dir"
        fi
    done
    
    # Test configuration file
    if [[ -f "runtime/nginx/conf/nginx.conf" ]]; then
        test_pass "Nginx configuration generated"
        
        # Check if port is correctly configured
        if grep -q "listen $TEST_PORT" "runtime/nginx/conf/nginx.conf"; then
            test_pass "Port correctly configured in nginx.conf"
        else
            test_fail "Port not correctly configured in nginx.conf"
        fi
    else
        test_fail "Nginx configuration not generated"
    fi
}

# Test service operations
test_service() {
    if [[ "$SKIP_SERVICE" == "true" ]]; then
        log_info "Skipping service test"
        return 0
    fi
    
    log_info "Testing service operations..."
    
    local package_dir="$PROJECT_ROOT/labwise-web-standalone"
    
    if [[ ! -d "$package_dir" ]]; then
        test_fail "Package directory not found for service test"
        return 1
    fi
    
    cd "$package_dir"
    
    # Test service start
    if ./start.sh; then
        test_pass "Service started"
        sleep 3  # Give service time to start
    else
        test_fail "Failed to start service"
        return 1
    fi
    
    # Test service status
    if ./status.sh > /dev/null 2>&1; then
        test_pass "Service status check"
    else
        test_fail "Service status check failed"
    fi
    
    # Test HTTP connectivity
    local max_attempts=10
    local attempt=1
    local connected=false
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s -f --max-time 5 "http://localhost:$TEST_PORT" > /dev/null 2>&1; then
            connected=true
            break
        fi
        sleep 2
        ((attempt++))
    done
    
    if [[ "$connected" == "true" ]]; then
        test_pass "HTTP connectivity" "Service responding on port $TEST_PORT"
    else
        test_fail "HTTP connectivity" "Service not responding on port $TEST_PORT"
    fi
    
    # Test health endpoint
    if curl -s -f --max-time 5 "http://localhost:$TEST_PORT/health" | grep -q "healthy"; then
        test_pass "Health endpoint" "Health check endpoint responding"
    else
        test_fail "Health endpoint" "Health check endpoint not responding"
    fi
    
    # Test health check script
    if ./scripts/health-check.sh --timeout 10; then
        test_pass "Health check script"
    else
        test_fail "Health check script"
    fi
    
    # Test service stop
    if ./stop.sh; then
        test_pass "Service stopped"
        sleep 2  # Give service time to stop
    else
        test_fail "Failed to stop service"
    fi
    
    # Verify service is actually stopped
    if ! curl -s -f --max-time 5 "http://localhost:$TEST_PORT" > /dev/null 2>&1; then
        test_pass "Service properly stopped"
    else
        test_fail "Service still responding after stop"
    fi
}

# Test monitoring features
test_monitoring() {
    log_info "Testing monitoring features..."
    
    local package_dir="$PROJECT_ROOT/labwise-web-standalone"
    
    if [[ ! -d "$package_dir" ]]; then
        test_fail "Package directory not found for monitoring test"
        return 0
    fi
    
    cd "$package_dir"
    
    # Test monitoring script exists
    if [[ -x "scripts/monitor.sh" ]]; then
        test_pass "Monitoring script exists and is executable"
    else
        test_fail "Monitoring script not found or not executable"
        return 0
    fi
    
    # Test monitoring check command
    if ./scripts/monitor.sh check > /dev/null 2>&1; then
        test_pass "Monitoring check command"
    else
        test_fail "Monitoring check command failed"
    fi
    
    # Test monitoring status command
    if ./scripts/monitor.sh status > /dev/null 2>&1; then
        test_pass "Monitoring status command"
    else
        test_fail "Monitoring status command failed"
    fi
}

# Cleanup test environment
cleanup_test() {
    log_info "Cleaning up test environment..."
    
    local package_dir="$PROJECT_ROOT/labwise-web-standalone"
    
    if [[ -d "$package_dir" ]]; then
        cd "$package_dir"
        
        # Stop any running services
        ./stop.sh > /dev/null 2>&1 || true
        
        # Clean up test files
        cd "$PROJECT_ROOT"
        rm -rf "$package_dir"
        rm -f labwise-web-standalone-*.tar.gz
        
        log_success "Test environment cleaned up"
    fi
}

# Show test results
show_results() {
    echo
    log_info "Test Results Summary"
    log_info "===================="
    
    for result in "${TEST_RESULTS[@]}"; do
        if [[ "$result" == PASS:* ]]; then
            echo -e "${GREEN}$result${NC}"
        else
            echo -e "${RED}$result${NC}"
        fi
    done
    
    echo
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    log_info "Total tests: $total_tests"
    log_success "Passed: $TESTS_PASSED"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        log_error "Failed: $TESTS_FAILED"
        echo
        log_error "Some tests failed. Please review the output above and check the troubleshooting guide."
        return 1
    else
        echo
        log_success "All tests passed! The deployment is working correctly."
        return 0
    fi
}

# Main function
main() {
    log_info "Starting labwise-web-2 standalone deployment tests..."
    echo
    
    parse_arguments "$@"
    
    # Run tests
    test_prerequisites
    test_build
    test_deployment
    test_service
    test_monitoring
    
    # Show results
    if show_results; then
        log_success "Testing completed successfully!"
        exit 0
    else
        log_error "Testing completed with failures!"
        exit 1
    fi
}

# Cleanup on exit
trap cleanup_test EXIT

# Execute main function
main "$@"
