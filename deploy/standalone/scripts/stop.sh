#!/bin/bash

# Stop Script for Standalone labwise-web-2 Deployment
# This script stops the nginx web server and related services

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
RUNTIME_DIR="$DEPLOY_DIR/runtime"
PID_DIR="$RUNTIME_DIR/pid"
LOGS_DIR="$RUNTIME_DIR/logs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Stop a service by PID file
stop_service() {
    local service_name="$1"
    local pid_file="$PID_DIR/${service_name}.pid"
    local timeout="${2:-10}"  # Default timeout 10 seconds
    
    if [[ ! -f "$pid_file" ]]; then
        log_warning "$service_name is not running (no PID file found)"
        return 0
    fi
    
    local pid
    pid=$(cat "$pid_file")
    
    # Check if process is actually running
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warning "$service_name PID file exists but process is not running"
        rm -f "$pid_file"
        return 0
    fi
    
    log_info "Stopping $service_name (PID: $pid)..."
    
    # Try graceful shutdown first
    if kill -TERM "$pid" 2>/dev/null; then
        # Wait for graceful shutdown
        local count=0
        while kill -0 "$pid" 2>/dev/null && [[ $count -lt $timeout ]]; do
            sleep 1
            ((count++))
        done
        
        # Check if process stopped
        if ! kill -0 "$pid" 2>/dev/null; then
            log_success "$service_name stopped gracefully"
            rm -f "$pid_file"
            return 0
        fi
        
        # If still running, force kill
        log_warning "$service_name did not stop gracefully, forcing shutdown..."
        if kill -KILL "$pid" 2>/dev/null; then
            sleep 2
            if ! kill -0 "$pid" 2>/dev/null; then
                log_success "$service_name force stopped"
                rm -f "$pid_file"
                return 0
            else
                log_error "Failed to stop $service_name"
                return 1
            fi
        else
            log_error "Failed to send kill signal to $service_name"
            return 1
        fi
    else
        log_error "Failed to send termination signal to $service_name"
        return 1
    fi
}

# Stop nginx service
stop_nginx() {
    log_info "Stopping nginx web server..."
    
    # Try to stop using PID file first
    if stop_service "nginx"; then
        return 0
    fi
    
    # Fallback: try to find and stop nginx processes
    log_info "Attempting to stop nginx processes by name..."
    local nginx_pids
    nginx_pids=$(pgrep -f "nginx.*conf/nginx.conf" 2>/dev/null || true)
    
    if [[ -n "$nginx_pids" ]]; then
        log_info "Found nginx processes: $nginx_pids"
        for pid in $nginx_pids; do
            if kill -TERM "$pid" 2>/dev/null; then
                log_info "Sent termination signal to nginx process $pid"
            fi
        done
        
        # Wait for processes to stop
        sleep 3
        
        # Check if any nginx processes are still running
        nginx_pids=$(pgrep -f "nginx.*conf/nginx.conf" 2>/dev/null || true)
        if [[ -n "$nginx_pids" ]]; then
            log_warning "Some nginx processes still running, force killing..."
            for pid in $nginx_pids; do
                kill -KILL "$pid" 2>/dev/null || true
            done
        fi
        
        log_success "Nginx processes stopped"
    else
        log_info "No nginx processes found"
    fi
}

# Clean up temporary files
cleanup() {
    log_info "Cleaning up temporary files..."
    
    # Clean up PID files for stopped processes
    if [[ -d "$PID_DIR" ]]; then
        for pid_file in "$PID_DIR"/*.pid; do
            if [[ -f "$pid_file" ]]; then
                local pid
                pid=$(cat "$pid_file" 2>/dev/null || echo "")
                if [[ -n "$pid" ]] && ! kill -0 "$pid" 2>/dev/null; then
                    rm -f "$pid_file"
                    log_info "Removed stale PID file: $(basename "$pid_file")"
                fi
            fi
        done
    fi
    
    # Clean up temporary nginx files
    local nginx_tmp="$RUNTIME_DIR/nginx/tmp"
    if [[ -d "$nginx_tmp" ]]; then
        find "$nginx_tmp" -type f -name "*.tmp" -mtime +1 -delete 2>/dev/null || true
    fi
    
    log_success "Cleanup completed"
}

# Show final status
show_final_status() {
    log_info "Final service status:"
    
    local any_running=false
    
    # Check nginx
    if [[ -f "$PID_DIR/nginx.pid" ]]; then
        local pid
        pid=$(cat "$PID_DIR/nginx.pid")
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "  Nginx: Still running (PID: $pid)"
            any_running=true
        else
            log_success "  Nginx: Stopped"
        fi
    else
        log_success "  Nginx: Stopped"
    fi
    
    if [[ "$any_running" == "true" ]]; then
        log_warning "Some services are still running. You may need to stop them manually."
        return 1
    else
        log_success "All services stopped successfully"
        return 0
    fi
}

# Main function
main() {
    log_info "Stopping labwise-web-2 services..."
    
    # Stop services
    stop_nginx
    
    # Clean up
    cleanup
    
    # Show final status
    if show_final_status; then
        log_success "All services stopped successfully!"
    else
        log_warning "Some services may still be running"
        exit 1
    fi
    
    log_info ""
    log_info "To start services again, run: ./start.sh"
}

# Handle script interruption
trap 'log_warning "Stop script interrupted"; exit 1' INT TERM

# Execute main function
main "$@"
