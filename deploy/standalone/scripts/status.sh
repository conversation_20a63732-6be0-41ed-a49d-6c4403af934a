#!/bin/bash

# Status Script for Standalone labwise-web-2 Deployment
# This script checks the status of all services and provides health information

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
RUNTIME_DIR="$DEPLOY_DIR/runtime"
NGINX_DIR="$RUNTIME_DIR/nginx"
LOGS_DIR="$RUNTIME_DIR/logs"
PID_DIR="$RUNTIME_DIR/pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service is running
check_service_status() {
    local service_name="$1"
    local pid_file="$PID_DIR/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "running:$pid"
        else
            echo "dead"
        fi
    else
        echo "stopped"
    fi
}

# Get process information
get_process_info() {
    local pid="$1"
    local info=""
    
    if command -v ps &> /dev/null; then
        # Get memory usage
        local memory
        memory=$(ps -o rss= -p "$pid" 2>/dev/null | awk '{print int($1/1024)"MB"}' || echo "unknown")
        info="$info Memory: $memory"
        
        # Get CPU usage
        local cpu
        cpu=$(ps -o %cpu= -p "$pid" 2>/dev/null | awk '{print $1"%"}' || echo "unknown")
        info="$info, CPU: $cpu"
        
        # Get start time
        local start_time
        start_time=$(ps -o lstart= -p "$pid" 2>/dev/null | awk '{print $1" "$2" "$3" "$4}' || echo "unknown")
        info="$info, Started: $start_time"
    fi
    
    echo "$info"
}

# Check nginx status
check_nginx_status() {
    local status
    status=$(check_service_status "nginx")
    
    case "$status" in
        running:*)
            local pid="${status#running:}"
            log_success "Nginx: Running (PID: $pid)"
            local info
            info=$(get_process_info "$pid")
            if [[ -n "$info" ]]; then
                log_info "  $info"
            fi
            
            # Check if nginx is responding
            check_nginx_health "$pid"
            ;;
        dead)
            log_error "Nginx: Dead (PID file exists but process not running)"
            ;;
        stopped)
            log_warning "Nginx: Stopped"
            ;;
    esac
}

# Check nginx health
check_nginx_health() {
    local pid="$1"
    
    # Get port from nginx configuration
    local nginx_conf="$NGINX_DIR/conf/nginx.conf"
    if [[ ! -f "$nginx_conf" ]]; then
        log_warning "  Health check skipped: nginx.conf not found"
        return
    fi
    
    local port
    port=$(grep -o 'listen [0-9]*' "$nginx_conf" | head -1 | awk '{print $2}' || echo "1555")
    
    # Test HTTP response
    if command -v curl &> /dev/null; then
        if curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
            log_success "  Health check: Responding on port $port"
        else
            log_warning "  Health check: Not responding on port $port"
        fi
    elif command -v wget &> /dev/null; then
        if wget -q --spider "http://localhost:$port" 2>/dev/null; then
            log_success "  Health check: Responding on port $port"
        else
            log_warning "  Health check: Not responding on port $port"
        fi
    else
        # Check if port is listening
        if command -v netstat &> /dev/null; then
            if netstat -tuln | grep -q ":$port "; then
                log_success "  Health check: Port $port is listening"
            else
                log_warning "  Health check: Port $port is not listening"
            fi
        else
            log_info "  Health check: Unable to verify (no curl/wget/netstat available)"
        fi
    fi
}

# Show log information
show_log_info() {
    log_info "Log Files:"
    
    local log_files=(
        "$LOGS_DIR/nginx.log:Access Log"
        "$LOGS_DIR/nginx-error.log:Error Log"
    )
    
    for log_entry in "${log_files[@]}"; do
        local log_file="${log_entry%:*}"
        local log_desc="${log_entry#*:}"
        
        if [[ -f "$log_file" ]]; then
            local size
            size=$(du -h "$log_file" 2>/dev/null | cut -f1 || echo "unknown")
            local lines
            lines=$(wc -l < "$log_file" 2>/dev/null || echo "unknown")
            log_info "  $log_desc: $log_file ($size, $lines lines)"
            
            # Show recent errors if any
            if [[ "$log_file" == *"error"* ]] && [[ -s "$log_file" ]]; then
                local recent_errors
                recent_errors=$(tail -5 "$log_file" 2>/dev/null | grep -i error | wc -l || echo "0")
                if [[ "$recent_errors" -gt 0 ]]; then
                    log_warning "    Recent errors: $recent_errors (last 5 lines)"
                fi
            fi
        else
            log_warning "  $log_desc: Not found ($log_file)"
        fi
    done
}

# Show system information
show_system_info() {
    log_info "System Information:"
    
    # Disk usage
    if command -v df &> /dev/null; then
        local disk_usage
        disk_usage=$(df -h "$DEPLOY_DIR" 2>/dev/null | awk 'NR==2 {print $4" available ("$5" used)"}' || echo "unknown")
        log_info "  Disk space: $disk_usage"
    fi
    
    # Memory usage
    if command -v free &> /dev/null; then
        local memory_info
        memory_info=$(free -h 2>/dev/null | awk 'NR==2 {print $7" available ("$3" used)"}' || echo "unknown")
        log_info "  Memory: $memory_info"
    fi
    
    # Load average
    if [[ -f /proc/loadavg ]]; then
        local load_avg
        load_avg=$(cut -d' ' -f1-3 /proc/loadavg 2>/dev/null || echo "unknown")
        log_info "  Load average: $load_avg"
    fi
    
    # Uptime
    if command -v uptime &> /dev/null; then
        local uptime_info
        uptime_info=$(uptime 2>/dev/null | sed 's/.*up //' | sed 's/,.*//' || echo "unknown")
        log_info "  System uptime: $uptime_info"
    fi
}

# Show configuration summary
show_config_summary() {
    log_info "Configuration Summary:"
    
    local nginx_conf="$NGINX_DIR/conf/nginx.conf"
    if [[ -f "$nginx_conf" ]]; then
        # Extract key configuration values
        local port
        port=$(grep -o 'listen [0-9]*' "$nginx_conf" | head -1 | awk '{print $2}' || echo "unknown")
        log_info "  HTTP Port: $port"
        
        local worker_processes
        worker_processes=$(grep -o 'worker_processes [0-9]*' "$nginx_conf" | awk '{print $2}' || echo "unknown")
        log_info "  Worker processes: $worker_processes"
        
        local root_dir
        root_dir=$(grep -o 'root [^;]*' "$nginx_conf" | head -1 | awk '{print $2}' || echo "unknown")
        log_info "  Document root: $root_dir"
    else
        log_warning "  Configuration file not found"
    fi
    
    # Show deployment info if available
    if [[ -f "$DEPLOY_DIR/VERSION" ]]; then
        log_info "  Version info: $DEPLOY_DIR/VERSION"
    fi
}

# Main function
main() {
    log_info "labwise-web-2 Standalone Deployment Status"
    log_info "=========================================="
    echo
    
    # Check service status
    log_info "Service Status:"
    check_nginx_status
    echo
    
    # Show configuration
    show_config_summary
    echo
    
    # Show log information
    show_log_info
    echo
    
    # Show system information
    show_system_info
    echo
    
    log_info "Management Commands:"
    log_info "  Start services: ./start.sh"
    log_info "  Stop services: ./stop.sh"
    log_info "  View access logs: tail -f $LOGS_DIR/nginx.log"
    log_info "  View error logs: tail -f $LOGS_DIR/nginx-error.log"
}

# Execute main function
main "$@"
