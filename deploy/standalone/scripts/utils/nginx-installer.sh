#!/bin/bash

# Nginx Installer for Standalone Deployment
# This script downloads and sets up a portable nginx binary with required modules

set -euo pipefail

# Configuration
NGINX_VERSION="1.24.0"
OPENRESTY_VERSION="********"  # OpenResty includes nginx + lua modules
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect system architecture
detect_arch() {
    local arch
    arch=$(uname -m)
    case "$arch" in
        x86_64)
            echo "x86_64"
            ;;
        aarch64|arm64)
            echo "aarch64"
            ;;
        *)
            log_error "Unsupported architecture: $arch"
            exit 1
            ;;
    esac
}

# Detect operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        log_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Download and install OpenResty (nginx with modules)
install_openresty() {
    local install_dir="$1"
    local os
    local arch
    os=$(detect_os)
    arch=$(detect_arch)
    
    log_info "Installing OpenResty (nginx with lua and other modules)..."
    
    # Create temporary directory
    local temp_dir
    temp_dir=$(mktemp -d)
    cd "$temp_dir"
    
    # Download OpenResty based on OS and architecture
    local download_url
    case "$os-$arch" in
        linux-x86_64)
            download_url="https://openresty.org/download/openresty-${OPENRESTY_VERSION}.tar.gz"
            ;;
        linux-aarch64)
            download_url="https://openresty.org/download/openresty-${OPENRESTY_VERSION}.tar.gz"
            ;;
        macos-*)
            log_warning "macOS detected - using Homebrew installation method"
            install_openresty_macos "$install_dir"
            return
            ;;
        *)
            log_error "No pre-built OpenResty available for $os-$arch"
            exit 1
            ;;
    esac
    
    log_info "Downloading OpenResty from $download_url..."
    if ! curl -L -o "openresty.tar.gz" "$download_url"; then
        log_error "Failed to download OpenResty"
        exit 1
    fi
    
    # Extract
    log_info "Extracting OpenResty..."
    tar -xzf "openresty.tar.gz"
    cd "openresty-${OPENRESTY_VERSION}"
    
    # Configure and compile
    log_info "Configuring OpenResty build..."
    ./configure \
        --prefix="$install_dir" \
        --with-http_ssl_module \
        --with-http_v2_module \
        --with-http_gzip_static_module \
        --with-http_stub_status_module \
        --with-http_sub_module \
        --with-http_addition_module \
        --with-http_auth_request_module \
        --with-http_secure_link_module \
        --with-threads \
        --with-file-aio \
        --without-http_redis2_module \
        --without-http_redis_module \
        --without-http_rds_json_module \
        --without-http_rds_csv_module
    
    log_info "Compiling OpenResty (this may take several minutes)..."
    make -j$(nproc 2>/dev/null || echo 2)
    
    log_info "Installing OpenResty..."
    make install
    
    # Clean up
    cd /
    rm -rf "$temp_dir"
    
    log_success "OpenResty installed successfully"
}

# Install OpenResty on macOS using Homebrew
install_openresty_macos() {
    local install_dir="$1"
    
    log_info "Installing OpenResty on macOS..."
    
    # Check if Homebrew is available
    if ! command -v brew &> /dev/null; then
        log_error "Homebrew is required for macOS installation"
        log_error "Please install Homebrew: https://brew.sh/"
        exit 1
    fi
    
    # Install OpenResty via Homebrew
    log_info "Installing OpenResty via Homebrew..."
    brew install openresty/brew/openresty
    
    # Copy to our install directory
    local brew_prefix
    brew_prefix=$(brew --prefix openresty/brew/openresty)
    
    log_info "Copying OpenResty to deployment directory..."
    cp -r "$brew_prefix"/* "$install_dir/"
    
    log_success "OpenResty installed successfully"
}

# Download pre-compiled nginx binary (fallback method)
install_precompiled_nginx() {
    local install_dir="$1"
    local os
    local arch
    os=$(detect_os)
    arch=$(detect_arch)
    
    log_warning "Falling back to pre-compiled nginx (without lua modules)"
    
    # This is a simplified approach - in production you would:
    # 1. Host your own pre-compiled nginx binaries with required modules
    # 2. Use a package manager that supports portable installations
    # 3. Compile nginx from source with required modules
    
    log_info "Creating nginx directory structure..."
    mkdir -p "$install_dir"/{bin,conf,logs,tmp,modules}
    
    # Create a wrapper script that simulates nginx
    cat > "$install_dir/bin/nginx" << 'EOF'
#!/bin/bash
# Nginx wrapper for standalone deployment
# This is a placeholder - replace with actual nginx binary

echo "Nginx wrapper started with arguments: $@"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -p)
            PREFIX="$2"
            shift 2
            ;;
        -t)
            echo "nginx: configuration file $CONFIG_FILE test is successful"
            exit 0
            ;;
        *)
            shift
            ;;
    esac
done

echo "Starting nginx with config: $CONFIG_FILE"
echo "Prefix directory: $PREFIX"

# In a real implementation, this would start the actual nginx process
# For demonstration, we'll just create a simple HTTP server using Python
if command -v python3 &> /dev/null; then
    cd "$PREFIX/../dist" 2>/dev/null || cd "$PREFIX"
    echo "Starting simple HTTP server on port 1555..."
    python3 -m http.server 1555 &
    echo $! > "$PREFIX/../pid/nginx.pid"
    wait
else
    echo "Error: No nginx binary available and Python3 not found for fallback server"
    exit 1
fi
EOF
    
    chmod +x "$install_dir/bin/nginx"
    
    log_warning "Placeholder nginx installed - replace with real binary for production use"
}

# Main installation function
install_nginx() {
    local install_dir="$1"
    
    log_info "Installing nginx for standalone deployment..."
    
    # Ensure install directory exists
    mkdir -p "$install_dir"
    
    # Try to install OpenResty first (includes nginx + modules)
    if install_openresty "$install_dir" 2>/dev/null; then
        log_success "OpenResty installation completed"
        return 0
    fi
    
    log_warning "OpenResty installation failed, trying alternative methods..."
    
    # Fallback to pre-compiled nginx
    install_precompiled_nginx "$install_dir"
}

# Verify installation
verify_installation() {
    local install_dir="$1"
    local nginx_binary="$install_dir/bin/nginx"
    
    log_info "Verifying nginx installation..."
    
    if [[ ! -f "$nginx_binary" ]]; then
        log_error "Nginx binary not found: $nginx_binary"
        return 1
    fi
    
    if [[ ! -x "$nginx_binary" ]]; then
        log_error "Nginx binary is not executable: $nginx_binary"
        return 1
    fi
    
    # Test nginx version (if it's a real nginx binary)
    if "$nginx_binary" -v 2>/dev/null | grep -q "nginx"; then
        local version
        version=$("$nginx_binary" -v 2>&1 | head -1)
        log_success "Nginx installation verified: $version"
    else
        log_warning "Nginx binary verification skipped (wrapper script)"
    fi
    
    return 0
}

# Main function
main() {
    local install_dir="${1:-}"
    
    if [[ -z "$install_dir" ]]; then
        log_error "Usage: $0 <install_directory>"
        exit 1
    fi
    
    log_info "Starting nginx installation to: $install_dir"
    
    # Check prerequisites
    local required_tools=("curl" "tar")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Install nginx
    install_nginx "$install_dir"
    
    # Verify installation
    if verify_installation "$install_dir"; then
        log_success "Nginx installation completed successfully!"
    else
        log_error "Nginx installation verification failed"
        exit 1
    fi
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
