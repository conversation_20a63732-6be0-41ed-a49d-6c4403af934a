#!/bin/bash

# Monitoring Script for Standalone labwise-web-2 Deployment
# This script provides continuous monitoring and alerting capabilities

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
RUNTIME_DIR="$DEPLOY_DIR/runtime"
LOGS_DIR="$RUNTIME_DIR/logs"

# Default configuration
DEFAULT_INTERVAL=60
DEFAULT_LOG_RETENTION=7
DEFAULT_ALERT_THRESHOLD=5

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
INTERVAL="$DEFAULT_INTERVAL"
LOG_RETENTION="$DEFAULT_LOG_RETENTION"
ALERT_THRESHOLD="$DEFAULT_ALERT_THRESHOLD"
DAEMON_MODE=false
ALERT_EMAIL=""
WEBHOOK_URL=""

# Logging functions
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $1"
}

# Show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] [COMMAND]

Monitor the labwise-web-2 standalone deployment.

COMMANDS:
    start                   Start monitoring daemon
    stop                    Stop monitoring daemon
    status                  Show monitoring status
    check                   Run single health check
    logs                    Show monitoring logs
    cleanup                 Clean up old log files

OPTIONS:
    --interval SECONDS      Monitoring interval (default: $DEFAULT_INTERVAL)
    --retention DAYS        Log retention in days (default: $DEFAULT_LOG_RETENTION)
    --threshold COUNT       Alert threshold (default: $DEFAULT_ALERT_THRESHOLD)
    --email EMAIL           Alert email address
    --webhook URL           Webhook URL for alerts
    --daemon               Run in daemon mode
    --help                 Show this help message

EXAMPLES:
    # Start monitoring with default settings
    $0 start

    # Start monitoring with custom interval and email alerts
    $0 --interval 30 --email <EMAIL> start

    # Run single health check
    $0 check

    # Clean up old logs
    $0 cleanup

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --interval)
                INTERVAL="$2"
                shift 2
                ;;
            --retention)
                LOG_RETENTION="$2"
                shift 2
                ;;
            --threshold)
                ALERT_THRESHOLD="$2"
                shift 2
                ;;
            --email)
                ALERT_EMAIL="$2"
                shift 2
                ;;
            --webhook)
                WEBHOOK_URL="$2"
                shift 2
                ;;
            --daemon)
                DAEMON_MODE=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            start|stop|status|check|logs|cleanup)
                COMMAND="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Setup monitoring environment
setup_monitoring() {
    log_info "Setting up monitoring environment..."
    
    # Create monitoring directories
    mkdir -p "$RUNTIME_DIR/monitoring"/{logs,alerts,metrics}
    
    # Create monitoring log file
    local monitor_log="$RUNTIME_DIR/monitoring/logs/monitor.log"
    touch "$monitor_log"
    
    log_success "Monitoring environment setup completed"
}

# Run health check
run_health_check() {
    local health_script="$SCRIPT_DIR/health-check.sh"
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [[ -f "$health_script" ]]; then
        local health_result
        if health_result=$("$health_script" --json 2>/dev/null); then
            local overall_status
            overall_status=$(echo "$health_result" | jq -r '.overall_status' 2>/dev/null || echo "unknown")
            
            # Log health check result
            echo "$timestamp|$overall_status|$health_result" >> "$RUNTIME_DIR/monitoring/logs/health.log"
            
            case "$overall_status" in
                healthy)
                    log_success "Health check passed"
                    return 0
                    ;;
                warning)
                    log_warning "Health check returned warnings"
                    return 1
                    ;;
                unhealthy)
                    log_error "Health check failed"
                    return 2
                    ;;
                *)
                    log_error "Health check returned unknown status"
                    return 3
                    ;;
            esac
        else
            log_error "Health check script failed to execute"
            return 3
        fi
    else
        log_error "Health check script not found: $health_script"
        return 3
    fi
}

# Send alert
send_alert() {
    local alert_type="$1"
    local message="$2"
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Log alert
    echo "$timestamp|$alert_type|$message" >> "$RUNTIME_DIR/monitoring/logs/alerts.log"
    
    # Send email alert if configured
    if [[ -n "$ALERT_EMAIL" ]] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "labwise-web-2 Alert: $alert_type" "$ALERT_EMAIL"
        log_info "Alert email sent to $ALERT_EMAIL"
    fi
    
    # Send webhook alert if configured
    if [[ -n "$WEBHOOK_URL" ]] && command -v curl &> /dev/null; then
        local payload
        payload=$(jq -n --arg type "$alert_type" --arg message "$message" --arg timestamp "$timestamp" '{
            "alert_type": $type,
            "message": $message,
            "timestamp": $timestamp,
            "service": "labwise-web-2"
        }')
        
        if curl -s -X POST -H "Content-Type: application/json" -d "$payload" "$WEBHOOK_URL" > /dev/null; then
            log_info "Alert webhook sent to $WEBHOOK_URL"
        else
            log_warning "Failed to send alert webhook"
        fi
    fi
}

# Monitor daemon
monitor_daemon() {
    log_info "Starting monitoring daemon (interval: ${INTERVAL}s)"
    
    local consecutive_failures=0
    local last_alert_time=0
    
    while true; do
        local current_time
        current_time=$(date +%s)
        
        # Run health check
        local health_status=0
        run_health_check || health_status=$?
        
        case $health_status in
            0)
                # Healthy
                consecutive_failures=0
                ;;
            1)
                # Warning
                consecutive_failures=$((consecutive_failures + 1))
                if [[ $consecutive_failures -ge $ALERT_THRESHOLD ]]; then
                    local time_since_alert=$((current_time - last_alert_time))
                    if [[ $time_since_alert -gt 3600 ]]; then  # Don't spam alerts (1 hour cooldown)
                        send_alert "WARNING" "Service health check returned warnings for $consecutive_failures consecutive checks"
                        last_alert_time=$current_time
                    fi
                fi
                ;;
            2|3)
                # Unhealthy or error
                consecutive_failures=$((consecutive_failures + 1))
                if [[ $consecutive_failures -ge $ALERT_THRESHOLD ]]; then
                    local time_since_alert=$((current_time - last_alert_time))
                    if [[ $time_since_alert -gt 1800 ]]; then  # 30 minute cooldown for critical alerts
                        send_alert "CRITICAL" "Service health check failed for $consecutive_failures consecutive checks"
                        last_alert_time=$current_time
                    fi
                fi
                ;;
        esac
        
        # Collect metrics
        collect_metrics
        
        # Clean up old logs if needed
        cleanup_old_logs
        
        # Sleep until next check
        sleep "$INTERVAL"
    done
}

# Collect system metrics
collect_metrics() {
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local metrics_file="$RUNTIME_DIR/monitoring/metrics/$(date '+%Y-%m-%d').json"
    
    # Collect basic system metrics
    local cpu_usage="0"
    local memory_usage="0"
    local disk_usage="0"
    
    # CPU usage (if available)
    if command -v top &> /dev/null; then
        cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//' 2>/dev/null || echo "0")
    fi
    
    # Memory usage (if available)
    if command -v free &> /dev/null; then
        memory_usage=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}' 2>/dev/null || echo "0")
    fi
    
    # Disk usage
    if command -v df &> /dev/null; then
        disk_usage=$(df "$DEPLOY_DIR" | awk 'NR==2{print $5}' | sed 's/%//' 2>/dev/null || echo "0")
    fi
    
    # Create metrics entry
    local metric_entry
    metric_entry=$(jq -n --arg timestamp "$timestamp" --arg cpu "$cpu_usage" --arg memory "$memory_usage" --arg disk "$disk_usage" '{
        "timestamp": $timestamp,
        "cpu_percent": ($cpu | tonumber),
        "memory_percent": ($memory | tonumber),
        "disk_percent": ($disk | tonumber)
    }')
    
    # Append to metrics file
    if [[ -f "$metrics_file" ]]; then
        local temp_file
        temp_file=$(mktemp)
        jq --argjson entry "$metric_entry" '. += [$entry]' "$metrics_file" > "$temp_file" && mv "$temp_file" "$metrics_file"
    else
        echo "[$metric_entry]" > "$metrics_file"
    fi
}

# Clean up old logs
cleanup_old_logs() {
    local retention_days="$LOG_RETENTION"
    
    # Clean up monitoring logs
    find "$RUNTIME_DIR/monitoring/logs" -name "*.log" -mtime +$retention_days -delete 2>/dev/null || true
    
    # Clean up metrics files
    find "$RUNTIME_DIR/monitoring/metrics" -name "*.json" -mtime +$retention_days -delete 2>/dev/null || true
    
    # Rotate large log files
    local max_size=$((10 * 1024 * 1024))  # 10MB
    for log_file in "$RUNTIME_DIR/monitoring/logs"/*.log; do
        if [[ -f "$log_file" ]] && [[ $(stat -f%z "$log_file" 2>/dev/null || stat -c%s "$log_file" 2>/dev/null || echo 0) -gt $max_size ]]; then
            mv "$log_file" "${log_file}.$(date +%Y%m%d-%H%M%S)"
            touch "$log_file"
        fi
    done
}

# Start monitoring
start_monitoring() {
    local pid_file="$RUNTIME_DIR/monitoring/monitor.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "Monitoring is already running (PID: $pid)"
            return 0
        else
            rm -f "$pid_file"
        fi
    fi
    
    setup_monitoring
    
    if [[ "$DAEMON_MODE" == "true" ]]; then
        # Start as daemon
        nohup "$0" --interval "$INTERVAL" --retention "$LOG_RETENTION" --threshold "$ALERT_THRESHOLD" monitor_daemon > "$RUNTIME_DIR/monitoring/logs/monitor.log" 2>&1 &
        local pid=$!
        echo "$pid" > "$pid_file"
        log_success "Monitoring daemon started (PID: $pid)"
    else
        # Start in foreground
        monitor_daemon
    fi
}

# Stop monitoring
stop_monitoring() {
    local pid_file="$RUNTIME_DIR/monitoring/monitor.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            rm -f "$pid_file"
            log_success "Monitoring daemon stopped"
        else
            log_warning "Monitoring daemon was not running"
            rm -f "$pid_file"
        fi
    else
        log_warning "Monitoring daemon is not running"
    fi
}

# Show monitoring status
show_monitoring_status() {
    local pid_file="$RUNTIME_DIR/monitoring/monitor.pid"
    
    log_info "Monitoring Status:"
    
    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_success "  Monitoring daemon: Running (PID: $pid)"
        else
            log_error "  Monitoring daemon: Dead (PID file exists but process not running)"
        fi
    else
        log_warning "  Monitoring daemon: Not running"
    fi
    
    # Show recent health check results
    local health_log="$RUNTIME_DIR/monitoring/logs/health.log"
    if [[ -f "$health_log" ]]; then
        log_info "  Recent health checks:"
        tail -5 "$health_log" | while IFS='|' read -r timestamp status result; do
            case "$status" in
                healthy)
                    log_success "    $timestamp: $status"
                    ;;
                warning)
                    log_warning "    $timestamp: $status"
                    ;;
                *)
                    log_error "    $timestamp: $status"
                    ;;
            esac
        done
    fi
}

# Show monitoring logs
show_monitoring_logs() {
    local log_file="$RUNTIME_DIR/monitoring/logs/monitor.log"
    
    if [[ -f "$log_file" ]]; then
        tail -50 "$log_file"
    else
        log_warning "Monitoring log file not found"
    fi
}

# Main function
main() {
    local command=""
    parse_arguments "$@"
    
    case "${COMMAND:-}" in
        start)
            DAEMON_MODE=true
            start_monitoring
            ;;
        stop)
            stop_monitoring
            ;;
        status)
            show_monitoring_status
            ;;
        check)
            run_health_check
            ;;
        logs)
            show_monitoring_logs
            ;;
        cleanup)
            cleanup_old_logs
            log_success "Log cleanup completed"
            ;;
        monitor_daemon)
            # Internal command for daemon mode
            monitor_daemon
            ;;
        "")
            log_error "No command specified"
            show_usage
            exit 1
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_usage
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"
