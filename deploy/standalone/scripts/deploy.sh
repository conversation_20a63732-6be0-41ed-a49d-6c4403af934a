#!/bin/bash

# Main Deployment Script for Standalone labwise-web-2
# This script handles the complete deployment process in environments without Docker

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
RUNTIME_DIR="$DEPLOY_DIR/runtime"
NGINX_DIR="$RUNTIME_DIR/nginx"
LOGS_DIR="$RUNTIME_DIR/logs"
PID_DIR="$RUNTIME_DIR/pid"

# Default configuration
DEFAULT_PORT=1555
DEFAULT_ENV="production"
DEFAULT_LOG_LEVEL="info"
DEFAULT_BACKEND_URL="http://brain-server:1337"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration variables
PORT="$DEFAULT_PORT"
ENVIRONMENT="$DEFAULT_ENV"
LOG_LEVEL="$DEFAULT_LOG_LEVEL"
BACKEND_URL="$DEFAULT_BACKEND_URL"
DATA_DIR=""
FORCE_REINSTALL=false

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy labwise-web-2 in standalone mode without Docker dependencies.

OPTIONS:
    --port PORT             HTTP port to listen on (default: $DEFAULT_PORT)
    --env ENVIRONMENT       Environment: development|production (default: $DEFAULT_ENV)
    --backend-url URL       Backend API URL (default: $DEFAULT_BACKEND_URL)
    --data-dir PATH         Data directory path (default: current directory)
    --log-level LEVEL       Log level: debug|info|warn|error (default: $DEFAULT_LOG_LEVEL)
    --force                 Force reinstallation even if already deployed
    --help                  Show this help message

EXAMPLES:
    # Basic deployment
    $0 --port 1555 --env production

    # Development deployment with custom backend
    $0 --port 3000 --env development --backend-url http://localhost:1337

    # Custom data directory
    $0 --port 1555 --data-dir /opt/labwise-web

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --port)
                PORT="$2"
                shift 2
                ;;
            --env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --backend-url)
                BACKEND_URL="$2"
                shift 2
                ;;
            --data-dir)
                DATA_DIR="$2"
                shift 2
                ;;
            --log-level)
                LOG_LEVEL="$2"
                shift 2
                ;;
            --force)
                FORCE_REINSTALL=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Validate configuration
validate_config() {
    log_info "Validating configuration..."
    
    # Validate port
    if ! [[ "$PORT" =~ ^[0-9]+$ ]] || [ "$PORT" -lt 1 ] || [ "$PORT" -gt 65535 ]; then
        log_error "Invalid port: $PORT. Must be between 1 and 65535."
        exit 1
    fi
    
    # Validate environment
    if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Must be 'development' or 'production'."
        exit 1
    fi
    
    # Check if port is available
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$PORT "; then
            log_warning "Port $PORT appears to be in use. Deployment may fail if not available."
        fi
    fi
    
    # Set data directory if not specified
    if [[ -z "$DATA_DIR" ]]; then
        DATA_DIR="$DEPLOY_DIR"
    fi
    
    log_success "Configuration validated"
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check available disk space (minimum 1GB)
    local available_space
    available_space=$(df "$DEPLOY_DIR" | awk 'NR==2 {print $4}')
    if [[ "$available_space" -lt 1048576 ]]; then  # 1GB in KB
        log_warning "Low disk space. At least 1GB recommended."
    fi
    
    # Check if we have write permissions
    if [[ ! -w "$DEPLOY_DIR" ]]; then
        log_error "No write permission in deployment directory: $DEPLOY_DIR"
        exit 1
    fi
    
    # Check for required directories
    local required_dirs=("$DEPLOY_DIR/dist" "$DEPLOY_DIR/templates")
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_error "Required directory not found: $dir"
            log_error "Please ensure the deployment package is complete."
            exit 1
        fi
    done
    
    log_success "System requirements check passed"
}

# Setup runtime environment
setup_runtime() {
    log_info "Setting up runtime environment..."
    
    # Create runtime directories
    mkdir -p "$RUNTIME_DIR"/{nginx/{bin,conf,logs,tmp,modules},logs,pid,tmp}
    
    # Set proper permissions
    chmod 755 "$RUNTIME_DIR"
    chmod 755 "$NGINX_DIR"
    chmod 755 "$LOGS_DIR"
    chmod 755 "$PID_DIR"
    
    log_success "Runtime environment setup completed"
}

# Configure nginx
configure_nginx() {
    log_info "Configuring nginx..."
    
    local nginx_conf="$NGINX_DIR/conf/nginx.conf"
    local template_file="$DEPLOY_DIR/templates/nginx.conf.template"
    
    if [[ ! -f "$template_file" ]]; then
        log_error "Nginx configuration template not found: $template_file"
        exit 1
    fi
    
    # Generate nginx configuration from template
    sed -e "s|{{PORT}}|$PORT|g" \
        -e "s|{{DEPLOY_DIR}}|$DEPLOY_DIR|g" \
        -e "s|{{LOGS_DIR}}|$LOGS_DIR|g" \
        -e "s|{{PID_DIR}}|$PID_DIR|g" \
        -e "s|{{BACKEND_URL}}|$BACKEND_URL|g" \
        -e "s|{{ENVIRONMENT}}|$ENVIRONMENT|g" \
        "$template_file" > "$nginx_conf"
    
    # Copy mime types
    if [[ -f "$DEPLOY_DIR/templates/mime.types" ]]; then
        cp "$DEPLOY_DIR/templates/mime.types" "$NGINX_DIR/conf/"
    fi
    
    log_success "Nginx configuration completed"
}

# Install nginx binary (placeholder for actual implementation)
install_nginx() {
    log_info "Installing portable nginx..."
    
    local nginx_binary="$NGINX_DIR/bin/nginx"
    
    # This is a placeholder - in a real implementation, you would:
    # 1. Download a pre-compiled nginx binary with required modules
    # 2. Or compile nginx from source with the required modules
    # 3. Or use a portable nginx distribution
    
    if [[ ! -f "$nginx_binary" ]]; then
        log_warning "Nginx binary not found. Creating placeholder..."
        log_warning "In production, this would download/install a real nginx binary with required modules:"
        log_warning "- ngx_http_headers_more_filter_module"
        log_warning "- ndk_http_module"
        log_warning "- ngx_http_lua_module"
        
        # Create a placeholder script for demonstration
        cat > "$nginx_binary" << 'EOF'
#!/bin/bash
echo "This is a placeholder nginx binary."
echo "In production, this would be a real nginx binary with required modules."
echo "Configuration file: $2"
echo "Prefix directory: $4"
exit 0
EOF
        chmod +x "$nginx_binary"
    fi
    
    log_success "Nginx installation completed"
}

# Create management scripts
create_management_scripts() {
    log_info "Creating management scripts..."
    
    # Create start script
    cat > "$DEPLOY_DIR/start.sh" << EOF
#!/bin/bash
exec "$SCRIPT_DIR/start.sh" "\$@"
EOF
    
    # Create stop script
    cat > "$DEPLOY_DIR/stop.sh" << EOF
#!/bin/bash
exec "$SCRIPT_DIR/stop.sh" "\$@"
EOF
    
    # Create status script
    cat > "$DEPLOY_DIR/status.sh" << EOF
#!/bin/bash
exec "$SCRIPT_DIR/status.sh" "\$@"
EOF
    
    chmod +x "$DEPLOY_DIR"/{start,stop,status}.sh
    
    log_success "Management scripts created"
}

# Main deployment function
main() {
    log_info "Starting standalone deployment of labwise-web-2..."
    
    parse_arguments "$@"
    validate_config
    check_requirements
    setup_runtime
    install_nginx
    configure_nginx
    create_management_scripts
    
    log_success "Deployment completed successfully!"
    log_info ""
    log_info "Deployment Summary:"
    log_info "  Port: $PORT"
    log_info "  Environment: $ENVIRONMENT"
    log_info "  Data Directory: $DATA_DIR"
    log_info "  Backend URL: $BACKEND_URL"
    log_info ""
    log_info "Next steps:"
    log_info "  1. Start services: ./start.sh"
    log_info "  2. Check status: ./status.sh"
    log_info "  3. View logs: tail -f $LOGS_DIR/nginx.log"
    log_info "  4. Access application: http://localhost:$PORT"
}

# Execute main function
main "$@"
