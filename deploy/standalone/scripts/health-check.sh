#!/bin/bash

# Health Check Script for Standalone labwise-web-2 Deployment
# This script performs comprehensive health checks on the deployed application

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
RUNTIME_DIR="$DEPLOY_DIR/runtime"
NGINX_DIR="$RUNTIME_DIR/nginx"
LOGS_DIR="$RUNTIME_DIR/logs"
PID_DIR="$RUNTIME_DIR/pid"

# Default configuration
DEFAULT_TIMEOUT=30
DEFAULT_RETRIES=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
TIMEOUT="$DEFAULT_TIMEOUT"
RETRIES="$DEFAULT_RETRIES"
VERBOSE=false
JSON_OUTPUT=false

# Logging functions
log_info() {
    if [[ "$JSON_OUTPUT" == "false" ]]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

log_success() {
    if [[ "$JSON_OUTPUT" == "false" ]]; then
        echo -e "${GREEN}[SUCCESS]${NC} $1"
    fi
}

log_warning() {
    if [[ "$JSON_OUTPUT" == "false" ]]; then
        echo -e "${YELLOW}[WARNING]${NC} $1"
    fi
}

log_error() {
    if [[ "$JSON_OUTPUT" == "false" ]]; then
        echo -e "${RED}[ERROR]${NC} $1"
    fi
}

# Show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Perform health checks on the labwise-web-2 standalone deployment.

OPTIONS:
    --timeout SECONDS       Request timeout (default: $DEFAULT_TIMEOUT)
    --retries COUNT         Number of retries (default: $DEFAULT_RETRIES)
    --verbose              Enable verbose output
    --json                 Output results in JSON format
    --help                 Show this help message

EXAMPLES:
    # Basic health check
    $0

    # Verbose health check with custom timeout
    $0 --verbose --timeout 60

    # JSON output for monitoring systems
    $0 --json

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            --retries)
                RETRIES="$2"
                shift 2
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --json)
                JSON_OUTPUT=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Initialize results structure
init_results() {
    HEALTH_RESULTS='{
        "timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'",
        "overall_status": "unknown",
        "checks": {}
    }'
}

# Add check result
add_check_result() {
    local check_name="$1"
    local status="$2"
    local message="$3"
    local details="${4:-{}}"
    
    HEALTH_RESULTS=$(echo "$HEALTH_RESULTS" | jq --arg name "$check_name" --arg status "$status" --arg message "$message" --argjson details "$details" '
        .checks[$name] = {
            "status": $status,
            "message": $message,
            "details": $details
        }
    ')
}

# Check if service is running
check_service_running() {
    local service_name="$1"
    local pid_file="$PID_DIR/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo "running:$pid"
        else
            echo "dead"
        fi
    else
        echo "stopped"
    fi
}

# Check nginx service
check_nginx_service() {
    log_info "Checking nginx service..."
    
    local status
    status=$(check_service_running "nginx")
    
    case "$status" in
        running:*)
            local pid="${status#running:}"
            log_success "Nginx service is running (PID: $pid)"
            
            local details='{}'
            if command -v ps &> /dev/null; then
                local memory cpu start_time
                memory=$(ps -o rss= -p "$pid" 2>/dev/null | awk '{print int($1/1024)}' || echo "0")
                cpu=$(ps -o %cpu= -p "$pid" 2>/dev/null | awk '{print $1}' || echo "0")
                start_time=$(ps -o lstart= -p "$pid" 2>/dev/null | awk '{print $1" "$2" "$3" "$4}' || echo "unknown")
                
                details=$(jq -n --arg pid "$pid" --arg memory "$memory" --arg cpu "$cpu" --arg start_time "$start_time" '{
                    "pid": $pid,
                    "memory_mb": ($memory | tonumber),
                    "cpu_percent": ($cpu | tonumber),
                    "start_time": $start_time
                }')
            fi
            
            add_check_result "nginx_service" "healthy" "Nginx service is running" "$details"
            return 0
            ;;
        dead)
            log_error "Nginx service is dead (PID file exists but process not running)"
            add_check_result "nginx_service" "unhealthy" "Nginx service is dead"
            return 1
            ;;
        stopped)
            log_error "Nginx service is not running"
            add_check_result "nginx_service" "unhealthy" "Nginx service is not running"
            return 1
            ;;
    esac
}

# Check HTTP endpoint
check_http_endpoint() {
    log_info "Checking HTTP endpoint..."
    
    # Get port from nginx configuration
    local nginx_conf="$NGINX_DIR/conf/nginx.conf"
    local port="1555"
    
    if [[ -f "$nginx_conf" ]]; then
        port=$(grep -o 'listen [0-9]*' "$nginx_conf" | head -1 | awk '{print $2}' || echo "1555")
    fi
    
    local url="http://localhost:$port"
    local health_url="http://localhost:$port/health"
    
    # Test main endpoint
    local attempt=1
    while [[ $attempt -le $RETRIES ]]; do
        if [[ "$VERBOSE" == "true" ]]; then
            log_info "Testing HTTP endpoint (attempt $attempt/$RETRIES): $url"
        fi
        
        if command -v curl &> /dev/null; then
            if curl -s -f --max-time "$TIMEOUT" "$url" > /dev/null 2>&1; then
                log_success "HTTP endpoint is responding"
                
                # Test health endpoint
                local health_response=""
                if curl -s -f --max-time "$TIMEOUT" "$health_url" 2>/dev/null | grep -q "healthy"; then
                    health_response="healthy"
                    log_success "Health endpoint is responding correctly"
                else
                    health_response="unknown"
                    log_warning "Health endpoint not available or not responding correctly"
                fi
                
                local details
                details=$(jq -n --arg url "$url" --arg port "$port" --arg health "$health_response" '{
                    "url": $url,
                    "port": ($port | tonumber),
                    "health_endpoint": $health,
                    "response_time_ms": 0
                }')
                
                add_check_result "http_endpoint" "healthy" "HTTP endpoint is responding" "$details"
                return 0
            fi
        elif command -v wget &> /dev/null; then
            if wget -q --spider --timeout="$TIMEOUT" "$url" 2>/dev/null; then
                log_success "HTTP endpoint is responding"
                add_check_result "http_endpoint" "healthy" "HTTP endpoint is responding" '{"url": "'$url'", "port": '$port'}'
                return 0
            fi
        fi
        
        if [[ $attempt -lt $RETRIES ]]; then
            log_warning "HTTP endpoint check failed, retrying in 2 seconds..."
            sleep 2
        fi
        ((attempt++))
    done
    
    log_error "HTTP endpoint is not responding after $RETRIES attempts"
    add_check_result "http_endpoint" "unhealthy" "HTTP endpoint is not responding" '{"url": "'$url'", "port": '$port', "attempts": '$RETRIES'}'
    return 1
}

# Check disk space
check_disk_space() {
    log_info "Checking disk space..."
    
    if command -v df &> /dev/null; then
        local disk_info
        disk_info=$(df "$DEPLOY_DIR" 2>/dev/null | awk 'NR==2 {print $2":"$3":"$4":"$5}' || echo "0:0:0:0%")
        
        IFS=':' read -r total used available percent <<< "$disk_info"
        local available_gb=$((available / 1024 / 1024))
        local percent_num="${percent%\%}"
        
        if [[ $available_gb -lt 1 ]]; then
            log_warning "Low disk space: ${available_gb}GB available"
            add_check_result "disk_space" "warning" "Low disk space" '{"available_gb": '$available_gb', "used_percent": '$percent_num'}'
        else
            log_success "Sufficient disk space: ${available_gb}GB available"
            add_check_result "disk_space" "healthy" "Sufficient disk space" '{"available_gb": '$available_gb', "used_percent": '$percent_num'}'
        fi
    else
        log_warning "Cannot check disk space (df command not available)"
        add_check_result "disk_space" "unknown" "Cannot check disk space"
    fi
}

# Check log files
check_log_files() {
    log_info "Checking log files..."
    
    local log_files=(
        "$LOGS_DIR/nginx.log:access"
        "$LOGS_DIR/nginx-error.log:error"
    )
    
    local log_status="healthy"
    local log_details='{"files": []}'
    
    for log_entry in "${log_files[@]}"; do
        local log_file="${log_entry%:*}"
        local log_type="${log_entry#*:}"
        
        if [[ -f "$log_file" ]]; then
            local size_bytes
            size_bytes=$(stat -f%z "$log_file" 2>/dev/null || stat -c%s "$log_file" 2>/dev/null || echo "0")
            local size_mb=$((size_bytes / 1024 / 1024))
            
            # Check for recent errors in error log
            local recent_errors=0
            if [[ "$log_type" == "error" ]] && [[ -s "$log_file" ]]; then
                recent_errors=$(tail -100 "$log_file" 2>/dev/null | grep -i error | wc -l || echo "0")
            fi
            
            log_details=$(echo "$log_details" | jq --arg file "$log_file" --arg type "$log_type" --arg size "$size_mb" --arg errors "$recent_errors" '
                .files += [{
                    "path": $file,
                    "type": $type,
                    "size_mb": ($size | tonumber),
                    "recent_errors": ($errors | tonumber)
                }]
            ')
            
            if [[ $recent_errors -gt 10 ]]; then
                log_warning "High error count in $log_file: $recent_errors recent errors"
                log_status="warning"
            fi
        else
            log_warning "Log file not found: $log_file"
            log_status="warning"
        fi
    done
    
    add_check_result "log_files" "$log_status" "Log files checked" "$log_details"
}

# Determine overall status
determine_overall_status() {
    local overall_status="healthy"
    
    # Check if any critical checks failed
    if echo "$HEALTH_RESULTS" | jq -e '.checks.nginx_service.status == "unhealthy"' > /dev/null 2>&1; then
        overall_status="unhealthy"
    elif echo "$HEALTH_RESULTS" | jq -e '.checks.http_endpoint.status == "unhealthy"' > /dev/null 2>&1; then
        overall_status="unhealthy"
    elif echo "$HEALTH_RESULTS" | jq -e '[.checks[].status] | contains(["warning"])' > /dev/null 2>&1; then
        overall_status="warning"
    fi
    
    HEALTH_RESULTS=$(echo "$HEALTH_RESULTS" | jq --arg status "$overall_status" '.overall_status = $status')
}

# Output results
output_results() {
    if [[ "$JSON_OUTPUT" == "true" ]]; then
        echo "$HEALTH_RESULTS" | jq '.'
    else
        local overall_status
        overall_status=$(echo "$HEALTH_RESULTS" | jq -r '.overall_status')
        
        echo
        case "$overall_status" in
            healthy)
                log_success "Overall health status: HEALTHY"
                ;;
            warning)
                log_warning "Overall health status: WARNING"
                ;;
            unhealthy)
                log_error "Overall health status: UNHEALTHY"
                ;;
        esac
    fi
}

# Main function
main() {
    parse_arguments "$@"
    
    # Check if jq is available for JSON processing
    if ! command -v jq &> /dev/null; then
        if [[ "$JSON_OUTPUT" == "true" ]]; then
            echo '{"error": "jq command not available for JSON output"}' >&2
            exit 1
        fi
        log_warning "jq not available - some features may be limited"
    fi
    
    init_results
    
    if [[ "$JSON_OUTPUT" == "false" ]]; then
        log_info "Starting health check for labwise-web-2..."
        echo
    fi
    
    # Perform health checks
    local exit_code=0
    
    check_nginx_service || exit_code=1
    check_http_endpoint || exit_code=1
    check_disk_space
    check_log_files
    
    # Determine overall status
    determine_overall_status
    
    # Output results
    output_results
    
    # Exit with appropriate code
    local overall_status
    overall_status=$(echo "$HEALTH_RESULTS" | jq -r '.overall_status' 2>/dev/null || echo "unknown")
    
    case "$overall_status" in
        healthy)
            exit 0
            ;;
        warning)
            exit 0
            ;;
        unhealthy)
            exit 1
            ;;
        *)
            exit 1
            ;;
    esac
}

# Execute main function
main "$@"
