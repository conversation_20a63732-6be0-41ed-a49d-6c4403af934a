# Standalone Deployment Solution for labwise-web-2

This directory contains a comprehensive standalone deployment solution that allows deploying the React frontend without Docker dependencies.

## Overview

The standalone deployment solution provides:
- **Self-contained runtime**: Portable nginx with all required modules
- **Zero system dependencies**: Everything runs from user directory
- **Simple deployment**: Single command deployment and management
- **Environment isolation**: No conflicts with system packages
- **Cross-platform support**: Works on Linux distributions without Docker

## Directory Structure

```
standalone/
├── README.md                 # This file
├── scripts/                  # Deployment and management scripts
│   ├── build-package.sh     # Build and package the deployment
│   ├── deploy.sh            # Main deployment script
│   ├── start.sh             # Start services
│   ├── stop.sh              # Stop services
│   ├── status.sh            # Check service status
│   └── utils/               # Utility scripts
├── templates/               # Configuration templates
│   ├── nginx.conf.template  # Nginx configuration template
│   ├── mime.types           # MIME types configuration
│   └── env.template         # Environment configuration template
├── runtime/                 # Runtime environment (created during deployment)
│   ├── nginx/              # Portable nginx installation
│   ├── logs/               # Application logs
│   ├── pid/                # Process ID files
│   └── tmp/                # Temporary files
└── dist/                   # Built React application (created during build)
```

## Quick Start

### 1. Build Deployment Package
```bash
# From the project root
./deploy/standalone/scripts/build-package.sh
```

### 2. Deploy to Target Environment
```bash
# Copy the generated package to target environment
scp labwise-web-standalone-*.tar.gz user@target-server:~/

# On target server
tar -xzf labwise-web-standalone-*.tar.gz
cd labwise-web-standalone/
./deploy.sh --port 1555 --env production
```

### 3. Manage Services
```bash
# Start services
./start.sh

# Check status
./status.sh

# Stop services
./stop.sh
```

## Configuration

The deployment supports environment-specific configuration through:
- Command-line parameters
- Environment variables
- Configuration files

### Supported Parameters

- `--port`: HTTP port (default: 1555)
- `--env`: Environment (development/production)
- `--data-dir`: Data directory path
- `--log-level`: Logging level
- `--backend-url`: Backend API URL

## Requirements

- Linux x86_64 system
- Minimum 512MB RAM
- 1GB disk space
- User-level permissions (no root required)

## Features

✅ **Portable Runtime**: Self-contained nginx with all modules  
✅ **Zero Dependencies**: No system packages required  
✅ **User-level Installation**: No root privileges needed  
✅ **Environment Isolation**: Isolated from system nginx  
✅ **Health Monitoring**: Built-in health checks  
✅ **Log Management**: Structured logging and rotation  
✅ **Process Management**: Reliable start/stop/restart  
✅ **Configuration Management**: Template-based configuration  

## Testing

### Automated Testing
Run the comprehensive test suite to verify your deployment:

```bash
# Run all tests
./scripts/test-deployment.sh

# Run tests with custom configuration
./scripts/test-deployment.sh --port 18555 --env development

# Skip build test (if already built)
./scripts/test-deployment.sh --skip-build
```

### Manual Testing
```bash
# Test health check
./scripts/health-check.sh --verbose

# Test monitoring
./scripts/monitor.sh check

# Test service management
./start.sh && ./status.sh && ./stop.sh
```

## Monitoring and Maintenance

### Health Monitoring
```bash
# Start continuous monitoring
./scripts/monitor.sh start

# Check monitoring status
./scripts/monitor.sh status

# View monitoring logs
./scripts/monitor.sh logs
```

### Log Management
```bash
# View real-time logs
tail -f runtime/logs/nginx.log

# Clean up old logs
./scripts/monitor.sh cleanup

# Rotate logs manually
mv runtime/logs/nginx.log runtime/logs/nginx.log.$(date +%Y%m%d)
touch runtime/logs/nginx.log
```

## Advanced Configuration

### Custom Nginx Modules
To use the full nginx configuration with lua and headers_more modules:

1. **Install OpenResty** (nginx with modules):
   ```bash
   # The nginx installer will attempt to install OpenResty automatically
   ./scripts/utils/nginx-installer.sh runtime/nginx
   ```

2. **Update nginx configuration** to enable modules:
   ```bash
   # Uncomment module loading in nginx.conf.template
   sed -i 's/# load_module/load_module/' templates/nginx.conf.template
   ```

3. **Rebuild and redeploy**:
   ```bash
   ./scripts/build-package.sh
   ./scripts/deploy.sh --force
   ```

### SSL/HTTPS Configuration
For production deployments with SSL:

1. **Obtain SSL certificates**
2. **Update nginx configuration template**:
   ```nginx
   server {
       listen 443 ssl;
       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;
       # ... rest of configuration
   }
   ```

### Load Balancing
For multiple backend instances:

```nginx
upstream backend {
    server backend1.example.com:1337 weight=3;
    server backend2.example.com:1337 weight=2;
    server backend3.example.com:1337 weight=1;
    # Add more servers as needed
}
```

## Production Deployment Checklist

- [ ] **Security**: Configure firewall rules for HTTP port
- [ ] **SSL/TLS**: Set up HTTPS certificates (recommended)
- [ ] **Monitoring**: Enable monitoring daemon
- [ ] **Backups**: Set up regular configuration backups
- [ ] **Updates**: Plan for regular deployment updates
- [ ] **Logging**: Configure log rotation and retention
- [ ] **Performance**: Tune nginx worker processes for your hardware
- [ ] **Health Checks**: Set up external health monitoring

## Troubleshooting

See the comprehensive troubleshooting guide in `docs/troubleshooting.md` for:
- Common deployment issues
- Service startup problems
- Network connectivity issues
- Performance optimization
- Log analysis techniques
- Recovery procedures

## Documentation

- **Deployment Guide**: `docs/deployment-guide.md` - Comprehensive deployment instructions
- **Troubleshooting**: `docs/troubleshooting.md` - Common issues and solutions
- **API Reference**: Check the main project documentation for API details

## Support and Contributing

For issues, questions, or contributions:
1. Check the troubleshooting guide
2. Review the deployment logs
3. Run the test suite to identify issues
4. Consult the main project documentation
