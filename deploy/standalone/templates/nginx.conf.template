worker_processes  1;

# Note: In a real deployment, these modules would be compiled into nginx
# load_module modules/ngx_http_headers_more_filter_module.so;
# load_module modules/ndk_http_module.so;
# load_module modules/ngx_http_lua_module.so;

# Process ID file
pid {{PID_DIR}}/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    # Logging configuration
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  {{LOGS_DIR}}/nginx.log  main;
    error_log   {{LOGS_DIR}}/nginx-error.log;

    sendfile        on;
    keepalive_timeout  65;

    # Upstream configuration for backend services
    upstream brain-server-stream {
        server {{BACKEND_URL}};
        # Note: In production, you would configure actual backend servers
        # server tasks.brain-server:1337;
        # ip_hash;
    }

    server {
        listen {{PORT}};
        server_name _;

        # Document root - serving the built React application
        root   {{DEPLOY_DIR}}/dist;
        index  index.html index.htm;

        # Proxy configuration
        proxy_busy_buffers_size   512k;
        proxy_buffers   4 512k;
        proxy_buffer_size   256k;

        # Main application route - SPA fallback
        location / {
            try_files $uri $uri/ /index.html;
            # Ensure ETag is enabled
            etag on;
        }

        # API proxy routes (configure based on your backend)
        location /api/smiles/to-image {
            rewrite ^/api/smiles/to-image/(.*)$ /$1 break;
            proxy_pass http://************:3333;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/smiles/image-file {
            proxy_pass http://************:9000/smiles-svgs;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/smiles/inchi {
            proxy_pass http://**************:8000/smiles/inchi;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/ {
            proxy_pass {{BACKEND_URL}}/api/;

            proxy_redirect off;
            proxy_buffering off;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
        }

        # WebSocket support
        location ~* \.io {
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_http_version 1.1;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_pass {{BACKEND_URL}};
            proxy_read_timeout 60s;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;

            # Additional directives for WebSocket
            proxy_buffering off;
        }

        # Static asset caching
        location ~* ^.+\.(?!html$|svg$)[^.]+$ {
            # Cache static assets for 1 year
            add_header Cache-Control "public, max-age=31536000, immutable";
            
            # Note: In production with nginx modules, you would use:
            # more_clear_headers 'Last-Modified';
            # set $etag_from_filename '';
            # rewrite_by_lua_block {
            #     local file_name = ngx.var.uri
            #     local etag = ngx.md5(file_name)
            #     ngx.var.etag_from_filename = etag
            # }
            # add_header ETag '"$etag_from_filename"';
        }

        # Gzip compression
        gzip on;
        gzip_comp_level 5;
        gzip_min_length 256;
        gzip_proxied any;
        gzip_vary on;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/javascript
            application/json
            application/xhtml+xml;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
