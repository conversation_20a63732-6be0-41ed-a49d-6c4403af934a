version: '3'

services:
  labwise-web:
    container_name: labwise-web-2
    image: "${IMAGE_NAME}"
    restart: always
    ports:
      - 1555:80
    volumes:
      - /home/<USER>/labwise-help/src:/usr/share/nginx/html/help
    logging:
      driver: loki
      options:
        loki-url: http://ec2-68-79-33-157.cn-northwest-1.compute.amazonaws.com.cn:3100/loki/api/v1/push
        loki-external-labels: instance=${INSTANCE_NAME}
