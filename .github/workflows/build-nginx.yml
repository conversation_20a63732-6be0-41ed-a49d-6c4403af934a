name: Build image in action and push

on:
  workflow_call:
    inputs:
      target-env:
        required: true
        type: string
  workflow_dispatch:
    inputs:
      target-env:
        description: 'Environment to create source branch'
        required: true
        type: choice
        options:
          - develop
          - test
          - product
          - demo
          - michigan
          - partener
          - partener-keycloak
          - partener-leyan
          - partener-beigene
        default: 'develop'

env:
  AWS_REGION: cn-northwest-1
  ECR_REPOSITORY: web-2

permissions:
  contents: read

jobs:
  build-push-image:
    timeout-minutes: 30 # the workflow can run 30 mins at most
    name: build front nginx image for ${{ inputs.target-env }} environment
    runs-on: ubuntu-latest

    steps:
      - name: Check out the files
        uses: actions/checkout@main
        with:
          fetch-depth: 1

      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Dependencies
        run: yarn install --production

      - name: Build Project
        run: NODE_ENV=production UMI_ENV=${{ inputs.target-env }} yarn build

      - name: Upload dist as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
          UMI_ENV: ${{ inputs.target-env }}
        run: |
          cp -r deploy/Dockerfile.build ./Dockerfile
          DOCKER_BUILDKIT=1 docker build --build-arg UMI_ENV=${{ inputs.target-env }} -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -t $ECR_REGISTRY/$ECR_REPOSITORY:${{ inputs.target-env }}-latest .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:${{ inputs.target-env }}-latest
          echo "image-tag=$IMAGE_TAG" >> $GITHUB_OUTPUT

      - run: echo "${{ steps.build-image.outputs.image-tag }}"

    outputs:
      image-tag: ${{ steps.build-image.outputs.image-tag }}
