name: Workflow when push on develop branch

on:
  push: # 自动触发监听
    branches:
      - develop
      - beigene

permissions:
  contents: read

# allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ startsWith(github.ref, 'refs/pull/') }}

jobs:
  deploy-normal:
    strategy:
      fail-fast: true
      matrix:
        target-env:
          - ${{ github.ref_name }}
    uses: ./.github/workflows/build-nginx.yml
    with:
      target-env: ${{ matrix.target-env }}
    secrets: inherit
