function forceRefresh() {
  if (location.href.indexOf('#reloaded') === -1) {
    console.log('---force refresh---')
    location.href = location.href + '#reloaded'
    window.location.reload(true)
  } else {
    alert('有版本更新，请手动刷新页面！')
  }
}

// prompt user to confirm refresh
window.addEventListener('error', (e) => {
  if(e?.message) console.log('---error info---', e)
  if (
    /Loading chunk [\d]+ failed/.test(e.message) ||
    e?.message?.includes(`Unexpected token '<'`) ||
    e?.message?.includes(`Loading CSS chunk`)
  ) {
    forceRefresh()
  }
})
