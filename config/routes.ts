﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  { path: '/', redirect: '/projects' }, // 重定向
  {
    path: '/user',
    layout: false,
    routes: [
      {
        path: '/user/login',
        component: './User/Login'
      }
    ]
  },
  {
    name: 'account.settings',
    path: '/settings',
    resource: 'settings',
    component: '@/pages/user-settings',
    wrappers: ['@/wrappers/auth'],
    hideInMenu: true
  },
  {
    name: 'list.playground',
    icon: 'chart|svg',
    access: 'internalStaff',
    resource: 'inner_workspace',
    path: '/playground',
    routes: [
      {
        path: '/playground',
        wrappers: ['@/wrappers/auth'],
        redirect: '/playground/commend'
      },
      {
        name: 'commend',
        icon: 'chart|svg',
        path: '/playground/commend',
        component: './playground',
        resource: 'inner_comment',
        wrappers: ['@/wrappers/auth'],
        exact: true,
        access: 'internalStaff'
      },
      {
        name: 'viewByBackbone',
        path: '/playground/commend/view-by-backbone/:backboneId',
        exact: true,
        component: '@/pages/route/view-by-backbone',
        hideInMenu: true
      },
      {
        name: 'AISandbox',
        icon: 'chart|svg',
        exact: true,
        resource: 'ai_sandbox',
        wrappers: ['@/wrappers/auth'],
        path: '/playground/ai-sandbox',
        component: '@/pages/AISandbox',
        access: 'internalStaff'
      }
    ]
  },
  {
    name: 'list.experimental-zone',
    icon: 'experimental_zone|svg',
    path: '/experimental-zone',
    resource: 'exp_zone',
    exact: true,
    routes: [
      { path: '/experimental-zone', component: '@/pages/experimental-zone' },
      {
        name: 'search',
        hideInMenu: true,
        path: '/experimental-zone/search',
        component: '@/pages/experimental-zone/search',
        exact: true
      }
    ]
  },
  {
    name: 'list.project-list',
    icon: 'project|svg',
    resource: 'project',
    path: '/projects',
    routes: [
      {
        path: '/projects',
        wrappers: ['@/wrappers/auth'],
        component: '@/pages/projects',
        exact: true,
        hideInMenu: true
      },
      {
        name: 'detail',
        path: '/projects/:id',
        exact: true,
        component: '@/pages/projects/detail',
        hideInMenu: true
      },
      {
        name: 'detail.reaction',
        path: '/projects/:id/reaction/:reactionId',
        hideInMenu: true,
        routes: [
          {
            path: '/projects/:id/reaction/:reactionId',
            component: '@/pages/reaction',
            exact: true,
            hideInMenu: true
          },
          {
            name: 'execute.detail',
            path: '/projects/:id/reaction/:reactionId/experiment-execute/detail/:experimentalNo', // 实验详情
            component: '@/pages/experiment/experiment-execute/detail',
            exact: true,
            hideInMenu: true
          }
        ]
      },
      {
        name: 'detail.experimentalProcedure',
        path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/detail/:experimentDesignId',
        hideInMenu: true,
        routes: [
          {
            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/detail/:experimentDesignId',
            component: '@/pages/experimental-procedure/detail',
            exact: true,
            hideInMenu: true
          }
        ]
      },
      {
        name: 'detail.experiment-conclusion',
        path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo',
        hideInMenu: true,
        routes: [
          {
            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo',
            component: '@/pages/experimental-procedure/conclusion',
            exact: true
          },
          {
            name: 'knowledgeBase',
            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo/knowledgeBase/:experimentDesignId',
            component: '@/pages/experimental-procedure/detail',
            exact: true,
            hideInMenu: true
          },
          {
            name: 'reportDetail',
            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo/reportDetail/:check_no',
            component: '@/pages/experimental-procedure/report-details',
            exact: true,
            hideInMenu: true
          }
        ]
      },
      {
        name: 'quotation',
        exact: true,
        path: '/projects/:id/quotation-records',
        component: '@/pages/projects/quotation-records',
        hideInMenu: true
      },
      {
        name: 'detail.quoteDetail',
        path: '/projects/:id/quotation-records/:quoteMoleculeId/quote-info',
        component: '@/pages/projects/quotation-records/detail',
        exact: true,
        hideInMenu: true
      },
      {
        name: 'detail.addMolecule',
        path: '/projects/:id/add-molecule',
        component: '@/pages/add-molecule',
        exact: true,
        hideInMenu: true
      },
      {
        name: 'detail.compound',
        path: '/projects/:id/compound/:compoundId',
        hideInMenu: true,
        routes: [
          {
            name: 'detail',
            path: '/projects/:id/compound/:compoundId',
            component: '@/pages/compound/refactor',
            exact: true,
            hideInMenu: true
          },
          {
            name: 'detail.create',
            path: '/projects/:id/compound/:compoundId/create',
            hideInMenu: true,
            routes: [
              {
                path: '/projects/:id/compound/:compoundId/create',
                component: './route/create',
                exact: true,
                hideInMenu: true
              }
            ]
          },
          {
            name: 'viewByBackbone',
            path: '/projects/:id/compound/:compoundId/view-by-backbone/:backboneId',
            component: '@/pages/route/view-by-backbone',
            exact: true,
            hideInMenu: true
          },
          {
            name: 'viewByBackbone',
            path: '/projects/:id/compound/:compoundId/view/:routeId',
            component: './route/view',
            hideInMenu: true
          },
          {
            name: 'edit',
            path: '/projects/:id/compound/:compoundId/edit/:routeId',
            component: './route/edit',
            hideInMenu: true
          }
        ]
      }
    ]
  },
  {
    name: 'list.workspace',
    icon: 'home',
    access: 'internalStaff',
    resource: 'home',
    path: '/workspace',
    exact: true,
    routes: [
      { path: '/workspace', wrappers: ['@/wrappers/auth'], redirect: 'home' },
      {
        name: 'myWorkbench',
        path: 'my-workbench',
        component: '@/pages/workspace/my-workbench',
        resource: 'workbench',
        wrappers: ['@/wrappers/auth'],
        exact: true
      },
      {
        name: 'myCompound',
        path: 'my-compound',
        component: '@/pages/workspace/my-compound',
        resource: 'my_compound',
        wrappers: ['@/wrappers/auth'],
        exact: true
      },
      {
        name: 'myReaction',
        path: 'my-reaction',
        component: '@/pages/workspace/my-reaction',
        resource: 'my_reaction',
        wrappers: ['@/wrappers/auth'],
        exact: true
      },
      {
        name: 'myExperiment',
        path: 'my-experiment',
        resource: 'my_experiment',
        wrappers: ['@/wrappers/auth'],
        component: '@/pages/workspace/my-experiment',
        exact: true
      }
    ]
  },
  {
    name: 'list.route',
    hideInMenu: true,
    routes: [
      {
        name: 'viewByBackbone',
        path: '/route/view-by-backbone/:backboneId',
        component: './route/view-by-backbone',
        hideInMenu: true
      },
      {
        name: 'view',
        path: '/route/view/:routeId',
        component: './route/view',
        hideInMenu: true
      }
    ]
  },
  {
    name: 'list.reaction',
    hideInMenu: true,
    routes: [
      {
        name: 'detail',
        path: '/reaction/:reactionId',
        component: './reaction',
        hideInMenu: true
      }
    ]
  },
  {
    name: 'list.experiment',
    path: '/experiment',
    icon: 'execute|svg',
    hideInMenu: true,
    routes: [
      {
        name: 'plan',
        path: '/experiment/experiment-plan',
        component: '@/pages/experiment/experiment-plan',
        exact: true
      },
      {
        name: 'execute',
        path: '/experiment/experiment-execute',
        component: '@/pages/experiment/experiment-execute',
        exact: true
      },
      {
        name: 'execute.detail',
        path: '/experiment/experiment-execute/detail/:experimentalNo', // 实验详情
        component: '@/pages/experiment/experiment-execute/detail',
        exact: true,
        hideInMenu: true
      }
    ]
  },
  {
    name: 'list.procedure',
    icon: 'experimentPlan|svg',
    path: '/experimental-procedure',
    hideInMenu: true,
    exact: true,
    routes: [
      {
        name: 'detail',
        path: '/experimental-procedure',
        component: '@/pages/experimental-procedure',
        exact: true,
        hideInMenu: true
      },
      {
        name: 'detail',
        path: '/experimental-procedure/detail/:experimentDesignId ',
        component: '@/pages/experimental-procedure/detail',
        exact: true,
        hideInMenu: true
      }
    ]
  },
  {
    name: 'list.material-manage',
    icon: 'materialManage|svg',
    path: '/material',
    resource: 'material',
    exact: true,
    routes: [
      {
        path: '/material',
        wrappers: ['@/wrappers/auth'],
        redirect: 'manage'
      },
      {
        name: 'storage',
        path: '/material/manage',
        component: '@/pages/material-manage',
        resource: 'material_lib',
        wrappers: ['@/wrappers/auth'],
        exact: true
      },
      {
        name: 'search-molecule',
        path: '/material/manage/search-molecule',
        component: '@/pages/add-molecule',
        exact: true,
        hideInMenu: true
      },
      {
        name: 'black-list',
        path: '/material/black-list',
        component: '@/pages/material-manage/black-list',
        resource: 'material_blacklist',
        wrappers: ['@/wrappers/auth'],
        exact: true
      },
      {
        name: 'black-list.add',
        path: '/material/black-list/add',
        component: '@/pages/material-manage/black-list/add',
        exact: true,
        hideInMenu: true
      }
    ]
  },
  {
    path: '/message-notification',
    component: '@/pages/message-notification',
    exact: true,
    hideInMenu: true
  },
  {
    name: 'list.batch-retro',
    icon: 'upload',
    path: '/batch-retro',
    component: '@/pages/batch-retro',
    exact: true
  },
  {
    path: '*',
    layout: false,
    component: './404'
  }
]
